import {
  Component,
  type ElementRef,
  Injectable,
  Input,
  type On<PERSON><PERSON>roy,
  type OnInit,
  inject,
  viewChild,
} from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialog, type MatDialogConfig } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { RouterModule } from '@angular/router';
import { EnsureSingleInstance } from '@lib/common/test-root-service';
import { Chart, registerables } from 'chart.js';
import { firstValueFrom } from 'rxjs';

Chart.register(...registerables);

interface ChartDataI {
  heading: string;
  header: any[];
  data: any[][];
}

@Component({
  imports: [
    FormsModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    ReactiveFormsModule,
    RouterModule,
  ],
  selector: 'app-data-chart',
  template: `<div class="w-full h-full flex flex-col">
    <div class="p-2 text-center">{{ heading }}</div>
    <div class="grow"><canvas #chartCanvas class="w-full"></canvas></div>
  </div>`,
})
export class DataChartComponent implements OnInit, OnDestroy {
  readonly chartCanvas = viewChild<ElementRef<HTMLCanvasElement>>('chartCanvas');
  public chart: Chart<any, any, unknown>;
  readonly data: { data: ChartDataI } = inject(MAT_DIALOG_DATA);
  public heading = this.data.data.heading;
  @Input() chartData!: ChartDataI;

  ngOnInit() {
    this.drawChart(this.data.data);
  }

  public drawChart(config: ChartDataI) {
    const labels = config.data.map((d) => d[0]);
    const datasets = config.header.slice(1).map((h, i) => ({
      label: h,
      data: config.data.map((d) => d[i + 1]),
    }));
    this.draw(labels, datasets);
  }

  public draw(labels: string[], datasets: { label: string; data: number[] }[]) {
    this.chart?.destroy();

    this.chart = new Chart(this.chartCanvas().nativeElement, {
      type: 'line',
      data: { labels, datasets },
      options: { scales: { y: { beginAtZero: true } } },
    });
  }

  ngOnDestroy() {
    this.chart?.destroy();
  }
}

@Injectable({ providedIn: 'root' })
export class ChartViewerService {
  readonly #dialog = inject(MatDialog);

  constructor() {
    EnsureSingleInstance(this);
  }

  public open(data: ChartDataI, options: MatDialogConfig<DataChartComponent> = {}) {
    const dialogRef = this.#dialog.open(DataChartComponent, {
      width: '80vw',
      height: '80vh',
      maxHeight: '80vh',
      maxWidth: '80vw',
      data: { data },
      enterAnimationDuration: 0,
      panelClass: ['animate__animated', 'animate__bounceIn'],
      ...options,
    });
    return firstValueFrom(dialogRef.afterClosed());
  }
}
