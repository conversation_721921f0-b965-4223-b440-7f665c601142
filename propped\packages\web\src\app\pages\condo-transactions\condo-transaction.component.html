@if (loading) {
  <div class="flex flex-col items-center justify-center h-screen">
    <h3 class="text-2xl font-bold">Loading....</h3>
    <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
  </div>
} @else {
  <div class="p-3 flex flex-col md:flex-row place-content-start items-center">
    <div class="mr-0 flex flex-row place-content-start items-center">
      <app-svg-icon icon="local:ura-sg" style="color: #ec1b2f" />
      <div class="p-3 text-2xl grow-1">Condo Sales</div>
    </div>
    <div
      class="flex flex-col md:flex-row gap-2 md:ml-auto w-full md:w-auto items-center mt-3 md:mt-0"
    >
      <div class="flex items-center m-1 sm:my-0 px-2 py-1">
        <span class="mr-2 text-base font-semibold">Show Chart</span>
        <label class="relative inline-flex items-center cursor-pointer">
          <input
            type="checkbox"
            class="sr-only peer"
            [checked]="showChartViewer"
            (change)="toggleChartViewer()"
          />
          <div
            class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"
          ></div>
        </label>
      </div>
      <div class="flex items-center m-1 sm:my-0 px-2 py-1">
        <span class="mr-2 text-base font-semibold">Show Map</span>
        <label class="relative inline-flex items-center cursor-pointer">
          <input type="checkbox" class="sr-only peer" [checked]="showMap" (change)="toggleMap()" />
          <div
            class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"
          ></div>
        </label>
      </div>
      <button (click)="openFilterForm()" class="btn btn-primary w-full md:w-auto">Filter</button>
    </div>
  </div>
  <!-- Map Container -->
  @if (showMap) {
    <div class="w-full mb-4">
      <!-- <div class="w-full h-[300px] md:h-[400px] border border-gray-300 rounded-lg overflow-hidden">
        <silver-dom-injector [dom]="mapElement" class="w-full h-full" />
      </div> -->
      <mgl-map
        [style]="style"
        [zoom]="[zoom]"
        [center]="[lng, lat]"
        (mapLoad)="onMapLoad($event)"
        [maxBounds]="[
          [103.6, 1.16],
          [104.1, 1.47],
        ]"
        class="w-full h-[400px] border border-gray-300 rounded-lg overflow-hidden"
      >
        <mgl-geojson-source id="condo-markers" [data]="condoMapMarkers">
          <mgl-layer
            id="condo-layer"
            type="circle"
            source="condo-markers"
            [paint]="{
              'circle-radius': 8,
              'circle-color': ['get', 'color'],
              'circle-stroke-width': 1,
              'circle-stroke-color': '#ffffff',
            }"
            (layerClick)="showPropertyInfo($event)"
          />
        </mgl-geojson-source>
        <mgl-geojson-source id="bookmark-markers" [data]="bookmarkMapMarkers">
          <mgl-layer
            id="bookmark-layer"
            type="circle"
            source="bookmark-markers"
            [paint]="{
              'circle-radius': 8,
              'circle-color': ['get', 'color'],
              'circle-stroke-width': 1,
              'circle-stroke-color': '#ffffff',
            }"
            (layerClick)="showPropertyInfo($event)"
          />
        </mgl-geojson-source>

        @if (showPropertyInformation) {
          <mgl-popup
            [lngLat]="selectedMarkerCoordinates"
            [closeOnClick]="true"
            (close)="showPropertyInformation = false"
            [closeButton]="false"
            className="custom-popup dark:bg-gray-800"
            class="dark:bg-gray-800"
          >
            <div
              class="p-4 max-w-[320px] bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700"
            >
              <!-- Header with close button -->
              <div
                class="flex justify-between items-center mb-3 pb-2 border-b border-gray-200 dark:border-gray-700"
              >
                <h3 class="font-bold text-lg text-gray-800 dark:text-white">
                  {{ selectedProperty?.projectName }}
                </h3>
                <button
                  class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors"
                  (click)="handleClosePropertyInfo()"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </button>
              </div>

              <!-- Property details with icons -->
              <div class="space-y-2 mb-3">
                <div class="flex items-center text-gray-700 dark:text-gray-300">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4 mr-2 text-blue-500 dark:text-blue-400"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  <span class="font-medium">District:</span>
                  <span class="ml-2">{{ selectedProperty?.district }}</span>
                </div>

                <div class="flex items-center text-gray-700 dark:text-gray-300">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4 mr-2 text-blue-500 dark:text-blue-400"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"
                    />
                  </svg>
                  <span class="font-medium">Type:</span>
                  <span class="ml-2">{{ selectedProperty?.propertyType }}</span>
                </div>

                <div class="flex items-center text-gray-700 dark:text-gray-300">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4 mr-2 text-blue-500 dark:text-blue-400"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"
                    />
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  <span class="font-medium">Price:</span>
                  <span class="ml-2 font-semibold text-green-600 dark:text-green-400"
                    >${{ selectedProperty?.price }}</span
                  >
                </div>

                <div class="flex items-center text-gray-700 dark:text-gray-300">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4 mr-2 text-blue-500 dark:text-blue-400"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  <span class="font-medium">PSF:</span>
                  <span class="ml-2 font-semibold text-green-600 dark:text-green-400"
                    >${{ selectedProperty?.psf }}</span
                  >
                </div>
              </div>

              <!-- Address -->
              <div class="text-xs text-gray-500 dark:text-gray-400 mb-3 italic">
                {{ selectedProperty?.address }}
              </div>
            </div>
          </mgl-popup>
        }
      </mgl-map>
    </div>
  }

  <div>
    <div class="overflow-x-auto overflow-y-auto" style="max-height: 70vh">
      <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-600 relative">
        <thead class="sticky top-0 bg-gray-100 dark:bg-gray-700">
          <tr>
            @for (column of columnDefinitions; track column.field) {
              <th
                scope="col"
                class="p-2 text-xs font-medium text-left text-gray-500 uppercase dark:text-gray-400"
                [class.cursor-pointer]="isSortableColumn(column.field)"
                (click)="isSortableColumn(column.field) && sortByColumn(column.field)"
              >
                {{ column.title }}
                @if (isSortableColumn(column.field)) {
                  <span class="ml-1 inline-block">
                    <mat-icon
                      class="text-xs"
                      [class.text-gray-900]="getSortDirection(column.field) === 'asc'"
                    >
                      arrow_upward
                    </mat-icon>
                    <mat-icon
                      class="text-xs"
                      [class.text-gray-900]="getSortDirection(column.field) === 'desc'"
                    >
                      arrow_downward
                    </mat-icon>
                  </span>
                }
              </th>
            }
            <th
              scope="col"
              class="p-2 text-xs font-medium text-left text-gray-500 uppercase dark:text-gray-400"
            >
              Bookmark
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
          @for (row of data; track $index) {
            <tr
              class="hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
              [class.bg-purple-100]="isRowHighlighted(row)"
              [class.dark:bg-purple-700]="isRowHighlighted(row) && isDarkModeEnabled()"
              [attr.data-row-id]="row[0]"
            >
              @for (column of columnDefinitions; track column.field) {
                <td class="p-2 text-xs font-medium whitespace-nowrap" (click)="selectRow(row)">
                  {{ column.transform ? column.transform(row[column.index]) : row[column.index] }}
                </td>
              }
              <td class="p-2 text-xs font-medium whitespace-nowrap">
                <input
                  type="checkbox"
                  class="checkbox"
                  [checked]="isBookmarked(row)"
                  (change)="toggleBookmark(row, $event)"
                />
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  </div>

  <div
    class="sticky bottom-0 right-0 items-center w-full p-4 bg-white border-t border-gray-200 sm:flex sm:justify-between dark:bg-gray-800 dark:border-gray-700"
  >
    <div class="flex items-center mb-4 sm:mb-0">
      <span class="material-symbols-outlined cursor-pointer" (click)="previous()">
        chevron_left
      </span>
      <span class="material-symbols-outlined cursor-pointer" (click)="next()">chevron_right</span>
      <span class="text-sm font-normal text-gray-500 dark:text-gray-400"
        >Showing
        <span class="font-semibold text-gray-900 dark:text-white"
          >{{ meta.start }}-{{ meta.end }}</span
        >
        of
        <span class="font-semibold text-gray-900 dark:text-white">{{ meta.total }}</span></span
      >
    </div>
    <div class="flex items-center space-x-3">
      <a
        (click)="previous()"
        class="inline-flex items-center justify-center flex-1 px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
      >
        <span class="material-symbols-outlined cursor-pointer"> chevron_left </span>
        Previous
      </a>
      <a
        (click)="next()"
        class="inline-flex items-center justify-center flex-1 px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
      >
        Next
        <span class="material-symbols-outlined cursor-pointer">chevron_right</span>
      </a>
    </div>
  </div>

  <div class="w-full flex flex-col sm:flex-row justify-end mt-4">
    <!-- <button
    (click)="toggleChartViewer()"
    class="btn btn-primary mx-2 my-1 sm:my-0"
  >
    {{ showChartViewer ? 'Hide Chart' : 'Show Chart' }}
  </button> -->
  </div>

  @if (showChartViewer && (medianChartData || psfChartData)) {
    <div class="mt-8 p-4 rounded-lg shadow-lg">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        @if (medianChartData) {
          <div>
            <app-stacked-chart [chartData]="medianChartData" />
          </div>
        }
        @if (psfChartData) {
          <div>
            <app-stacked-chart [chartData]="psfChartData" />
          </div>
        }
      </div>
    </div>
  }
  <!-- Bookmarked Selections Table -->
  @if (bookmarkedRows.length > 0) {
    <div class="my-6">
      <div class="flex flex-col sm:flex-row justify-between items-center mb-4">
        <h2 class="text-xl font-semibold mb-2 sm:mb-0">Bookmarked Selections</h2>
        <div class="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
          <button (click)="removeAllBookmarks()" class="btn btn-error w-full sm:w-auto">
            Remove all Bookmarks
          </button>
          <div class="flex items-center m-1 sm:my-0 bg-white px-2 py-1">
            <span class="mr-2 text-base font-semibold">Show Bookmarked Chart</span>
            <label class="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                class="sr-only peer"
                [checked]="showBookmarkedChart"
                (change)="toggleBookmarkedChart()"
              />
              <div
                class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"
              ></div>
            </label>
          </div>
          @if (showMap) {
            <button (click)="toggleBookmarkedOnMap()" class="btn btn-primary w-full sm:w-auto">
              {{ showBookmarkedOnMap ? 'Hide Bookmarked on Map' : 'Show Bookmarked on Map' }}
            </button>
          }
        </div>
      </div>

      <div
        class="overflow-x-auto overflow-y-auto max-h-[400px] scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-100 dark:scrollbar-thumb-gray-600 dark:scrollbar-track-gray-800"
      >
        <table
          class="min-w-full divide-y divide-gray-200 table-fixed dark:divide-gray-600 relative"
        >
          <thead class="sticky top-0 bg-gray-100 dark:bg-gray-700">
            <tr>
              <!-- Transaction Date -->
              <th
                scope="col"
                class="p-2 text-xs font-medium text-left text-gray-500 uppercase dark:text-gray-400"
              >
                Txn date
              </th>
              <!-- Town -->
              <th
                scope="col"
                class="p-2 text-xs font-medium text-left text-gray-500 uppercase dark:text-gray-400"
              >
                Project Name
              </th>
              <!-- Street Name -->
              <th
                scope="col"
                class="p-2 text-xs font-medium text-left text-gray-500 uppercase dark:text-gray-400"
              >
                Street
              </th>
              <!-- District -->
              <th
                scope="col"
                class="p-2 text-xs font-medium text-left text-gray-500 uppercase dark:text-gray-400"
              >
                District
              </th>
              <!-- Floor Level -->
              <th
                scope="col"
                class="p-2 text-xs font-medium text-left text-gray-500 uppercase dark:text-gray-400"
              >
                Floor Level
              </th>
              <!-- Area -->
              <th
                scope="col"
                class="p-2 text-xs font-medium text-left text-gray-500 uppercase dark:text-gray-400"
              >
                Area (sqft)
              </th>
              <!-- Price -->
              <th
                scope="col"
                class="p-2 text-xs font-medium text-left text-gray-500 uppercase dark:text-gray-400"
              >
                Price
              </th>
              <!-- PSF -->
              <th
                scope="col"
                class="p-2 text-xs font-medium text-left text-gray-500 uppercase dark:text-gray-400"
              >
                PSF
              </th>
              <!-- Tenure -->
              <th
                scope="col"
                class="p-2 text-xs font-medium text-left text-gray-500 uppercase dark:text-gray-400"
              >
                Tenure
              </th>
              <!-- Action -->
              <th
                scope="col"
                class="p-2 text-xs font-medium text-left text-gray-500 uppercase dark:text-gray-400"
              >
                Action
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
            @for (row of bookmarkedRows; track $index) {
              <tr
                class="hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                [class.bg-purple-100]="isRowHighlighted(row)"
                [attr.data-row-id]="row[0]"
              >
                <!-- Transaction Date -->
                <td class="p-2 text-xs font-medium whitespace-nowrap">
                  {{ parseToDate(row[1]) }}
                </td>
                <!-- Town -->
                <td class="p-2 text-xs font-medium whitespace-nowrap">
                  {{ row[2] }}
                </td>
                <!-- Street Name -->
                <td class="p-2 text-xs font-medium whitespace-nowrap">
                  {{ row[3] }}
                </td>
                <!-- District -->
                <td class="p-2 text-xs font-medium whitespace-nowrap">
                  {{ row[4] }}
                </td>
                <!-- Floor Level -->
                <td class="p-2 text-xs font-medium whitespace-nowrap">
                  {{ row[5] }}
                </td>
                <!-- Area -->
                <td class="p-2 text-xs font-medium whitespace-nowrap">
                  {{ row[6] }}
                </td>
                <!-- Price -->
                <td class="p-2 text-xs font-medium whitespace-nowrap">${{ row[7] }}</td>
                <!-- PSF -->
                <td class="p-2 text-xs font-medium whitespace-nowrap">${{ row[8] }}</td>
                <!-- Tenure -->
                <td class="p-2 text-xs font-medium whitespace-nowrap">{{ row[9] }}</td>
                <!-- Action -->
                <td class="p-2 text-xs font-medium whitespace-nowrap">
                  <button class="btn btn-sm btn-error" (click)="removeBookmark(row, $event)">
                    Remove
                  </button>
                </td>
              </tr>
            }
          </tbody>
        </table>
      </div>

      <!-- Bookmarked Transactions Chart -->
      @if (showBookmarkedChart && bookmarkedRows.length > 0) {
        <div class="w-full my-6 dark:bg-gray-800 p-4 sm:p-6 rounded-lg shadow-sm">
          <h2 class="text-2xl font-bold mb-4 text-center dark:text-white">
            Bookmarked Transactions Chart
          </h2>
          <div class="flex flex-col md:flex-row gap-4">
            <div class="w-full md:w-1/2">
              <h3 class="text-lg font-medium mb-2 text-center dark:text-white">Price Comparison</h3>
              <app-stacked-chart [chartData]="bookmarkedPriceChartData" />
            </div>
            <div class="w-full md:w-1/2">
              <h3 class="text-lg font-medium mb-2 text-center dark:text-white">PSF Comparison</h3>
              <app-stacked-chart [chartData]="bookmarkedPsfChartData" />
            </div>
          </div>
        </div>
      }
    </div>
  }

  <div class="h-1 bg-gray-700 w-[60%] mx-auto my-6"></div>
  <!-- Transaction Statistics -->
  @if (data.length > 0) {
    <div class="w-full mb-8">
      <h1 class="text-2xl font-bold mb-4 text-center">Search Result Details</h1>
      @if (statsLoading) {
        <div class="flex flex-col md:flex-row gap-8 items-center justify-center">
          <div
            class="animate-pulse flex flex-col md:flex-row gap-8 items-center md:items-start w-full"
          >
            <!-- Transaction Count Skeleton -->
            <div class="flex flex-col items-center">
              <div class="h-8 w-40 bg-gray-200 rounded mb-2"></div>
              <div class="h-16 w-20 bg-gray-200 rounded"></div>
            </div>

            <!-- Price Statistics Skeleton -->
            <div class="flex-1 w-full">
              <div class="grid grid-cols-2 gap-2">
                <div class="h-8 w-40 bg-gray-200 rounded mb-2 ml-auto"></div>
              </div>
              <div class="grid grid-cols-2 gap-2">
                <div class="h-6 w-20 bg-gray-200 rounded ml-auto mb-2"></div>
                <div class="h-6 w-32 bg-gray-200 rounded mb-2"></div>

                <div class="h-6 w-20 bg-gray-200 rounded ml-auto mb-2"></div>
                <div class="h-6 w-32 bg-gray-200 rounded mb-2"></div>

                <div class="h-6 w-20 bg-gray-200 rounded ml-auto"></div>
                <div class="h-6 w-32 bg-gray-200 rounded"></div>
              </div>
            </div>

            <!-- PSF Statistics Skeleton -->
            <div class="flex-1 w-full">
              <div class="grid grid-cols-2 gap-2">
                <div class="h-8 w-40 bg-gray-200 rounded mb-2 ml-auto"></div>
              </div>
              <div class="grid grid-cols-2 gap-2">
                <div class="h-6 w-20 bg-gray-200 rounded ml-auto mb-2"></div>
                <div class="h-6 w-32 bg-gray-200 rounded mb-2"></div>

                <div class="h-6 w-20 bg-gray-200 rounded ml-auto mb-2"></div>
                <div class="h-6 w-32 bg-gray-200 rounded mb-2"></div>

                <div class="h-6 w-20 bg-gray-200 rounded ml-auto"></div>
                <div class="h-6 w-32 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>
        </div>
      } @else {
        <div class="flex flex-col md:flex-row gap-8 items-center md:items-start">
          <!-- Transaction Count -->
          <div class="flex flex-col items-center">
            <h2 class="text-xl font-bold underline mb-2">Transactions</h2>
            <div class="text-6xl font-bold">{{ transactionCount }}</div>
          </div>

          <!-- Price Statistics -->
          <div class="flex-1 w-full">
            <div class="grid grid-cols-2 gap-2">
              <h2 class="text-xl text-right font-bold underline mb-2">Price</h2>
            </div>

            <div class="grid grid-cols-2 gap-2">
              <div class="text-right text-purple-700 font-bold">Highest</div>
              <div class="font-bold">${{ priceStats.highest.toLocaleString() }}</div>

              <div class="text-right font-bold">Median</div>
              <div class="font-bold">${{ priceStats.median.toLocaleString() }}</div>

              <div class="text-right text-red-600 font-bold">Lowest</div>
              <div class="font-bold">${{ priceStats.lowest.toLocaleString() }}</div>
            </div>
          </div>

          <!-- PSF Statistics -->
          <div class="flex-1 w-full">
            <div class="grid grid-cols-2 gap-2">
              <h2 class="text-xl text-right font-bold underline mb-2">PSF</h2>
            </div>
            <div class="grid grid-cols-2 gap-2">
              <div class="text-right text-purple-700 font-bold">Highest</div>
              <div class="font-bold">${{ psfStats.highest.toLocaleString() }}</div>

              <div class="text-right font-bold">Median</div>
              <div class="font-bold">${{ psfStats.median.toLocaleString() }}</div>

              <div class="text-right text-red-600 font-bold">Lowest</div>
              <div class="font-bold">${{ psfStats.lowest.toLocaleString() }}</div>
            </div>
          </div>
        </div>
      }
    </div>
  }

  @if (data.length > 0) {
    <div class="w-full mb-8 bg-gray-50 dark:bg-gray-800 p-4 sm:p-6 rounded-lg shadow-sm">
      <h1 class="text-2xl text-center font-bold mb-4 dark:text-white">Price Forecast Calculator</h1>

      <div class="flex flex-col md:flex-row gap-4 mb-6">
        <!-- Input Section -->
        <div
          class="flex flex-col md:flex-row mx-auto items-center gap-4 p-4 bg-white dark:bg-gray-700 rounded-lg shadow-sm"
        >
          <div class="text-center md:text-right">
            <div class="font-semibold mb-1 dark:text-white">Input Forecast</div>
            <div class="text-sm text-gray-600 dark:text-gray-300">Annualized Capital Growth</div>
            <div class="text-xs text-gray-500 dark:text-gray-400">(from previous transaction)</div>
          </div>

          <div class="flex flex-wrap items-center justify-center">
            <input
              type="number"
              value="6"
              class="w-16 h-10 text-center border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white rounded-md"
              min="0"
              [formControl]="growthPercentage"
              max="100"
            />
            <span class="mx-2 text-lg dark:text-white">%</span>

            <span class="mx-2 dark:text-white">in</span>

            <input
              type="number"
              value="3"
              class="w-16 h-10 text-center border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white rounded-md"
              min="1"
              max="50"
              [formControl]="growthYears"
            />
            <span class="mx-2 text-lg dark:text-white">Years</span>
          </div>
        </div>
      </div>

      <div class="text-center mb-6">
        <div class="font-semibold dark:text-white">
          Your Forecast Projection Based on {{ growthPercentage.value }}% Growth and
          {{ growthYears.value }} Years
        </div>
        <div class="text-sm text-gray-600 dark:text-gray-300">From the Previous Transaction</div>
      </div>

      <!-- Forecast Results Table -->
      <div class="overflow-x-auto">
        <table
          class="min-w-full bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg"
        >
          <thead>
            <tr class="bg-gray-100 dark:bg-gray-800">
              <th class="py-3 px-6 text-left dark:text-white"></th>
              <th class="py-3 px-6 text-center font-bold text-xl dark:text-white">Price</th>
              <th class="py-3 px-6 text-center font-bold text-xl dark:text-white">PSF</th>
            </tr>
          </thead>
          <tbody>
            @if (growthPercentage.value > 0 && growthYears.value > 0) {
              <tr class="border-b border-gray-200 dark:border-gray-600">
                <td class="py-3 px-6 text-left font-medium dark:text-white">
                  All Transactions in Search Criteria
                </td>
                <td class="py-3 px-6 text-center font-bold dark:text-white">
                  ${{ forecastPrice.toLocaleString() }}
                </td>
                <td class="py-3 px-6 text-center font-bold dark:text-white">
                  ${{ forecastPSF.toLocaleString() }}
                </td>
              </tr>
            }
          </tbody>
        </table>
      </div>

      <div class="mt-4 text-sm text-gray-500 dark:text-gray-400 text-center">
        Note: Forecasts are based on median values and are for estimation purposes only.
      </div>
    </div>
  }
}
