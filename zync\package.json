{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon src/app/server.js", "dev": "nodemon src/app/server.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^5.1.1", "body-parser": "^2.2.0", "crypto": "^1.0.1", "dotenv": "^16.5.0", "ejs": "^3.1.10", "express": "^5.1.0", "install": "^0.13.0", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.0", "nodemailer": "^6.10.1", "nodemon": "^3.1.9", "npm": "^11.3.0", "qrcode": "^1.5.4", "speakeasy": "^2.0.0"}}