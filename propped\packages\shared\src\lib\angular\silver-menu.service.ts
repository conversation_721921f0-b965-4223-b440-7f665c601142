import { CommonModule } from '@angular/common';
import {
  type AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  HostListener,
  Injectable,
  type OnDestroy,
  type OnInit,
  inject,
  input,
  signal,
} from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { prevent } from '@lib/angular/fun';
import { SVGIconComponent } from '@lib/angular/svg-icon.component';
import { wait } from '@lib/common/fun';
import { daisyMerge } from '@lib/common/tailwind-merge';
import { type Subscription, fromEvent, merge, take } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class SilverMenuService {
  public isOpen = signal(false);
  public position = signal([0, 0] as [number, number]);
  public menu = signal([]);
  public defer: PromiseWithResolvers<any>;

  async open($event: MouseEvent, menu: any[]) {
    this.defer?.resolve(undefined);
    this.defer = undefined;
    this.isOpen.set(false);
    this.position.set([0, 0]);
    await wait(10);
    this.isOpen.set(true);
    this.menu.set(menu);
    this.positionMenu($event);
    this.defer = Promise.withResolvers();
    const v = await this.defer.promise;
    this.menu.set([]);
    this.defer = undefined;
    this.isOpen.set(false);
    this.position.set([0, 0]);
    return v;
  }

  positionMenu($event: MouseEvent) {
    const x = $event.clientX;
    const y = $event.clientY;
    const h = 36 * this.menu().length + 16;
    this.position.set([
      x + 56 * 4 > window.innerWidth ? x - 56 * 4 : x,
      y + h > window.innerHeight ? y - h : y,
    ]);
  }
}

@Component({
  selector: 'app-internal-silver-menu',
  imports: [MatIconModule, SVGIconComponent, CommonModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <ng-template
      [ngTemplateOutlet]="menuTemplate"
      [ngTemplateOutletContext]="{ $implicit: menu() }"
    />
    <ng-template #menuTemplate let-menu>
      @let root = isRoot(menu);
      <ul
        class="absolute menu bg-base-100 rounded-box w-56 flex-nowrap shadow-lg border border-base-content/20"
        [class.__root-menu]="root"
        [class]="menu.innerClass || '' + (root ? '' : 'submenu left-[100%] right-auto')"
        style="margin-inline-start: 0; margin-inline-end: 0;"
        [style.left.px]="root ? position()[0] : null"
        [style.top.px]="root ? position()[1] : null"
      >
        @for (item of menu; track $index) {
          <li
            [class]="item.innerClass || ''"
            [class.has-submenu]="item.submenu"
            [disabled]="item.disabled ?? true"
          >
            <a [class]="item.cssClass || ''" (click)="select(item)">
              @if (item.icon) {
                <mat-icon
                  [class]="
                    daisyMerge(
                      '!w-5 !h-5 text-[20px] p-0 !overflow-visible rounded-full',
                      item.iconClass || ''
                    )
                  "
                  >{{ item.icon }}</mat-icon
                >
              } @else if (item.url) {
                <app-svg-icon
                  [class]="
                    daisyMerge(
                      'w-5 h-5 text-[20px] p-0 !overflow-visible rounded-full',
                      item.iconClass || ''
                    )
                  "
                  [icon]="item.url"
                />
              }
              {{ item.name }}
              @if (item.submenu) {
                <mat-icon class="!w-4 !h-4 text-[16px]">keyboard_arrow_right</mat-icon>
              }
            </a>
            @if (item.submenu) {
              <ng-template
                [ngTemplateOutlet]="menuTemplate"
                [ngTemplateOutletContext]="{ $implicit: item.submenu }"
              />
            }
          </li>
        }
      </ul>
    </ng-template>
  `,
  styles: `
    .menu {
      z-index: 10;
    }

    @keyframes showRootMenu {
      0% {
        opacity: 0;
        visibility: hidden;
      }
      100% {
        opacity: 1;
        visibility: visible;
      }
    }

    .__root-menu {
      animation: showRootMenu 0.3s ease-in-out forwards;
    }

    .has-submenu .menu {
      visibility: hidden;
      opacity: 0;
      transform: scale(0.95);
      transition:
        visibility 0s linear 0.6s,
        opacity 0.2s ease-in-out 0.6s,
        transform 0.2s ease-in-out 0.6s;
    }

    .has-submenu:hover > .menu {
      visibility: visible;
      opacity: 1;
      transform: scale(1);
      transition:
        visibility 0s linear 0s,
        opacity 0.2s ease-in-out,
        transform 0.2s ease-in-out;
    }

    .menu:has(.has-submenu:hover) .has-submenu:not(:hover) > .menu {
      visibility: hidden;
      opacity: 0;
      transform: scale(0.95);
      transition:
        visibility 0s linear 0.3s,
        opacity 0.2s ease-in-out,
        transform 0.2s ease-in-out;
    }
  `,
})
export class InternalGoldMenuComponent implements AfterViewInit, OnInit, OnDestroy {
  readonly menu = input<any[]>([]);
  readonly position = input([0, 0]);

  readonly goldMenuService = inject(SilverMenuService);
  private el = inject(ElementRef);
  public daisyMerge = daisyMerge;

  #subscriptions: Record<string, Subscription> = {};

  async ngOnInit(): Promise<void> {
    const sub = fromEvent(this.el.nativeElement, 'contextmenu')
      .pipe(take(1))
      .subscribe((v: MouseEvent) => prevent(v));
    await wait(10);
    sub.unsubscribe();
    this.#subscriptions['xyz'] = merge(
      fromEvent(this.el.nativeElement, 'contextmenu'),
      fromEvent(window, 'click'),
      fromEvent(window, 'contextmenu'),
    ).subscribe((_event: MouseEvent) => {
      this.goldMenuService.defer?.resolve(undefined);
    });
  }

  isRoot(item: any): boolean {
    return this.menu() === item;
  }

  ngAfterViewInit(): void {
    this.adjustSubmenuPositions();
  }

  @HostListener('window:resize')
  onResize() {
    this.adjustSubmenuPositions();
  }

  @HostListener('click', ['$event'])
  clickOutside(event: MouseEvent) {
    if (event.target === this.el.nativeElement) {
      this.goldMenuService.defer?.resolve(undefined);
    }
  }

  select(item: any) {
    this.goldMenuService.defer?.resolve(item);
  }

  adjustSubmenuPositions(): void {
    const submenus = this.el.nativeElement.querySelectorAll('.submenu');
    for (const submenu of submenus) {
      const box = submenu.getBoundingClientRect();
      const pBox = submenu.parentElement.parentElement.getBoundingClientRect();
      const makeRight = box.right > window.innerWidth;
      const { style } = submenu;
      style.position = 'fixed';
      style.left = makeRight ? `${pBox.left - pBox.width}px` : `${pBox.left + pBox.width}px`;
      if (box.height > window.innerHeight) {
        style.top = 0;
        style.height = `${window.innerHeight}px`;
        style.overflowY = 'auto';
      } else if (window.innerHeight < box.bottom) {
        style.bottom = 0;
      }
    }
  }

  ngOnDestroy(): void {
    for (const key in this.#subscriptions) {
      this.#subscriptions[key].unsubscribe();
    }
  }
}

@Component({
  selector: 'app-silver-menu',
  imports: [InternalGoldMenuComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    @if (silverMenuService.isOpen()) {
      <app-internal-silver-menu
        [menu]="silverMenuService.menu()"
        [position]="silverMenuService.position()"
      />
    }
  `,
})
export class SilverMenuComponent {
  readonly silverMenuService = inject(SilverMenuService);
}
