import * as OTPAuth from 'otpauth';
import QRCode from 'qrcode';
import { environment } from '../../../environments/environment';

const generateQR = (text: string) =>
  QRCode.toDataURL(text, { color: { light: '#00000000', dark: '#F9AE33' } });

export const generateOptions = (username: string, secret: string) => {
  return {
    issuer: environment.appName,
    label: username,
    algorithm: 'SHA1',
    digits: 6,
    period: 30,
    secret,
  };
};

export const generateSecret = async (username: string) => {
  const secret = new OTPAuth.Secret({ size: 20 }).base32;
  const url = new OTPAuth.TOTP(generateOptions(username, secret)).toString();
  return { secret, url, qr: await generateQR(url) };
};

const cache: any = {};
export const generateToken = (secret: string) => {
  if (!cache[secret]) cache[secret] = new OTPAuth.TOTP(generateOptions('username', secret));
  return cache[secret];
};
