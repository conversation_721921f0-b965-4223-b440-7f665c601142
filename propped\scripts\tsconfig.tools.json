{
  "extends": "./tsconfig.json",
  "compilerOptions": {
    "outDir": "../dist/out-tsc/tools",
    // "rootDir": ".",
    "module": "None",
    "lib": ["ESNext", "DOM"],
    "moduleResolution": "bundler",
    "target": "ESNext",
    "types": ["node"],
    "strict": false,
    "noImplicitOverride": false,
    "noPropertyAccessFromIndexSignature": false,
    "noImplicitReturns": false,
    "esModuleInterop": true,
    "noFallthroughCasesInSwitch": false
    // "importHelpers": false
  },
  // "files": ["tim/main.ts"]
  "include": ["**/*.ts"]
}
