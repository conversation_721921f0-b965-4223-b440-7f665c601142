const nodemailer = require('nodemailer');

// Create a transporter object using SMTP transport
const transporter = nodemailer.createTransport({
  service: 'gmail', // Use Gmail as the email service
  auth: {
    user: process.env.SMTP_EMAIL_USER, // Your Gmail email address
    pass: process.env.SMTP_EMAIL_PASS, // Your Gmail password or app-specific password
  },
});

/**
 * Send an email with OTP for registration verification 
 * @param {string} to - Recipient email address
 * @param {string} otp - One-time password
 * @returns {Promise} - Resolves with info about the sent email
 */
const sendRegistrationOTP = async (to, otp) => {
  const mailOptions = {
    from: process.env.SMTP_EMAIL_USER,
    to,
    subject: 'Email Verification OTP',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <h2 style="color: #333;">Verify Your Email</h2>
        <p>Thank you for registering! Please use the following OTP to verify your email address:</p>
        <div style="background-color: #f5f5f5; padding: 10px; text-align: center; font-size: 24px; font-weight: bold; letter-spacing: 5px; margin: 20px 0;">
          ${otp}
        </div>
        <p>This OTP will expire in 10 minutes.</p>
        <p>If you didn't request this verification, please ignore this email.</p>
        <p style="margin-top: 20px; font-size: 12px; color: #777;">
          This is an automated message, please do not reply.
        </p>
      </div>
    `,
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    console.log('Registration email sent:', info.messageId);
    return info;
  } catch (error) {
    console.error('Error sending registration email:', error);
    throw error;
  }
};

/**
 * Send an email with OTP for password reset
 * @param {string} to - Recipient email address
 * @param {string} otp - One-time password
 * @returns {Promise} - Resolves with info about the sent email
 */
const sendPasswordResetOTP = async (to, otp) => {
  const mailOptions = {
    from: process.env.SMTP_EMAIL_USER,
    to,
    subject: 'Password Reset OTP',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <h2 style="color: #333;">Reset Your Password</h2>
        <p>You requested to reset your password. Please use the following OTP to proceed:</p>
        <div style="background-color: #f5f5f5; padding: 10px; text-align: center; font-size: 24px; font-weight: bold; letter-spacing: 5px; margin: 20px 0;">
          ${otp}
        </div>
        <p>This OTP will expire in 10 minutes.</p>
        <p>If you didn't request a password reset, please ignore this email and ensure your account is secure.</p>
        <p style="margin-top: 20px; font-size: 12px; color: #777;">
          This is an automated message, please do not reply.
        </p>
      </div>
    `,
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    console.log('Password reset email sent:', info.messageId);
    return info;
  } catch (error) {
    console.error('Error sending password reset email:', error);
    throw error;
  }
};

/**
 * Send an email with OTP for email activation
 * @param {string} to - Recipient email address
 * @param {string} otp - One-time password
 * @returns {Promise} - Resolves with info about the sent email
 */
const sendEmailActivationOTP = async (to, otp) => {
  const mailOptions = {
    from: process.env.SMTP_EMAIL_USER,
    to,
    subject: 'Email Activation OTP',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <h2 style="color: #333;">Activate Your Email</h2>
        <p>You requested to activate your email. Please use the following OTP to proceed:</p>
        <div style="background-color: #f5f5f5; padding: 10px; text-align: center; font-size: 24px; font-weight: bold; letter-spacing: 5px; margin: 20px 0;">
          ${otp}
        </div>
        <p>This OTP will expire in 10 minutes.</p>
        <p>If you didn't request this activation, please ignore this email.</p>
        <p style="margin-top: 20px; font-size: 12px; color: #777;">
          This is an automated message, please do not reply.
        </p>
      </div>
    `,
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    console.log('Email activation sent:', info.messageId);
    return info;
  } catch (error) {
    console.error('Error sending email activation:', error);
    throw error;
  }
};

/**
 * Send an email with OTP for 2FA verification
 * @param {string} to - Recipient email address
 * @param {string} otp - One-time password
 * @returns {Promise} - Resolves with info about the sent email
 */
const send2FAOTP = async (to, otp) => {
  const mailOptions = {
    from: process.env.SMTP_EMAIL_USER,
    to,
    subject: '2FA Verification Code',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <h2 style="color: #333;">Two-Factor Authentication</h2>
        <p>You are attempting to log in to your account. Please use the following verification code:</p>
        <div style="background-color: #f5f5f5; padding: 10px; text-align: center; font-size: 24px; font-weight: bold; letter-spacing: 5px; margin: 20px 0;">
          ${otp}
        </div>
        <p>This code will expire in 10 minutes.</p>
        <p>If you didn't attempt to log in, please secure your account immediately.</p>
        <p style="margin-top: 20px; font-size: 12px; color: #777;">
          This is an automated message, please do not reply.
        </p>
      </div>
    `,
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    console.log('2FA email sent:', info.messageId);
    return info;
  } catch (error) {
    console.error('Error sending 2FA email:', error);
    throw error;
  }
};

/**
 * Send TOTP setup confirmation email
 * @param {string} to - Recipient email address
 * @param {string} appName - Application name
 * @returns {Promise} - Resolves with info about the sent email
 */
const sendTOTPSetupConfirmation = async (to, appName = 'Zync App') => {
  const mailOptions = {
    from: process.env.SMTP_EMAIL_USER,
    to,
    subject: 'TOTP Authentication Enabled',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <h2 style="color: #333;">TOTP Authentication Enabled</h2>
        <p>Two-Factor Authentication using TOTP (Time-based One-Time Password) has been successfully enabled for your ${appName} account.</p>
        <div style="background-color: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <h3 style="color: #2e7d32; margin-top: 0;">Security Enhancement Active</h3>
          <p style="margin-bottom: 0;">Your account is now protected with an additional layer of security. You'll need your authenticator app to log in.</p>
        </div>
        <p><strong>Important:</strong> Make sure to keep your authenticator app accessible. If you lose access, you can still use email OTP as a backup.</p>
        <p>If you didn't enable this feature, please contact our support team immediately.</p>
        <p style="margin-top: 20px; font-size: 12px; color: #777;">
          This is an automated message, please do not reply.
        </p>
      </div>
    `,
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    console.log('TOTP setup confirmation email sent:', info.messageId);
    return info;
  } catch (error) {
    console.error('Error sending TOTP setup confirmation email:', error);
    throw error;
  }
};

module.exports = {
  sendRegistrationOTP,
  sendPasswordResetOTP,
  sendEmailActivationOTP,
  send2FAOTP,
  sendTOTPSetupConfirmation,
};
