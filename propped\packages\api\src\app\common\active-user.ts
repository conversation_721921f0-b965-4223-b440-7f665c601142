import { ROLES_BY_ID } from '@lib/common/const/roles';
import { hasAny } from '@lib/common/fun';

export interface Role {
  id: number;
  name: string;
}

export class ActiveUser {
  public readonly createdAt: Date;
  public readonly email: string;
  public readonly pic: number;
  public readonly id: number;
  public readonly name: string;
  public readonly roleIds: number[];
  public readonly roles: Role[];
  public readonly updatedAt: Date;
  public readonly username: string;
  public readonly isActive: boolean;
  public readonly meta: any;
  public readonly secret: any;
  public readonly password: string;

  constructor(data: any = {}) {
    this.id = data.id;
    this.name = data.name;
    this.email = data.email;
    this.username = data.username;
    this.createdAt = data.createdAt;
    this.updatedAt = data.updatedAt;
    this.isActive = data.isActive;
    this.meta = data.meta;
    this.secret = data.secret;
    this.password = data.password;
    this.pic = data.pic;
    this.roleIds = data.roleIds;
    this.roles = this.roleIds.map((v) => ({ id: v, name: ROLES_BY_ID[v] }));
  }

  public hasRole(id: number) {
    return this.roleIds.includes(id);
  }

  public hasAnyRole(ids: number[]) {
    return hasAny(this.roleIds, ids);
  }

  public toJSON() {
    return {
      id: this.id,
      name: this.name,
      email: this.email,
      username: this.username,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      isActive: this.isActive,
      meta: this.meta,
      pic: this.pic,
      roles: this.roles,
    };
  }
}
