<div class="overflow-x-hidden flex flex-col h-full relative">
  <div class="absolute top-4 left-0 right-0 sm:left-4 sm:right-auto z-20 px-4 sm:px-0">
    <div class="relative flex">
      <!-- Search bar -->
      <div class="relative m-2 w-full max-w-full sm:max-w-md md:max-w-lg">
        <div class="flex flex-col gap-2 md:flex-row md:items-center w-full sm:w-auto">
          <div
            class="flex items-center shadow-lg rounded-full overflow-hidden border border-blue-100 dark:border-gray-700 bg-white dark:bg-gray-800 w-full sm:w-auto"
          >
            <div class="flex items-center pl-4">
              <mat-icon class="text-blue-500 dark:text-blue-400">location_on</mat-icon>
            </div>
            <input
              type="text"
              class="px-3 py-3 w-full sm:w-64 md:w-80 focus:outline-none bg-transparent dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
              placeholder="Enter address, car park, or landmark"
              [(ngModel)]="searchQuery"
              (input)="onSearchInput()"
            />
            <button
              class="px-4 py-3 bg-blue-600 text-white hover:bg-blue-700 transition-colors flex items-center justify-center"
              (click)="onSearchInput()"
              aria-label="Search"
            >
              <mat-icon>search</mat-icon>
            </button>
          </div>

          <!-- Current location button next to search bar -->
          <button
            class="p-2 text-blue-500 bg-white rounded-full flex items-center justify-center w-10 h-10 dark:bg-primary-content"
            (click)="showCurrentLocation()"
            title="My Location"
          >
            <mat-icon>my_location</mat-icon>
          </button>
        </div>

        @if (
          (searchResults.length > 0 || (recentSearches.length > 0 && searchQuery)) && showDropdown
        ) {
          <div
            class="absolute top-full left-0 w-full mt-1 bg-white rounded-md shadow-lg z-[999] max-h-60 overflow-y-auto"
            style="min-width: 250px"
          >
            @if (searchResults.length > 0) {
              <div class="p-2 bg-gray-50 border-b border-gray-200">
                <span class="text-sm font-medium text-gray-700">Search Results</span>
              </div>
              @for (result of searchResults; track result) {
                <div
                  class="px-3 py-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100"
                  (click)="selectSearchResult(result)"
                >
                  <div class="font-medium">{{ result.BUILDING || result.ROAD_NAME }}</div>
                  <div class="text-sm text-gray-600 truncate">{{ result.ADDRESS }}</div>
                </div>
              }
            }
            @if (recentSearches.length > 0 && searchQuery) {
              <div class="p-2 bg-gray-50 border-b border-gray-200">
                <span class="text-sm font-medium text-gray-700">Recent Searches</span>
              </div>
              @for (searchResult of recentSearches; track searchResult) {
                <div
                  class="px-3 py-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100 flex justify-between items-center"
                  (click)="selectSearchResult(searchResult)"
                >
                  <div class="flex items-center">
                    <i class="material-icons text-gray-500 mr-2 text-sm">schedule</i>
                    <div>
                      <div class="font-medium">
                        {{ searchResult.BUILDING || searchResult.ROAD_NAME }}
                      </div>
                      <div class="text-sm text-gray-600 truncate">{{ searchResult.ADDRESS }}</div>
                    </div>
                  </div>
                  <button
                    class="text-gray-500 hover:text-red-500 p-1 rounded-full hover:bg-gray-200"
                    (click)="removeRecentSearch(searchResult, $event)"
                  >
                    <i class="material-icons text-sm">close</i>
                  </button>
                </div>
              }
            }
          </div>
        }
      </div>
    </div>
  </div>

  <div class="absolute top-20 right-2 z-20 md:top-4 md:right-4">
    <div class="relative flex">
      <!-- <button
        class="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 transition-colors flex items-center shadow-md school-query-button"
        (click)="toggleSchoolDropdown()"
      >
        <mat-icon>school</mat-icon>
        <span class="ml-2">School Query</span>
      </button> -->

      @if (userLocationMarker) {
        <div class="flex items-center ml-2">
          <button
            class="px-3 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors flex items-center shadow-md ml-2"
            (click)="clearSearch()"
          >
            <mat-icon>clear</mat-icon>
          </button>
        </div>
      }
      @if (setSearchResultMarker) {
        <div class="flex items-center ml-2">
          <button
            class="px-3 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors flex items-center shadow-md ml-2"
            (click)="clearSearchResultMarker()"
          >
            <mat-icon>clear</mat-icon>
          </button>
        </div>
      }

      @if (schoolMarker) {
        <button
          class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors flex items-center shadow-md ml-2"
          (click)="clearSchoolMarkers()"
        >
          <mat-icon>clear</mat-icon>
          <span class="ml-2">Clear</span>
        </button>
      }
    </div>
    <!-- User location info window button -->
  </div>

  <!-- Property Information Table - Positioned at the top right corner of the screen -->
  @if (showPropertyTable && hasTransactions) {
    <div
      class="fixed top-40 right-24 bg-white rounded-lg shadow-lg z-10 border border-gray-200 max-w-sm md:max-w-md"
    >
      <button
        class="text-gray-400 hover:text-gray-700 absolute top-1 right-1"
        (click)="showPropertyTable = false"
      >
        <mat-icon>close</mat-icon>
      </button>
      <div class="overflow-x-auto max-h-80 mt-8">
        <table class="w-full text-left border-collapse text-xs bg-blue-900 text-white">
          <thead class="e">
            <tr class="border border-white">
              <th class="px-2 py-1 font-bold whitespace-nowrap border border-white">Date</th>
              <th class="px-2 py-1 font-bold whitespace-nowrap border border-white">Type</th>
              <th class="px-2 py-1 font-bold whitespace-nowrap border border-white">Floor Level</th>
              <th class="px-2 py-1 font-bold whitespace-nowrap border border-white">Area (SQFT)</th>
              <th class="px-2 py-1 font-bold whitespace-nowrap border border-white">Price (S$)</th>
              <th class="px-2 py-1 font-bold whitespace-nowrap border border-white">PSF (S$)</th>
            </tr>
          </thead>
          <tbody>
            @for (transaction of transactionData; track $index) {
              <tr class="text-white">
                <td class="px-2 py-1 whitespace-nowrap">{{ transaction.date }}</td>
                <td class="px-2 py-1 whitespace-nowrap">{{ transaction.flatType }}</td>
                <td class="px-2 py-1 whitespace-nowrap">{{ transaction.floorLevel }}</td>
                <td class="px-2 py-1 whitespace-nowrap">{{ transaction.area }}</td>
                <td class="px-2 py-1 whitespace-nowrap">${{ transaction.price }}</td>
                <td class="px-2 py-1 whitespace-nowrap">${{ transaction.psf }}</td>
              </tr>
            }
          </tbody>
        </table>
      </div>
    </div>
  }

  <div class="flex-grow p-4 overflow-auto relative">
    @if ((!showHDBListings && !showHDBData && !showCondoListings && !showCondoData) || !activeTab) {
      <div class="h-full flex flex-col items-center justify-center">
        <silver-dom-injector [dom]="mapElement" class="w-full h-full rounded-lg shadow-md" />

        @if (isLoadingNearbyCondos) {
          <div
            class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white bg-opacity-80 p-4 rounded-lg shadow-md z-10"
          >
            <div class="flex flex-col items-center">
              <div
                class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"
              ></div>
              <p class="mt-2 font-medium">Finding nearby properties...</p>
            </div>
          </div>
        }

        @if (isLoadingHawkerCenters) {
          <div
            class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white bg-opacity-80 p-4 rounded-lg shadow-md z-10"
          >
            <div class="flex flex-col items-center">
              <div
                class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"
              ></div>
              <p class="mt-2 font-medium">Finding nearby hawker centers...</p>
            </div>
          </div>
        }

        @if (isLoadingBusStops) {
          <div
            class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white bg-opacity-80 p-4 rounded-lg shadow-md z-10"
          >
            <div class="flex flex-col items-center">
              <div
                class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"
              ></div>
              <p class="mt-2 font-medium">Finding nearby bus stops...</p>
            </div>
          </div>
        }
      </div>
    } @else if (showHDBListings || showCondoListings) {
      <div class="h-full grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="md:col-span-2 h-96 md:h-full">
          <silver-dom-injector [dom]="mapElement" class="w-full h-full rounded-lg shadow-md" />
        </div>
        <div class="md:col-span-1 overflow-auto h-96 md:h-full">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-bold">
              {{ activeTab === 'HDB' ? 'HDB Sale Listings' : 'Condo Sale Listings' }}
              <span class="text-sm text-gray-600 ml-2">
                ({{ activeTab === 'HDB' ? filteredPropertyData.length : condoData.length }}
                properties)
              </span>
            </h2>
          </div>
          <button
            (click)="openFilterForm()"
            class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors flex items-center"
          >
            <mat-icon>filter_list</mat-icon>
            <span class="ml-2">Filter</span>
          </button>
          <div class="space-y-4">
            @for (
              property of activeTab === 'HDB' ? propertyData : condoData;
              track property.blockNo;
              let i = $index
            ) {
              <div
                class="p-4 rounded-lg shadow-md hover:shadow-lg transition-shadow cursor-pointer"
                [ngClass]="{ 'bg-blue-300': i === 0 }"
                (click)="centerMapOnProperty(property)"
              >
                <h3 class="text-lg font-semibold">
                  {{ activeTab === 'HDB' ? 'Block ' : '' }}{{ property.blockNo }}
                </h3>
                <p>{{ property.street }}, {{ property.town }}</p>
                <p>Postal Code: {{ property.postalCode }}</p>
                <p class="text-green-600 font-bold">S$ {{ formatPrice(property.salesPrice) }}</p>
              </div>
            }
          </div>
        </div>
      </div>
    } @else {
      <div class="h-full grid grid-cols-1 md:grid-cols-3 gap-4">
        @if (isLoading) {
          <div class="col-span-3 flex justify-center items-center h-full">
            <div
              class="animate-spin rounded-full h-32 w-32 border-t-2 border-b-2 border-blue-500"
            ></div>
          </div>
        } @else {
          <div class="md:col-span-3 h-96 md:h-full">
            <silver-dom-injector [dom]="mapElement" class="w-full h-full rounded-lg shadow-md" />
          </div>
          <!-- <div class="md:col-span-1 overflow-auto h-96 md:h-full">
            <h2 class="text-xl font-bold mb-4">
              @if (activeTab === 'HDB') {
                HDB Data ({{ hdbData.length }} records)
              } @else {
                Condo Data ({{ apiCondoData.length }} records)
              }
            </h2>
            <div class="space-y-4">
              @if (activeTab === 'HDB') {
                @for (hdb of hdbData; track hdb.blockNumber; let i = $index) {
                  <div
                    class="p-4 rounded-lg shadow-md hover:shadow-lg transition-shadow cursor-pointer relative"
                    [ngClass]="{ 'bg-blue-100': i === 0 }"
                  >
                    <div class="absolute top-2 right-2">
                      <button class="btn btn-circle" (click)="togglePropertyMenu($event, hdb)">
                        <mat-icon>more_vert</mat-icon>
                      </button>
                    </div>
                    <div (click)="centerMapOnHDB(hdb)">
                      <h3 class="text-lg font-semibold">
                        {{ hdb.title || 'Block ' + hdb.blockNumber }}
                      </h3>
                      <p>{{ hdb.hdbTown || 'Unknown Town' }}</p>
                      <p>Postal Code: {{ hdb.postalCode || 'N/A' }}</p>
                      <p>Project: {{ hdb.projectName || 'N/A' }}</p>
                    </div>
                  </div>
                }
              } @else {
                @for (condo of apiCondoData; track condo._id) {
                  <div
                    class="p-4 rounded-lg shadow-md hover:shadow-lg transition-shadow cursor-pointer relative"
                  >
                    <div class="absolute top-2 right-2">
                      <div class="relative">
                        <button class="btn btn-circle" (click)="togglePropertyMenu($event, condo)">
                          <mat-icon>more_vert</mat-icon>
                        </button>
                      </div>
                    </div>
                    <div (click)="centerMapOnCondo(condo)">
                      <h3 class="text-lg font-semibold">
                        {{ condo.title || condo.projectName || 'Unnamed Condo' }}
                      </h3>
                      <p>Address: {{ condo.address || 'N/A' }}</p>
                      <p>Total Units: {{ condo.totalUnits || 'N/A' }}</p>
                    </div>
                  </div>
                }
              }
            </div>
          </div> -->
        }
      </div>
    }
  </div>

  <div class="flex space-x-4 p-4 border-b">
    <button
      [class]="
        daisyMerge(
          'px-4 py-2 rounded-md transition-colors flex items-center shadow-md',
          activeTab === 'HDB'
            ? 'bg-blue-600 text-white hover:bg-blue-700'
            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
        )
      "
      (click)="setActiveTab('HDB')"
    >
      <span class="material-icons mr-2">apartment</span>
      HDB
    </button>
    <button
      [class]="
        daisyMerge(
          'px-4 py-2 rounded-md transition-colors flex items-center shadow-md',
          activeTab === 'Condo'
            ? 'bg-blue-600 text-white hover:bg-blue-700'
            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
        )
      "
      (click)="setActiveTab('Condo')"
    >
      <span class="material-icons mr-2">domain</span>
      Condo
    </button>

    <!-- Pagination controls for HDB data -->
    @if (showHDBData && activeTab === 'HDB') {
      <div class="flex items-center ml-auto space-x-2">
        <!-- Limit selector -->
        <div class="flex items-center">
          <label for="hdbLimitSelect" class="text-lg text-gray-600 mr-2">Show:</label>
          <select
            id="hdbLimitSelect"
            class="px-4 py-2 border rounded-md text-sm"
            [(ngModel)]="selectedHDBLimit"
            (change)="loadHDBPage(0)"
          >
            @for (option of paginationOptions; track option) {
              <option [value]="option">{{ option }}</option>
            }
          </select>
        </div>

        <!-- Page navigation -->
        <div class="flex items-center space-x-1">
          <button
            class="px-2 py-1 rounded-md bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
            [disabled]="currentHDBPage === 0"
            (click)="loadHDBPage(currentHDBPage - 1)"
          >
            <span class="material-icons text-lg">chevron_left</span>
          </button>

          <span class="text-sm px-2">
            {{ currentHDBPage + 1 }} / {{ Math.ceil(totalHDBCount / selectedHDBLimit) }}
          </span>

          <button
            class="px-2 py-1 rounded-md bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
            [disabled]="(currentHDBPage + 1) * selectedHDBLimit >= totalHDBCount"
            (click)="loadHDBPage(currentHDBPage + 1)"
          >
            <span class="material-icons text-lg">chevron_right</span>
          </button>
        </div>
      </div>
    }

    <!-- Pagination controls for Condo data -->
    @if (showCondoData && activeTab === 'Condo') {
      <div class="flex items-center ml-auto space-x-2">
        <!-- Limit selector -->
        <div class="flex items-center">
          <label for="condoLimitSelect" class="text-base mr-2">Show:</label>
          <select
            id="condoLimitSelect"
            class="px-2 py-1 border rounded-md text-sm"
            [(ngModel)]="selectedCondoLimit"
            (change)="loadCondoPage(0)"
          >
            @for (option of paginationOptions; track option) {
              <option [value]="option">{{ option }}</option>
            }
          </select>
        </div>

        <!-- Page navigation -->
        <div class="flex items-center space-x-1">
          <button
            class="px-2 py-1 rounded-md bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
            [disabled]="currentCondoPage === 0"
            (click)="loadCondoPage(currentCondoPage - 1)"
          >
            <span class="material-icons text-sm">chevron_left</span>
          </button>

          <span class="text-sm px-2">
            {{ currentCondoPage + 1 }} / {{ Math.ceil(totalCondoCount / selectedCondoLimit) }}
          </span>

          <button
            class="px-2 py-1 rounded-md bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
            [disabled]="(currentCondoPage + 1) * selectedCondoLimit >= totalCondoCount"
            (click)="loadCondoPage(currentCondoPage + 1)"
          >
            <span class="material-icons text-sm">chevron_right</span>
          </button>
        </div>
      </div>
    }
  </div>
  @if (activeTab) {
    <div class="flex space-x-4 p-4 border-b">
      <button
        class="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors flex items-center"
        (click)="displaySaleListings()"
      >
        <mat-icon>attach_money</mat-icon>
        <span class="ml-2">
          {{ activeTab === 'HDB' ? 'HDB Sale Listings' : 'Condo Sale Listings' }}
        </span>
      </button>
      @if (activeTab === 'HDB') {
        <div class="relative inline-block">
          @if (showDropdown) {
            <div class="absolute shadow-lg mt-4 w-64 border rounded p-2 z-10 top-full left-0">
              @for (town of hdbTowns; track $index) {
                <div
                  class="flex items-center space-x-2 cursor-pointer p-1 rounded"
                  (click)="toggleTown(town[1])"
                >
                  <div
                    class="w-5 h-5 border"
                    [style.border-color]="townColors[town[1]]"
                    [style.background-color]="
                      isSelected(town[1]) ? townColors[town[1]] : 'transparent'
                    "
                  ></div>
                  <span
                    [style.color]="townColors[town[1]]"
                    [style.font-weight]="isSelected(town[1]) ? 'bold' : 'normal'"
                  >
                    {{ town[1] }}
                  </span>
                </div>
              }

              <!-- Reset filter option -->
              @if (selectedTowns.size > 0) {
                <div class="mt-2 pt-2 border-t border-gray-200">
                  <div
                    class="flex items-center px-3 py-2 rounded-md cursor-pointer hover:bg-gray-100 text-blue-600"
                    (click)="selectedTowns.clear(); initializeHDBMap()"
                  >
                    <mat-icon class="text-blue-600 mr-2" style="font-size: 18px">refresh</mat-icon>
                    <span>Reset Filter</span>
                  </div>
                </div>
              }
            </div>
          }
          <button
            class="bg-purple-500 text-white px-4 py-2 rounded flex items-center"
            (click)="toggleDropdown()"
          >
            <mat-icon class="mr-2">apartment</mat-icon>
            HDB Data
            <mat-icon class="ml-2">{{
              showDropdown ? 'arrow_drop_up' : 'arrow_drop_down'
            }}</mat-icon>
          </button>
        </div>
      } @else {
        <button
          class="px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600 transition-colors flex items-center"
          (click)="displayPropertyData()"
        >
          <mat-icon>apartment</mat-icon>
          <span class="ml-2">Condo Data</span>
        </button>
      }
    </div>
  }
</div>
