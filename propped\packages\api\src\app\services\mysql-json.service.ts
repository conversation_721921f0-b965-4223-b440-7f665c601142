import { sql } from 'drizzle-orm';
import { DBI } from '../../environments/env-modules';
import { drizzleDb } from '../common/db';
import { type TableMeta } from '../common/tables.const';

const jsPathToMySQLPath = (path: string) => {
  let jsonPath = path
    .split('.')
    .map((part) => (/^\d+$/.test(part) ? `[${part}]` : `.${part}`))
    .join('');
  jsonPath = jsonPath === '$.' ? '$' : jsonPath;
  return jsonPath;
};

const jsPathToPGPath = (path: string) => {
  return `{"${path.split('.').join('","')}"}`;
};

const jsValueToMySQLValue = (value: any) => {
  return `CAST(${DBI.escape(JSON.stringify(value))} AS JSON)`;
};

const jsValueToPGValue = (value: any) => {
  return `${DBI.escape(JSON.stringify(value))}::jsonb`;
};

// const makeJsonSetQuery = (column: string, path: string, value: any) => {
//   return `JSON_SET(${column}, '$${jsPathToMySQLPath(
//     path,
//   )}', ${jsValueToMySQLValue(value)})`;
// };

const makeJsonSetQueryMultiKey = (col: string, data: any) => {
  const result: any = [];
  Object.entries(data).forEach(([key, value]: [string, any]) => {
    result.push(`'$${jsPathToMySQLPath(key)}'`, jsValueToMySQLValue(value));
  });
  return `JSON_SET(${col}, ${result.join(', ')})`;
};

const makeJsonSetQueryMultiKeyPG = (col: string, data: any) => {
  return Object.entries(data).reduce(
    (obj, [key, value]: [string, any]) =>
      `JSONB_SET(${obj}, '${jsPathToPGPath(key)}', ${jsValueToPGValue(value)}, TRUE)`,
    col,
  );
};

const makeJsonSetQueryMultiCol = (data: any) => {
  const result: any = {};
  Object.entries(data).forEach(([col, value]: [string, any]) => {
    result[col] = () => makeJsonSetQueryMultiKey(col, value);
  });
  return result;
};

const makeJsonRemoveQueryMultiKey = (col: string, keys: string[]) => {
  const result: any = [];
  keys.forEach((key: string) => {
    result.push(`'$${jsPathToMySQLPath(key)}'`);
  });
  return `JSON_REMOVE(${col}, ${result.join(', ')})`;
};

const makeJsonRemoveQueryMultiCol = (data: any) => {
  const result: any = {};
  Object.entries(data).forEach(([col, value]: [string, string[]]) => {
    result[col] = () => makeJsonRemoveQueryMultiKey(col, value);
  });
  return result;
};

const makeJsonSetQueryMultiColPG = (data: any) => {
  const result: any = {};
  Object.entries(data).forEach(([col, value]: [string, any]) => {
    result[col] = () => makeJsonSetQueryMultiKeyPG(col, value);
  });
  return result;
};

const makeJsonRemoveQueryMultiKeyPG = (col: string, keys: string[]) => {
  return keys.reduce((obj, key: string) => `(${obj} #- '${jsPathToPGPath(key)}')`, col);
};

const makeJsonRemoveQueryMultiColPG = (data: Record<string, string[]>) => {
  const result: Record<string, () => string> = {};
  Object.entries(data).forEach(([col, value]: [string, string[]]) => {
    result[col] = () => makeJsonRemoveQueryMultiKeyPG(col, value);
  });
  return result;
};

export class MySQLJsonService {
  public hasJSONCol(table: TableMeta) {
    return {
      set: (match: any, data: any) => this.set(table, match, data),
      remove: (match: any, data: any) => this.remove(table, match, data),
    };
  }

  set(table: TableMeta, match: any, data: any) {
    const whereClause = this.convertMatchToWhereClause(match);
    const query = `
      UPDATE ${table.name}
      SET ${this.convertObjectToSETClause(DBI.isMySQL ? makeJsonSetQueryMultiCol(data) : makeJsonSetQueryMultiColPG(data))}
      WHERE ${whereClause}
    `;
    return drizzleDb.execute(sql.raw(query));
  }

  remove(table: TableMeta, match: any, data: Record<string, string[]>) {
    const whereClause = this.convertMatchToWhereClause(match);
    const query = `
      UPDATE ${table.name}
      SET ${this.convertObjectToSETClause(DBI.isMySQL ? makeJsonRemoveQueryMultiCol(data) : makeJsonRemoveQueryMultiColPG(data))}
      WHERE ${whereClause}
    `;
    return drizzleDb.execute(query);
  }

  private convertMatchToWhereClause(match: any): string {
    return Object.entries(match)
      .map(([key, value]) => `${key} = ${value as any}`)
      .join(' AND ');
  }

  convertObjectToSETClause(data: any) {
    return Object.entries(data)
      .map(([key, value]) => {
        if (typeof value === 'function') return `${key} = ${value()}`;
        return `${key} = ${value as any}`;
      })
      .join(', ');
  }
}
