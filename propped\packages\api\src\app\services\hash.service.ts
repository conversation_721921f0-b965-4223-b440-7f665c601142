import type { BinaryLike, ScryptOptions } from 'node:crypto';
import { randomBytes, scrypt, timingSafeEqual } from 'node:crypto';
import { promisify } from 'node:util';
import { environment } from '../../environments/environment';

export const SuperSalt = environment.auth.passwordSalt;

export const scryptP: (
  password: BinaryLike,
  salt: BinaryLike,
  keylen: number,
  options: ScryptOptions,
) => Promise<Buffer> = promisify(scrypt);

export const randomBytesP = (size: number): Promise<Buffer> =>
  new Promise((s, f) => randomBytes(size, (e, d) => (e ? f(e) : s(d))));

export interface IHashEngine {
  make(word: string): Promise<string>;
  check(key: string, hash: string): Promise<boolean>;
}

export class ScryptHashEngine implements IHashEngine {
  public async make(word: string) {
    const randomSalt = (await randomBytesP(15)).toString('base64');
    const prefix = `s$14$8$1$32$${randomSalt}$`;
    const genHash = await scryptP(SuperSalt + word, randomSalt, 32, {
      cost: 2 ** 14,
      blockSize: 8,
      parallelization: 1,
    });
    return prefix + genHash.toString('base64');
  }

  public async check(key: string, hash: string) {
    const [, /* algo  */ iter, block, parallel, len, salt, code] = hash.split('$');
    if (salt === undefined || code === undefined) return false;
    const genHash = await scryptP(SuperSalt + key, salt, Number(len), {
      cost: 2 ** Number(iter),
      blockSize: Number(block),
      parallelization: Number(parallel),
    });
    return timingSafeEqual(genHash, Buffer.from(code, 'base64'));
  }
}

export const PasswordHashEngine: IHashEngine = new ScryptHashEngine();
