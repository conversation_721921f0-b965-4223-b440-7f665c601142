{"linter": {"enabled": true, "rules": {"suspicious": {"noCommentText": "off", "noAssignInExpressions": "off", "noExplicitAny": "off"}, "style": {"noNonNullAssertion": "off", "noCommaOperator": "off", "useImportType": "off", "noParameterAssign": "off"}, "performance": {"noDelete": "off"}, "complexity": {"noForEach": "off", "useLiteralKeys": "off"}, "a11y": {"useAltText": "off", "useKeyWithClickEvents": "off"}}}, "formatter": {"enabled": true, "lineWidth": 100, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf"}, "javascript": {"formatter": {"quoteStyle": "single"}}}