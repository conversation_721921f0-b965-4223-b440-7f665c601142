import { Auth } from '../../guards/auth.guard';
import { Compress } from '../../receptor/compression.receptor';
import { HDBSalesListingOfferModel } from './hdb-sales-offer.model';
import { HDBSalesListingModel } from './hdb-sales.model';


@Auth()
export class HDBSalesOfferController {
  /**
   * Create a new offer
   */
  @Compress()
  async createOffer({
    listingId,
    buyerId,
    sellerId,
    offerPrice,
    message,
  }: {
    listingId: number;
    buyerId: number;
    sellerId: number;
    offerPrice: number;
    message?: string;
  }) {
    console.log("NEW OFFER", listingId, buyerId);

    // Check if an offer already exists for this listing and buyer
    const existingOffer = await HDBSalesListingOfferModel.findOne({
      listingId,
      buyerId,
      sellerId
    });

    if (existingOffer) {
      // Update the existing offer
      const updatedOffer = await HDBSalesListingOfferModel.findByIdAndUpdate(
        existingOffer._id,
        {
          offerPrice,
          message,
          status: 'pending',
          updatedAt: new Date()
        },
        { new: true }
      );
      return { data: updatedOffer._id.toString() };
    } else {
      // Create a new offer
      const offer = await HDBSalesListingOfferModel.create({
        listingId,
        buyerId,
        sellerId,
        offerPrice,
        message,
        status: 'pending',
      });
      return { data: offer._id.toString() };
    }
  }

  /**
   * Reject an offer
   */
  @Compress()
  async rejectOffer({ offerId }: { offerId: string }) {
    const offer = await HDBSalesListingOfferModel.findByIdAndUpdate(
      offerId,
      { status: 'rejected' },
      { new: true }
    );
    return { data: offer };
  }

  /**
   * Counter an offer
   */
  @Compress()
  async counterOffer({
    offerId,
    counterOfferPrice,
    counterOfferMessage,
  }: {
    offerId: string;
    counterOfferPrice: number;
    counterOfferMessage?: string;
  }) {
    const offer = await HDBSalesListingOfferModel.findByIdAndUpdate(
      offerId,
      {
        status: 'countered',
        counterOfferPrice,
        counterOfferMessage,
      },
      { new: true }
    );
    return { data: offer };
  }
  @Compress()
  async getOfferById({ offerId }: { offerId: string }) {

    const offer = await HDBSalesListingOfferModel.findById(offerId);
    return { data: offer };
  }

  @Compress()
  async getOffersBySellerId({ sellerId }: { sellerId: number }) {
    try {
      const listings = await HDBSalesListingModel.find({ user_id: sellerId }).lean();

      const listingIds = listings.map(listing => listing.listingId);

      const offers = await HDBSalesListingOfferModel.find({
        listingId: { $in: listingIds }
      }).sort({ createdAt: -1 }).lean();

      const enrichedOffers = await Promise.all(offers.map((offer) => {
        const listing = listings.find(l => l.listingId === offer.listingId);
        return {
          ...offer,
          listingDetails: listing ? {
            _id: listing._id,
            blockNumber: listing.blockNumber,
            street: listing.street,
            town: listing.town,
            askingPrice: listing.price
          } : null
        };
      }));

      return { data: enrichedOffers };
    } catch (error) {
      console.error('Error fetching offers for seller:', error);
      return { error: 'Failed to fetch offers', status: 500 };
    }
  }

  @Compress()
  async acceptOffer({ offerId }: { offerId: string }) {
    const offer = await HDBSalesListingOfferModel.findByIdAndUpdate(
      offerId,
      { status: 'accepted' },
      { new: true }
    );
    return { data: offer };
  }

  @Compress()
  async getOffersByBuyerId({ buyerId }: { buyerId: number }) {
    try {
      // Find all offers made by this buyer
      const offers = await HDBSalesListingOfferModel.find({ buyerId })
        .sort({ createdAt: -1 })
        .lean();

      // Get all listing IDs from the offers
      const listingIds = [...new Set(offers.map(offer => offer.listingId))];

      // Fetch the listings
      const listings = await HDBSalesListingModel.find({
        listingId: { $in: listingIds }
      }).lean();

      // Enrich the offers with listing details
      const enrichedOffers = offers.map(offer => {
        const listing = listings.find(l => l.listingId === offer.listingId);
        return {
          ...offer,
          listingDetails: listing ? {
            _id: listing._id,
            blockNumber: listing.blockNumber,
            street: listing.street,
            town: listing.town,
            price: listing.price,
          } : null
        };
      });

      return { data: enrichedOffers };
    } catch (error) {
      console.error('Error fetching offers for buyer:', error);
      return { error: 'Failed to fetch buyer offers', status: 500 };
    }
  }

  @Compress()
  async getOfferByListingId({ listingId,buyerId }: { listingId: number,buyerId: number }) {
    try {
      const offer = await HDBSalesListingOfferModel.findOne({ listingId,buyerId })
        .sort({ createdAt: -1 })
        .lean();
      return { data: offer };
    } catch (error) {
      console.error('Error fetching offers for listing:', error);
      return { error: 'Failed to fetch offers for listing', status: 500 };
    }
  }

  @Compress()
  async getAcceptedOffersByBuyerId({ buyerId }: { buyerId: number }) {
    try {
      // Find all accepted offers made by this buyer
      const offers = await HDBSalesListingOfferModel.find({
        buyerId,
        status: 'accepted'
      })
        .sort({ createdAt: -1 })
        .lean();

      // Get all listing IDs from the offers
      const listingIds = [...new Set(offers.map(offer => offer.listingId))];

      // Fetch the listings
      const listings = await HDBSalesListingModel.find({
        listingId: { $in: listingIds }
      }).lean();

      // Enrich the offers with listing details
      const enrichedOffers = offers.map(offer => {
        const listing = listings.find(l => l.listingId === offer.listingId);
        return {
          ...offer,
          listingDetails: listing ? {
            _id: listing._id,
            blockNumber: listing.blockNumber,
            street: listing.street,
            town: listing.town,
            price: listing.price,
            saleCondition: listing.salesCondition,
          } : null
        };
      });

      return { data: enrichedOffers };
    } catch (error) {
      console.error('Error fetching accepted offers for buyer:', error);
      return { error: 'Failed to fetch accepted buyer offers', status: 500 };
    }
  }

  @Compress()
  async getAcceptedOffersBySellerId({ sellerId }: { sellerId: number }) {
    try {
      // Find all accepted offers made by this buyer
      const offers = await HDBSalesListingOfferModel.find({
        sellerId,
        status: 'accepted'
      })
        .sort({ createdAt: -1 })
        .lean();

      // Get all listing IDs from the offers
      const listingIds = [...new Set(offers.map(offer => offer.listingId))];

      // Fetch the listings
      const listings = await HDBSalesListingModel.find({
        listingId: { $in: listingIds }
      }).lean();

      // Enrich the offers with listing details
      const enrichedOffers = offers.map(offer => {
        const listing = listings.find(l => l.listingId === offer.listingId);
        return {
          ...offer,
          listingDetails: listing ? {
            _id: listing._id,
            blockNumber: listing.blockNumber,
            street: listing.street,
            town: listing.town,
            price: listing.price,
            saleCondition: listing.salesCondition,
          } : null
        };
      });

      console.log("ENRICHED OFFERS FOR SELLER", enrichedOffers);
      return { data: enrichedOffers };
    } catch (error) {
      console.error('Error fetching accepted offers for buyer:', error);
      return { error: 'Failed to fetch accepted buyer offers', status: 500 };
    }
  }

  @Compress()
  async getAcceptedOfferByListingAndSeller({
    listingId,
    sellerId
  }: {
    listingId: number;
    sellerId: number;
  }) {
    try {
      // Find the accepted offer for this specific listing and seller
      const offer = await HDBSalesListingOfferModel.findOne({
        listingId,
        sellerId,
        status: 'accepted'
      }).lean();

      if (!offer) {
        return { data: null };
      }

      // Fetch the listing details
      const listing = await HDBSalesListingModel.findOne({
        listingId
      }).lean();

      // Enrich the offer with listing details
      const enrichedOffer = {
        ...offer,
        listingDetails: listing ? {
          _id: listing._id,
          blockNumber: listing.blockNumber,
          street: listing.street,
          town: listing.town,
          price: listing.price,
          saleCondition: listing.salesCondition,
        } : null
      };

      return { data: enrichedOffer };
    } catch (error) {
      console.error('Error fetching accepted offer for listing and seller:', error);
      return { error: 'Failed to fetch accepted offer', status: 500 };
    }
  }

}
