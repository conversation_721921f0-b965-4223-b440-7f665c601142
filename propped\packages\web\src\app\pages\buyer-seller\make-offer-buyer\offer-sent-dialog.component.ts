import { Component as DialogComponent, inject, Inject } from '@angular/core';
import { Router } from '@angular/router';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';

@DialogComponent({
  selector: 'offer-sent-dialog',
  template: `
    <div class="flex flex-col items-center justify-center p-8">
      <mat-icon class="absolute top-2 right-2 cursor-pointer" (click)="onCloseDialog()">cancel</mat-icon>
      <div class="rounded-lg bg-blue-500 text-white w-full text-center p-6 mb-4">
        <div class="text-2xl font-bold mb-2">Offer Sent!</div>
        <div class="text-base">Await Seller's Response in Chat Module</div>
      </div>
      <button mat-button color="primary" class="w-full bg-blue-600 text-white rounded-lg py-2 mt-2" (click)="onReturnToChat()">Return to Chat Module</button>
    </div>
  `,
  standalone: true,
  imports: [CommonModule, MatIconModule],
})
export class OfferSentDialogComponent {
  router = inject(Router);
  dialogRef = inject(MatDialogRef<OfferSentDialogComponent>); // Inject MatDialogRef to control the dialog

  constructor(@Inject(MAT_DIALOG_DATA) public data: any) {
    console.log("OFFER DATA",this.data);
  }

  onReturnToChat() {
    this.dialogRef.close();
    this.router.navigate(['/buyer-seller/seller-preview'], {
      queryParams: {
        offerId: this.data.offerId,
        listingId: this.data.listingId
      }
    });
  }

  onCloseDialog() {
    // Close the dialog when the cancel icon is clicked
    this.dialogRef.close();
  }
}
