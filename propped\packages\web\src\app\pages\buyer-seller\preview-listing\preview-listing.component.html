<div class="w-full p-4">
  <div class="rounded-lg shadow-lg p-6 border border-blue-100">
    <!-- Listing Header Section -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold">
        {{ currentListing?.street }} Block
        {{ currentListing?.blockNo?.substring(3) || currentListing?.block }}
      </h1>
      <p>Postal Code: {{ currentListing?.postalCode }}</p>
      <p class="text-gray-600">{{ currentListing?.town }}</p>

      <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mt-4">
        <div>
          <div class="text-3xl font-bold">
            S$ {{ formatPrice(currentListing?.salesPrice || currentListing?.price) }}
          </div>
          <div>S$ {{ currentListing?.psf || currentListing?.pricePSF }} PSF</div>
          <p class="mt-2 sm:mt-0 text-sm font-semibold text-gray-500">
            Listed on {{ currentListing?.listedDate }} (Listing ID: {{ currentListing?.listingId }})
          </p>
        </div>
      </div>
      <div class="mt-2 w-full h-[1px] bg-gray-200"></div>
      <div class="w-full flex justify-between items-center flex-wrap">
        <div>
          <h4 class="font-bold mt-4 mb-2">Sale Conditions</h4>
          <ul class="list-disc pl-2">
            @for (condition of salesListingData?.salesCondition; track $index) {
              <li class="marker:text-blue-500">{{ condition }}</li>
            }
          </ul>
        </div>
        <div class="my-2">
          <button
            class="bg-blue-600 text-white text-sm my-2 p-2 rounded-lg flex items-center justify-center w-full h-12 md:w-64"
          >
            <mat-icon class="mr-2">compare</mat-icon>
            Side-by-Side Comparison
          </button>
          <button
            class="bg-blue-600 text-white text-sm my-2 p-2 rounded-lg flex items-center justify-center w-full h-12 md:w-64"
            (click)="showOnMap()"
          >
            <mat-icon class="mr-2">map</mat-icon>
            Show On Map
          </button>
        </div>
      </div>
    </div>

    <!-- Image Gallery Section -->
    <div class="mb-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- Main Image -->
        <div class="md:col-span-2 relative">
          <img
            src="assets/dummy_prop.png"
            alt="Main property image"
            class="w-full h-64 md:h-96 object-cover rounded-lg"
          />
          <div class="absolute bottom-3 left-3 flex space-x-2">
            <span class="bg-gray-800 bg-opacity-70 text-white px-3 py-1 rounded-full text-sm">
              {{ currentListing?.photoCount }}
              <mat-icon class="text-sm align-middle">photo_library</mat-icon>
            </span>
            @if (currentListing?.hasVideo) {
              <span class="bg-blue-600 bg-opacity-70 text-white px-3 py-1 rounded-full text-sm">
                LiveTour <mat-icon class="text-sm align-middle">videocam</mat-icon>
              </span>
            }
          </div>
        </div>

        <!-- Thumbnails -->
        <div class="md:col-span-1 grid grid-cols-2 gap-2">
          @for (image of listingData.thumbnails; track image) {
            <img
              [src]="image"
              alt="Property thumbnail"
              class="w-full h-32 object-cover rounded-lg"
            />
          }
        </div>
      </div>
    </div>
    @if (activeView === 'default') {
      <div class="mb-6">
        <button
          class="w-full bg-blue-600 text-white my-2 py-2 px-4 rounded-lg flex items-center justify-center"
          (click)="viewPhotos()"
        >
          View Photos
        </button>
        <button
          class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg flex items-center justify-center"
          (click)="viewFloorplan()"
        >
          View Floorplan / Siteplan
        </button>
      </div>
    }
    @if (activeView !== 'default') {
      <div class="mb-6">
        <div class="bg-white rounded-lg shadow-lg border border-blue-100 dark:bg-gray-800">
          <!-- Header -->
          <div class="bg-white border-b dark:bg-gray-800">
            <div class="p-4">
              <!-- Property Header -->
              <div class="flex justify-end items-center">
                <button (click)="closeDetailView()" class="text-gray-700 dark:text-white">
                  <mat-icon>close</mat-icon>
                </button>
              </div>
            </div>
          </div>

          <!-- Content Area -->
          <div class="p-4 dark:bg-gray-800">
            <!-- Floorplan -->
            <h4 class="font-bold text-xl dark:text-white">Floor plan Details</h4>
            <div class="mb-6 flex justify-center">
              <img
                [src]="'assets/floor-plan-1.jpg'"
                alt="Floorplan"
                class="max-w-full max-h-[500px] object-contain rounded-lg"
              />
            </div>

            <!-- Photos Section Toggle -->
            <div
              class="flex justify-between items-center py-3 px-4 bg-gray-100 rounded-lg cursor-pointer mb-4 dark:border border-blue-200 dark:bg-transparent"
              (click)="togglePhotos()"
            >
              <span class="font-medium dark:text-white">Photos</span>
              <mat-icon>{{ showPhotosExpanded ? 'remove' : 'add' }}</mat-icon>
            </div>

            <!-- Photos Grid (conditionally shown) -->
            @if (showPhotosExpanded && !showPhotoSlider) {
              <div class="mb-6">
                <div class="rounded-lg border border-blue-200 p-4">
                  <h3 class="text-xl font-bold mb-4">Photos</h3>

                  <div class="grid grid-cols-2 sm:grid-cols-4 gap-3">
                    @for (photo of getDummyPhotos(); track photo.type) {
                      <div class="flex flex-col">
                        <div class="relative cursor-pointer" (click)="openPhotoSlider(photo.type)">
                          <img
                            [src]="photo.placeholderImg"
                            [alt]="photo.type"
                            class="w-full aspect-square object-cover rounded-lg"
                          />
                          <div
                            class="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded-full"
                          >
                            {{ photo.type }} ({{ photo.images.length }})
                          </div>
                        </div>
                      </div>
                    }
                  </div>
                </div>
              </div>
            }

            <!-- Photo Slider (when an image is clicked) -->
            @if (showPhotoSlider) {
              <div class="mb-6">
                <div class="rounded-lg border border-blue-200 p-4">
                  <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold">
                      {{ selectedPhotoType }} ({{ selectedPhotoIndex + 1 }}/{{
                        getPhotoCount(selectedPhotoType)
                      }})
                    </h3>
                    <button (click)="closePhotoSlider()" class="font-semibold text-lg">
                      <mat-icon>close</mat-icon>
                    </button>
                  </div>

                  <div class="relative">
                    <!-- Main Image -->
                    <div class="flex justify-center">
                      <img
                        [src]="getCurrentSlideImage()"
                        [alt]="selectedPhotoType"
                        class="max-w-full max-h-[400px] object-contain rounded-lg"
                      />
                    </div>

                    <!-- Navigation Arrows -->
                    <button
                      (click)="prevPhoto()"
                      class="absolute left-2 top-1/2 transform -translate-y-1/2 bg-blue-600 text-white py-1 px-2 rounded-full"
                    >
                      <mat-icon>chevron_left</mat-icon>
                    </button>
                    <button
                      (click)="nextPhoto()"
                      class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-blue-600 text-white py-1 px-2 rounded-full"
                    >
                      <mat-icon>chevron_right</mat-icon>
                    </button>
                  </div>

                  <!-- Thumbnails -->
                  <div class="flex justify-center mt-4 space-x-2">
                    <!-- Using a simpler approach to avoid complex expressions in template -->
                    @if (selectedPhotoType) {
                      @for (idx of [0, 1]; track idx) {
                        <div
                          class="w-16 h-16 rounded-lg cursor-pointer border-2"
                          [class.border-blue-500]="idx === selectedPhotoIndex"
                          [class.border-transparent]="idx !== selectedPhotoIndex"
                          (click)="selectedPhotoIndex = idx"
                        >
                          <img
                            [src]="
                              'assets/properties/' + selectedPhotoType + '-' + (idx + 1) + '.jpg'
                            "
                            class="w-full h-full object-cover rounded-lg"
                            [alt]="selectedPhotoType + ' ' + (idx + 1)"
                          />
                        </div>
                      }
                    }
                  </div>
                </div>
              </div>
            }
          </div>
        </div>
      </div>
    }

    <!-- Seller Message Section -->
    <div class="mb-6 p-4 rounded-lg border border-blue-200">
      <p class="italic">"{{ currentListing?.sellerMessage }}"</p>
    </div>

    <!-- Listing Action Buttons -->
    <div class="flex flex-wrap justify-between mb-6">
      <button class="flex items-center hover:text-blue-600">
        <mat-icon>favorite_border</mat-icon>
        <span class="ml-1">Shortlist</span>
      </button>
      <button class="flex items-center hover:text-blue-600">
        <mat-icon>share</mat-icon>
        <span class="ml-1">Share</span>
      </button>
      <button class="flex items-center hover:text-blue-600">
        <mat-icon>visibility_off</mat-icon>
        <span class="ml-1">Hide</span>
      </button>
      <button class="flex items-center hover:text-blue-600">
        <mat-icon>flag</mat-icon>
        <span class="ml-1">Report</span>
      </button>
    </div>

    <!-- Inquiry / Negotiation Buttons -->
    <div class="space-y-3 mb-6">
      @if (userData?.id === salesListingData?.user_id){
      <button
        class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg flex items-center justify-center"
        (click)="openSellerInfoModal()"
      >
        Ask Seller a question / request more photos
      </button>
      } @else {

      <button
      class="w-full bg-blue-600  text-white py-3 px-4 rounded-lg flex items-center justify-center"
      (click)="navigateBuyerToChatPage()"
      >
        Ask Seller a question / request more photos
      </button>
     }
     @if (userData?.id !== salesListingData?.user_id){
      <button
        (click)="goToMakeOfferPage()"
        class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg flex items-center justify-center"
      >
        Enter Negotiation Room / Make Offer
      </button>
      @if(buyerOfferData && buyerOfferData.status === 'pending'){
        <p class="text-sm font-semibold">Your have offered a price of {{buyerOfferData.offerPrice}} which is pending approval</p>
      }
      @if(buyerOfferData && buyerOfferData.status === 'rejected'){
        <p class="text-sm font-semibold">Your have offered a price of {{buyerOfferData.offerPrice}} which is rejected</p>
      }
      @if(buyerOfferData && buyerOfferData.status === 'accepted'){
        <p class="text-sm font-bold">Your have offered a price of {{buyerOfferData.offerPrice}} which is accepted, Please visit ,<span>Dashboard</span> for Resale Transaction Progress</p>
      }

    }
    @else {
      <button
      class="w-full bg-blue-600 opacity-50 text-white py-3 px-4 rounded-lg flex items-center justify-center"
      disabled
      >
        Enter Negotiation Room / Make Offer
      </button>
    }
    </div>

    <!-- Property Details Section -->
    <div class="mb-6">
      <h2 class="text-xl font-bold text-gray-900 mb-4 border-b pb-2">Property Details</h2>
      <div class="grid grid-cols-2 gap-4">
        <!-- Left Column -->
        <div>
          <div class="mb-3">
            <div class="text-sm text-gray-500">Area</div>
            <div class="font-medium">
              {{ listingData?.area.sqm }} sqm / {{ listingData?.area.sqm * 10.7639 }} sqft
            </div>
          </div>
          <div class="mb-3">
            <div class="text-sm text-gray-500">Developer</div>
            <div class="font-medium">{{ listingData?.developer }}</div>
          </div>
          <div class="mb-3">
            <div class="text-sm text-gray-500">Tenure (Remaining)</div>
            <div class="font-medium">
              {{ listingData?.tenure.remaining }} years
            </div>
          </div>
          <div class="mb-3">
            <div class="text-sm text-gray-500">Flat Type</div>
            <div class="font-medium">{{ listingData?.flatType }}</div>
          </div>
        </div>

        <!-- Right Column -->
        <div>
          <div class="mb-3">
            <div class="text-sm text-gray-500">Floor</div>
            <div class="font-medium">{{ listingData?.floor }}</div>
          </div>
          <div class="mb-3">
            <div class="text-sm text-gray-500">Flat Model</div>
            <div class="font-medium">{{ listingData?.flatModel }}</div>
          </div>
          <div class="mb-3">
            <div class="text-sm text-gray-500">Bedrooms</div>
            <div class="font-medium">{{ listingData?.bedrooms }}</div>
          </div>
          <div class="mb-3">
            <div class="text-sm text-gray-500">Bathrooms</div>
            <div class="font-medium">{{ listingData?.bathrooms }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Sale Conditions Section -->
    <!-- <div class="mb-6">
      <h2 class="text-xl font-bold text-gray-900 mb-4 border-b pb-2">Sale Conditions</h2>
      <div class="grid grid-cols-2 gap-4">
        <div class="mb-3">
          <div class="text-sm text-gray-500">Vacant Possession</div>
          <div class="font-medium">{{ listingData.saleConditions.vacantPossession }}</div>
        </div>
        <div class="mb-3">
          <div class="text-sm text-gray-500">Extension of Stay</div>
          <div class="font-medium">{{ listingData.saleConditions.extensionOfStay }} months</div>
        </div>
        @if (listingData.saleConditions.otherConditions) {
          <div class="col-span-2 mb-3">
            <div class="text-sm text-gray-500">Other Conditions</div>
            <div class="font-medium">{{ listingData.saleConditions.otherConditions }}</div>
          </div>
        }
      </div>
    </div> -->

    <!-- Navigation Buttons -->
    <div class="flex justify-between mt-8">
      <button
        routerLink="/buyer-seller/step5"
        class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
      >
        Back to Edit
      </button>

      <button
        routerLink="/buyer-seller"
        class="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
      >
        Submit Listing
      </button>
    </div>

    <!-- Map Modal -->
    @if (showMapModal) {
      <div class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
        <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-4xl mx-4">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold text-blue-900">Property Location</h3>
            <button class="text-gray-500 hover:text-gray-700" (click)="closeMapModal()">
              <mat-icon>close</mat-icon>
            </button>
          </div>
          <div class="h-96 bg-gray-200 rounded-lg flex items-center justify-center">
            <!-- In a real app, this would be a Google Map component -->
            <div class="text-center">
              <mat-icon class="text-5xl text-gray-400">map</mat-icon>
              <p class="mt-2 text-gray-600">Map would be displayed here</p>
              <p class="text-sm text-gray-500">
                {{ currentListing?.address }}, Block {{ currentListing?.block }},
                {{ currentListing?.town }}
              </p>
            </div>
          </div>
        </div>
      </div>
    }

    <!-- Old Floorplan Modal removed and replaced with the dynamic UI above -->
  </div>
</div>
