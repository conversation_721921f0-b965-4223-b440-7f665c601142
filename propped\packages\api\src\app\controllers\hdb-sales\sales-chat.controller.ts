import { Auth } from '../../guards/auth.guard';
import { Compress } from '../../receptor/compression.receptor';
import { HDBSalesChatModel, type IChatMessage } from './sales-chat.model';
import { HDBSalesListingModel } from './hdb-sales.model';

@Auth()
export class SalesChatController {
  @Compress()
  async createChat({
    listingId,
    buyerId,
    sellerId,
    initialMessage,
    initialMessageType,
  }: {
    listingId: string;
    buyerId: number;
    sellerId: number;
    initialMessage?: string;
    initialMessageType?: 'text' | 'image' | 'video' | 'file';
  }) {
    try {
      // Check if the listing exists
      const listing = await HDBSalesListingModel.findById(listingId);
      if (!listing) {
        return { error: 'Listing not found', status: 404 };
      }

      // Check if a chat already exists between these users for this listing
      const existingChat = await HDBSalesChatModel.findOne({
        listingId,
        buyerId,
        sellerId,
      });

      if (existingChat) {
        return { data: existingChat, status: 200 };
      }

      // Create a new chat
      const messages: IChatMessage[] = [];

      // Add initial message if provided
      if (initialMessage) {
        messages.push({
          message: initialMessage,
          role: 'buyer',
          user_id: buyerId,
          timestamp: new Date(),
          messageType: initialMessageType,
        });
      }

      const newChat = await HDBSalesChatModel.create({
        listingId,
        buyerId,
        sellerId,
        messages,
      });

      return { data: newChat, status: 201 };
    } catch (error) {
      console.error('Error creating chat:', error);
      return { error: 'Failed to create chat', status: 500 };
    }
  }

  @Compress()
  async getChatById({ chatId }: { chatId: string }) {
    try {
      const chat = await HDBSalesChatModel.findById(chatId);
      if (!chat) {
        return { error: 'Chat not found', status: 404 };
      }
      return { data: chat, status: 200 };
    } catch (error) {
      console.error('Error fetching chat:', error);
      return { error: 'Failed to fetch chat', status: 500 };
    }
  }

  /**
   * Get chat conversation by listing ID and user IDs
   * @param listingId The ID of the sales listing
   * @param buyerId The ID of the buyer
   * @param sellerId The ID of the seller
   * @returns The chat conversation
   */
  @Compress()
  async getChatByListingAndUsers({
    listingId,
    buyerId,
    sellerId,
  }: {
    listingId: string;
    buyerId: number;
    sellerId: number;
  }) {
    try {
      const chat = await HDBSalesChatModel.findOne({
        listingId,
        buyerId,
        sellerId,
      });

      if (!chat) {
        return { error: 'Chat not found', status: 404 };
      }

      return { data: chat, status: 200 };
    } catch (error) {
      console.error('Error fetching chat:', error);
      return { error: 'Failed to fetch chat', status: 500 };
    }
  }

  /**
   * Get all chats for a specific listing
   * @param listingId The ID of the sales listing
   * @returns Array of chat conversations
   */
  @Compress()
  async getChatsByListingId({ listingId }: { listingId: string }) {
    try {
      console.log("LISTING ID", listingId);
      const chats = await HDBSalesChatModel.find({ listingId });
      return { data: chats, status: 200 };
    } catch (error) {
      console.error('Error fetching chats:', error);
      return { error: 'Failed to fetch chats', status: 500 };
    }
  }

  /**
   * Get all chats for a specific user (either as buyer or seller)
   * @param userId The ID of the user
   * @returns Array of chat conversations
   */
  @Compress()
  async getChatsByUserId({ userId }: { userId: number }) {
    try {
      const chats = await HDBSalesChatModel.find({
        $or: [{ buyerId: userId }, { sellerId: userId }],
      });
      return { data: chats, status: 200 };
    } catch (error) {
      console.error('Error fetching chats:', error);
      return { error: 'Failed to fetch chats', status: 500 };
    }
  }

  /**
   * Add a new message to an existing chat conversation
   * @param chatId The ID of the chat conversation
   * @param message The message content
   * @param role The role of the sender (buyer or seller)
   * @param userId The ID of the sender
   * @returns The updated chat conversation
   */
  @Compress()
  async addMessage({
    chatId,
    message,
    role,
    userId,
    messageType,
  }: {
    chatId: string;
    message: string;
    role: 'buyer' | 'seller' | 'system';
    userId: number;
    messageType: 'text' | 'image' | 'video' | 'file';
  }) {
    try {
      const chat = await HDBSalesChatModel.findById(chatId);
      if (!chat) {
        return { error: 'Chat not found', status: 404 };
      }

      // Validate that the user is either the buyer or seller
      if (role === 'buyer' && chat.buyerId !== userId) {
        return { error: 'User is not the buyer in this chat', status: 403 };
      }
      if (role === 'seller' && chat.sellerId !== userId) {
        return { error: 'User is not the seller in this chat', status: 403 };
      }

      // Add the new message
      const newMessage: IChatMessage = {
        message,
        role,
        user_id: userId,
        timestamp: new Date(),
        messageType,
      };

      chat.messages.push(newMessage);
      await chat.save();

      return { data: chat, status: 200 };
    } catch (error) {
      console.error('Error adding message:', error);
      return { error: 'Failed to add message', status: 500 };
    }
  }

  @Compress()
  async updateChatOffer({
    chatId,
    offerId,
  }: {
    chatId: string;
    offerId: string;
  }) {
    try {
      const chat = await HDBSalesChatModel.findByIdAndUpdate(
        chatId,
        { offerId },
        { new: true }
      );

      if (!chat) {
        return { error: 'Chat not found', status: 404 };
      }

      return { data: chat, status: 200 };
    } catch (error) {
      console.error('Error updating chat offer:', error);
      return { error: 'Failed to update chat offer', status: 500 };
    }
  }

  @Compress()
  async deleteChat({ chatId }: { chatId: string }) {
    try {
      const result = await HDBSalesChatModel.findByIdAndDelete(chatId);
      if (!result) {
        return { error: 'Chat not found', status: 404 };
      }
      return { data: { message: 'Chat deleted successfully' }, status: 200 };
    } catch (error) {
      console.error('Error deleting chat:', error);
      return { error: 'Failed to delete chat', status: 500 };
    }
  }

  @Compress()
  async editMessage({
    chatId,
    messageId,
    newMessage,
    userId,
  }: {
    chatId: string;
    messageId: string;
    newMessage: string;
    userId: number;
  }) {
    try {
      const chat = await HDBSalesChatModel.findById(chatId);
      if (!chat) {
        return { error: 'Chat not found', status: 404 };
      }

      // Find the message by its ID
      const messageIndex = chat.messages.findIndex(
        (msg) => msg._id.toString() === messageId
      );

      if (messageIndex === -1) {
        return { error: 'Message not found', status: 404 };
      }

      // Check if the user is the author of the message
      if (chat.messages[messageIndex].user_id !== userId) {
        return { error: 'You can only edit your own messages', status: 403 };
      }

      // Update the message
      chat.messages[messageIndex].message = newMessage;
      chat.messages[messageIndex].edited = true;
      chat.messages[messageIndex].editedAt = new Date();

      await chat.save();
      return { data: chat, status: 200 };
    } catch (error) {
      console.error('Error editing message:', error);
      return { error: 'Failed to edit message', status: 500 };
    }
  }

  @Compress()
  async deleteMessage({
    chatId,
    messageId,
    userId,
  }: {
    chatId: string;
    messageId: string;
    userId: number;
  }) {
    try {
      const chat = await HDBSalesChatModel.findById(chatId);
      if (!chat) {
        return { error: 'Chat not found', status: 404 };
      }

      // Find the message by its ID
      const messageIndex = chat.messages.findIndex(
        (msg) => msg._id.toString() === messageId
      );

      if (messageIndex === -1) {
        return { error: 'Message not found', status: 404 };
      }

      // Check if the user is the author of the message
      if (chat.messages[messageIndex].user_id !== userId) {
        return { error: 'You can only delete your own messages', status: 403 };
      }

      // Remove the message
      chat.messages.splice(messageIndex, 1);

      await chat.save();
      return { data: chat, status: 200 };
    } catch (error) {
      console.error('Error deleting message:', error);
      return { error: 'Failed to delete message', status: 500 };
    }
  }
  @Compress()
  async getChatsBySellerId({ sellerId }: { sellerId: number }) {
    try {
      const chats = await HDBSalesChatModel.find({ sellerId })
        .populate({
          path: 'listingId',
          model: HDBSalesListingModel,
          select: 'blockNumber street town askingPrice listingId'
        });
      return { data: chats, status: 200 };
    } catch (error) {
      console.error('Error fetching seller chats:', error);
      return { error: 'Failed to fetch seller chats', status: 500 };
    }
  }
}
