import { Schema, model, type Types } from 'mongoose';


export interface IHDBSalesTransaction {
  _id?: Types.ObjectId;
  offerId: Types.ObjectId;      // Reference to HDBSalesListingOffer _id
  listingId: Types.ObjectId;       // Reference to HDBSalesListing listingId
  buyerId: number;              // User ID of the buyer
  sellerId: number;             // User ID of the seller
  createdAt?: Date;
  updatedAt?: Date;

  sellerCheckpoints: {
    findBuyer: {
      title?: string;
      completed: boolean;
      completedAt?: Date;
      notes?: string;
    };
    verifyDetailsInOTP: {
      title?: string;
      completed: boolean;
      completedAt?: Date;
      notes?: string;
    };

    receiveOptionFee: {
      title?: string;
      completed: boolean;
      completedAt?: Date;
      amount?: number;
      notes?: string;
    };

    singpassSignAndIssueOTP: {
      title?: string;
      completed: boolean;
      completedAt?: Date;
      notes?: string;
    };
    receiveHDBValuationResult: {
      title?: string;
      completed: boolean;
      completedAt?: Date;
      valuationAmount?: number;
      notes?: string;
      role?: string;
    };

    receiveCompletedOTP: {
      title?: string;
      completed: boolean;
      completedAt?: Date;
      notes?: string;
      role?: string;
    };

    receiveExerciseFee: {
      title?: string;
      completed: boolean;
      completedAt?: Date;
      amount?: number;
      notes?: string;
      role?: string;
    };
    submitDocumentsToHDB: {
      title?: string;
      completed: boolean;
      completedAt?: Date;
      submissionId?: string;
      notes?: string;
    };

    endorseDocumentsAndPayFees: {
      title?: string;
      completed: boolean;
      completedAt?: Date;
      fees?: number;
      notes?: string;
    };

    receiveHDBAppointmentNotice: {
      title?: string;
      completed: boolean;
      completedAt?: Date;
      appointmentDate?: Date;
      notes?: string;
    };

    receiveHDBApprovalLetter: {
      title?: string;
      completed: boolean;
      completedAt?: Date;
      approvalDate?: Date;
      notes?: string;
    };

    attendHDBCompletionAppointment: {
      title?: string;
      completed: boolean;
      completedAt?: Date;
      completionDate?: Date;
      notes?: string;
    };
    paymentToPropped: {
      title?: string;
      completed: boolean;
      completedAt?: Date;
      amount?: number;
      paymentMethod?: string;
      notes?: string;
    };
  };

  buyerCheckpoints: {
    makePriceOffer: {
      title?: string;
      completed: boolean;
      completedAt?: Date;
      offerAmount?: number;
      notes?: string;

    };

    agreePriceWithSeller: {
      title?: string;
      completed: boolean;
      completedAt?: Date;
      agreedPrice?: number;
      notes?: string;
    };

    payOptionFee: {
      title?: string;
      completed: boolean;
      completedAt?: Date;
      amount?: number;
      notes?: string;
    };

    applyForHDBValuation: {
      title?: string;
      completed: boolean;
      completedAt?: Date;
      valuationRequestId?: string;
      notes?: string;
    };

    receiveHDBValuationBuyer: {
      title?: string;
      completed: boolean;
      completedAt?: Date;
      valuationAmount?: number;
      notes?: string;
    };

    obtainBankOfferLetterOrHDBLoan: {
      title?: string;
      completed: boolean;
      completedAt?: Date;
      loanAmount?: number;
      lender?: string;
      notes?: string;
    };

    exerciseOTPSignOTP: {
      title?: string;
      completed: boolean;
      completedAt?: Date;
      notes?: string;
    };

    payExerciseFee: {
      title?: string;
      completed: boolean;
      completedAt?: Date;
      amount?: number;
      notes?: string;
    };

    receiveSignedOTPFromSeller: {
      title?: string;
      completed: boolean;
      completedAt?: Date;
      notes?: string;
    };

    submitDocumentsToHDBBuyer: {
      title?: string;
      completed: boolean;
      completedAt?: Date;
      submissionId?: string;
      notes?: string;
    };

    endorseDocumentsAndPayFeesBuyer: {
      title?: string;
      completed: boolean;
      completedAt?: Date;
      fees?: number;
      notes?: string;
    };

    receiveHDBAppointmentNoticeBuyer: {
      title?: string;
      completed: boolean;
      completedAt?: Date;
      appointmentDate?: Date;
      notes?: string;
    };

    receiveHDBApprovalLetterBuyer: {
      title?: string;
      completed: boolean;
      completedAt?: Date;
      approvalDate?: Date;
      notes?: string;
    };

    attendHDBCompletionAppointmentBuyer: {
      title?: string;
      completed: boolean;
      completedAt?: Date;
      completionDate?: Date;
      notes?: string;
    };
  };

  // Transaction checkpoints with completion status and dates
  checkpoints: {
    // 1. Price Acceptance
    priceAcceptance: {
      completed: boolean;
      completedAt?: Date;
      notes?: string;
    };
    // 2. Buyer Places Option Fee
    buyerOptionFee: {
      completed: boolean;
      completedAt?: Date;
      amount?: number;
      notes?: string;
    };

    // 3. Seller Receives Option Fee
    sellerOptionFee: {
      completed: boolean;
      completedAt?: Date;
      notes?: string;
    };

    // 4. Seller Issues OTP
    sellerIssuesOTP: {
      completed: boolean;
      completedAt?: Date;
      otpDaysRemaining?: number;
      notes?: string;
    };

    // 5. Buyer Obtains Valuation
    buyerValuation: {
      completed: boolean;
      completedAt?: Date;
      valuationAmount?: number;
      valuationAgency?: string;
      notes?: string;
    };

    // 6. Buyer Obtains Loan Approval
    buyerLoanApproval: {
      completed: boolean;
      completedAt?: Date;
      loanAmount?: number;
      lender?: string;
      notes?: string;
    };

    // 7. Buyer Exercises Option
    buyerExercisesOption: {
      completed: boolean;
      completedAt?: Date;
      notes?: string;
    };

    // 8. Buyer Deposits Exercise Fee
    buyerExerciseFee: {
      completed: boolean;
      completedAt?: Date;
      amount?: number;
      notes?: string;
    };

    // 9. Seller Receives Exercise Fee
    sellerExerciseFee: {
      completed: boolean;
      completedAt?: Date;
      notes?: string;
    };

    // 10. Submission to HDB Resale Portal
    hdbPortalSubmission: {
      completed: boolean;
      completedAt?: Date;
      submissionId?: string;
      notes?: string;
    };

    // 11. HDB Sets Appointment Date
    hdbAppointment: {
      completed: boolean;
      completedAt?: Date;
      appointmentDate?: Date;
      notes?: string;
    };

    // 12. Receipt of Approval Letter
    approvalLetter: {
      completed: boolean;
      completedAt?: Date;
      approvalDate?: Date;
      notes?: string;
    };

    // 13. Final Completion
    finalCompletion: {
      completed: boolean;
      completedAt?: Date;
      completionDate?: Date;
      notes?: string;
    };

    // 14. Payment to Propped
    paymentToPropped: {
      completed: boolean;
      completedAt?: Date;
      amount?: number;
      paymentMethod?: string;
      notes?: string;
    };
  };

      // Buyer Checkpoints


  // Overall transaction status
  status: 'in_progress' | 'completed' | 'cancelled' | 'on_hold';

  // Additional fields for transaction tracking
  transactionAmount: number;    // Final agreed sale price
  completionPercentage: number; // Calculated percentage of completion
  expectedCompletionDate?: Date;
  actualCompletionDate?: Date;
  documents?: {                 // Document tracking
    name: string;
    uploadedAt: Date;
    url: string;
    type: string;
  }[];
}

const HDBSalesTransactionSchema = new Schema<IHDBSalesTransaction>(
  {
    offerId: { type: Schema.Types.ObjectId, required: true, ref: 'HDBSalesListingOffer' },
    listingId: { type: Schema.Types.ObjectId, required: true, ref: 'HDBSalesListing' },
    buyerId: { type: Number, required: true },
    sellerId: { type: Number, required: true },

    buyerCheckpoints:{
      // Buyer Checkpoints
      makePriceOffer: {
        title: { type: String, default: 'Make Price Offer' },
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        offerAmount: { type: Number },
        notes: { type: String },
      },
      agreePriceWithSeller: {
        title: { type: String, default: 'Agree Price with Seller' },
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        agreedPrice: { type: Number },
        notes: { type: String },
      },
      payOptionFee: {
        title: { type: String, default: 'Pay $1,000 Option Fee' },
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        amount: { type: Number, default: 1000 },
        notes: { type: String },
      },
      applyForHDBValuation: {
        title: { type: String, default: 'Apply for HDB Valuation' },
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        valuationRequestId: { type: String },
        notes: { type: String },
      },
      receiveHDBValuationBuyer: {
        title: { type: String, default: 'Receive HDB Valuation Result' },
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        valuationAmount: { type: Number },
        notes: { type: String },
      },
      obtainBankOfferLetterOrHDBLoan: {
        title: { type: String, default: 'Obtain Bank Offer Letter / HDB Loan' },
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        loanAmount: { type: Number },
        lender: { type: String },
        notes: { type: String },
      },
      exerciseOTPSignOTP: {
        title: { type: String, default: 'Exercise OTP / Sign OTP' },
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        notes: { type: String },
      },
      payExerciseFee: {
        title: { type: String, default: 'Pay $4,000 Exercise Fee' },
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        amount: { type: Number, default: 4000 },
        notes: { type: String },
      },
      receiveSignedOTPFromSeller: {
        title: { type: String, default: 'Receive Signed OTP from Seller' },
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        notes: { type: String },
        role: { type: String, default: 'buyer' }
      },
      submitDocumentsToHDBBuyer: {
        title: { type: String, default: 'Submit Documents to HDB' },
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        submissionId: { type: String },
        notes: { type: String },
      },
      endorseDocumentsAndPayFeesBuyer: {
        title: { type: String, default: 'Endorse Documents + Pay Fees' },
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        fees: { type: Number },
        notes: { type: String },
      },
      receiveHDBAppointmentNoticeBuyer: {
        title: { type: String, default: 'Receive HDB Appointment Notice' },
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        appointmentDate: { type: Date },
        notes: { type: String },
      },
      receiveHDBApprovalLetterBuyer: {
        title: { type: String, default: 'Receive HDB Approval Letter' },
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        approvalDate: { type: Date },
        notes: { type: String },
      },
      attendHDBCompletionAppointmentBuyer: {
        title: { type: String, default: 'Attend HDB Completion Appt' },
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        completionDate: { type: Date },
        notes: { type: String },
      },
    },

    sellerCheckpoints:{
      findBuyer: {
        title: { type: String, default: 'Find a Buyer' },
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        notes: { type: String },
      },
      verifyDetailsInOTP: {
        title: { type: String, default: 'Verify Details in OTP' },
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        notes: { type: String },

      },
      receiveOptionFee: {
        title: { type: String, default: 'Receive Option Fee' },
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        amount: { type: Number },
        notes: { type: String },
      },
      singpassSignAndIssueOTP: {
        title: { type: String, default: 'Singpass Sign & Issue OTP' },
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        notes: { type: String },
      },
      receiveHDBValuationResult: {
        title: { type: String, default: 'Receive HDB Valuation Result' },
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        valuationAmount: { type: Number },
        notes: { type: String },
      },
      receiveCompletedOTP: {
        title: { type: String, default: 'Receive Completed OTP' },
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        notes: { type: String },
      },
      receiveExerciseFee: {
        title: { type: String, default: 'Receive Exercise Fee' },
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        amount: { type: Number },
        notes: { type: String },
      },
      submitDocumentsToHDB: {
        title: { type: String, default: 'Submit Documents to HDB' },
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        submissionId: { type: String },
        notes: { type: String },
      },
      endorseDocumentsAndPayFees: {
        title: { type: String, default: 'Endorse Documents + Pay Fees' },
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        fees: { type: Number },
        notes: { type: String },
      },
      receiveHDBAppointmentNotice: {
        title: { type: String, default: 'Receive HDB Appointment Notice' },
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        appointmentDate: { type: Date },
        notes: { type: String },
      },
      receiveHDBApprovalLetter: {
        title: { type: String, default: 'Receive HDB Approval Letter' },
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        approvalDate: { type: Date },
        notes: { type: String },
      },
      attendHDBCompletionAppointment: {
        title: { type: String, default: 'Attend HDB Completion Appt' },
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        completionDate: { type: Date },
        notes: { type: String },
      },

    },

    checkpoints: {
      priceAcceptance: {
        title: { type: String , default: 'Price Acceptance'},
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        notes: { type: String }
      },
      // Existing checkpoints
      buyerOptionFee: {
        title: { type: String, default: 'Buyer places Option Fee'},
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        amount: { type: Number },
        notes: { type: String }
      },
      sellerOptionFee: {
        title: { type: String, default: 'Seller receives Option Fee'},
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        notes: { type: String }
      },
      sellerIssuesOTP: {
        title: { type: String, default: 'Seller issues OTP'},
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        otpDaysRemaining: { type: Number, default: 21 },
        notes: { type: String }
      },
      buyerValuation: {
        title: { type: String, default: 'Buyer obtains Valuation'},
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        valuationAmount: { type: Number },
        valuationAgency: { type: String },
        notes: { type: String }
      },
      buyerLoanApproval: {
        title: { type: String, default: 'Buyer obtains Loan Approval'},
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        loanAmount: { type: Number },
        lender: { type: String },
        notes: { type: String }
      },
      buyerExercisesOption: {
        title: { type: String, default: 'Buyer exercises Option'},
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        notes: { type: String }
      },
      buyerExerciseFee: {
        title: { type: String, default: 'Buyer Deposit Exercise Fee'},
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        amount: { type: Number },
        notes: { type: String }
      },
      sellerExerciseFee: {
        title: { type: String, default: 'Seller Receives Exercise Fee'},
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        notes: { type: String }
      },
      hdbPortalSubmission: {
        title: { type: String, default: 'Submission to HDB Resale Portal'},
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        submissionId: { type: String },
        notes: { type: String }
      },
      hdbAppointment: {
        title: { type: String, default: 'HDB Sets Appointment Date'},
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        appointmentDate: { type: Date },
        notes: { type: String }
      },
      approvalLetter: {
        title: { type: String, default: 'Receipt of Approval Letter'},
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        approvalDate: { type: Date },
        notes: { type: String }
      },
      finalCompletion: {
        title: { type: String, default: 'Final Completion'},
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        completionDate: { type: Date },
        notes: { type: String }
      },
      paymentToPropped: {
        title: { type: String, default: 'Payment to Propped'},
        completed: { type: Boolean, default: false },
        completedAt: { type: Date },
        amount: { type: Number },
        paymentMethod: { type: String },
        notes: { type: String }
      }
    },

    status: {
      type: String,
      enum: ['in_progress', 'completed', 'cancelled', 'on_hold'],
      default: 'in_progress'
    },

    transactionAmount: { type: Number, required: true },
    completionPercentage: { type: Number, default: 0 },
    expectedCompletionDate: { type: Date },
    actualCompletionDate: { type: Date },

    documents: [{
      name: { type: String },
      uploadedAt: { type: Date },
      url: { type: String },
      type: { type: String }
    }]
  },
  {
    timestamps: true,
    collection: 'HDBSalesTransaction'
  }
);

HDBSalesTransactionSchema.pre('save', function(next) {
  // Count total checkpoints
  let totalCheckpoints = 0;
  let completedCheckpoints = 0;
  const checkpoints = this.checkpoints;

  // Count seller checkpoints
  const sellerCheckpointKeys = [
    'findBuyer', 'verifyDetailsInOTP', 'receiveOptionFee', 'singpassSignAndIssueOTP',
    'receiveHDBValuationResult', 'receiveCompletedOTP', 'receiveExerciseFee',
    'submitDocumentsToHDB', 'endorseDocumentsAndPayFees', 'receiveHDBAppointmentNotice',
    'receiveHDBApprovalLetter', 'attendHDBCompletionAppointment'
  ];

  // Count buyer checkpoints
  const buyerCheckpointKeys = [
    'makePriceOffer', 'agreePriceWithSeller', 'payOptionFee', 'applyForHDBValuation',
    'receiveHDBValuationBuyer', 'obtainBankOfferLetterOrHDBLoan', 'exerciseOTPSignOTP',
    'payExerciseFee', 'receiveSignedOTPFromSeller', 'submitDocumentsToHDBBuyer',
    'endorseDocumentsAndPayFeesBuyer', 'receiveHDBAppointmentNoticeBuyer',
    'receiveHDBApprovalLetterBuyer', 'attendHDBCompletionAppointmentBuyer'
  ];

  // Count common checkpoints
  const commonCheckpointKeys = [
    'priceAcceptance', 'paymentToPropped'
  ];

  // Count all checkpoint keys
  const allCheckpointKeys = [...sellerCheckpointKeys, ...buyerCheckpointKeys, ...commonCheckpointKeys];
  totalCheckpoints = allCheckpointKeys.length;

  // Count completed checkpoints
  allCheckpointKeys.forEach(key => {
    if (checkpoints[key] && checkpoints[key].completed) {
      completedCheckpoints++;
    }
  });

  // Calculate percentage
  this.completionPercentage = Math.round((completedCheckpoints / totalCheckpoints) * 100);

  // If all checkpoints are completed, update status to completed
  if (completedCheckpoints === totalCheckpoints) {
    this.status = 'completed';
    this.actualCompletionDate = new Date();
  }

  next();
});

export const HDBSalesTransactionModel = model<IHDBSalesTransaction>('HDBSalesTransaction', HDBSalesTransactionSchema);
