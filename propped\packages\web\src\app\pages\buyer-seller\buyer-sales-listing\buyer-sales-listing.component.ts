import { Component, type OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../../../services/auth.service';
import { apiRPC, injectController } from '@api/rpc';
import { inject } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { SnackBarService } from '@lib/angular/snack-bar.service';

@Component({
  selector: 'app-buyer-sales-listing',
  standalone: true,
  imports: [CommonModule, DatePipe],
  templateUrl: './buyer-sales-listing.component.html',
  styleUrls: [],
})
export class BuyerSalesListingComponent implements OnInit {
  readonly #hdbSalesController = injectController(apiRPC.HDBSalesController);
  readonly #hdbSalesOfferController = injectController(apiRPC.HDBSalesOfferController);
  readonly router = inject(Router);
  readonly authService = inject(AuthService);
  readonly snackBarService = inject(SnackBarService);

  userId: number | null = null;
  salesListings: any[] = [];
  userOffers: any[] = [];
  isLoading = true;

  async ngOnInit() {
    try {
      this.isLoading = true;
      const user = await this.authService.getActiveUser();
      this.userId = user.id;

      // Fetch all sales listings except those by the current user
      const res = await this.#hdbSalesController.getAllSalesListings({ user_id: this.userId });
      this.salesListings = res?.data || [];

      // Fetch user's existing offers
      await this.fetchUserOffers();
    } catch (error) {
      console.error('Error loading sales listings:', error);
      this.snackBarService.error('Failed to load sales listings');
    } finally {
      this.isLoading = false;
    }
  }

  async fetchUserOffers() {
    if (!this.userId) return;

    try {
      const offersRes = await this.#hdbSalesOfferController.getOffersByBuyerId({ buyerId: this.userId });
      this.userOffers = offersRes?.data || [];
      console.log('User offers:', this.userOffers);
    } catch (error) {
      console.error('Error fetching user offers:', error);
    }
  }

  hasExistingOffer(listingId: number): boolean {
    if (!this.userOffers || this.userOffers.length === 0) return false;

    return this.userOffers.some(offer =>
      offer.listingId === listingId &&
      (offer.status === 'pending' || offer.status === 'accepted')
    );
  }

  makeOffer(listing: any) {
    if (this.hasExistingOffer(listing.listingId)) {
      this.snackBarService.info('You already have a pending or accepted offer for this listing');
      return;
    }

    console.log("LISTING", listing);
    this.router.navigate(['/buyer-seller/make-offer-buyer'], {
      queryParams: {
        listing_id: listing.listingId,
        id: listing._id
      },
    });
  }

  proceedToPreviewListing(listing: any) {
    console.log("PROCEED TO PREVIEW LISTING PAGE", listing);
    this.router.navigate(['/buyer-seller/preview'], {
      queryParams: {
        listingId: listing._id
      }
    });
  }

  proceedToChatSection(listing: any) {
    this.router.navigate(['/buyer-seller/seller-preview'], {
      queryParams: {
        listingId: listing._id
      }
    });
  }
}
