/* Seller Preview Component Styles */

// Add any specific styles for the seller preview component here

.sticky {
  position: sticky;
  top: 1rem;
}

// Animation for search bar
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slideDown {
  animation: slideDown 0.3s ease-out forwards;
}

// Responsive adjustments
@media (max-width: 768px) {
  .sticky {
    position: static;
  }
}
