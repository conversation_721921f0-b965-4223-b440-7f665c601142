const pool = require("../../config/connection");
const bcrypt = require('bcrypt');

// Get user profile
const getProfile = async (req, res) => {
    console.log("getProfile")

    try {
        const userId = req.user.id;

        const [userRows] = await pool.query(
            `SELECT id, name, email, profile_image, country_code, qualification, 
            phone, gender, country, state, city, zipcode, address, created_at 
            FROM users WHERE id = ?`,
            [userId]
        );

        if (userRows.length === 0) {
            return res.status(404).json({
                success: false,
                message: "User not found"
            });
        }

        res.status(200).json({
            success: true,
            message: "User profile retrieved successfully",
            data: userRows[0]
        });
    } catch (error) {
        console.error("Error in getProfile:", error);
        res.status(500).json({
            success: false,
            message: "Internal Server Error",
            error: error.message
        });
    }
};

// Update user profile
const updateProfile = async (req, res) => {
    try {
        const userId = req.user.id;
        const { 
            name, 
            qualification, 
            phone, 
            gender,
            country,
            state,
            city,
            zipcode,
            address 
        } = req.body;

        // Update user profile
        await pool.query(
            `UPDATE users SET 
            name = COALESCE(?, name),
            qualification = COALESCE(?, qualification),
            phone = COALESCE(?, phone),
            gender = COALESCE(?, gender),
            country = COALESCE(?, country),
            state = COALESCE(?, state),
            city = COALESCE(?, city),
            zipcode = COALESCE(?, zipcode),
            address = COALESCE(?, address),
            updated_at = NOW()
            WHERE id = ?`,
            [name, qualification, phone, gender, country, state, city, zipcode, address, userId]
        );

        res.status(200).json({
            success: true,
            message: "Profile updated successfully"
        });
    } catch (error) {
        console.error("Error in updateProfile:", error);
        res.status(500).json({
            success: false,
            message: "Internal Server Error",
            error: error.message
        });
    }
};

// Change password
const changePassword = async (req, res) => {
    try {
        const userId = req.user.id;
        const { currentPassword, newPassword } = req.body;

        if (!currentPassword || !newPassword) {
            return res.status(400).json({
                success: false,
                message: "Current password and new password are required"
            });
        }

        // Get current user's password
        const [userRows] = await pool.query(
            "SELECT password FROM users WHERE id = ?",
            [userId]
        );

        // Verify current password
        const isPasswordValid = await bcrypt.compare(currentPassword, userRows[0].password);
        if (!isPasswordValid) {
            return res.status(401).json({
                success: false,
                message: "Current password is incorrect"
            });
        }

        // Hash new password
        const hashedPassword = await bcrypt.hash(newPassword, 10);

        // Update password
        await pool.query(
            "UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?",
            [hashedPassword, userId]
        );

        res.status(200).json({
            success: true,
            message: "Password changed successfully"
        });
    } catch (error) {
        console.error("Error in changePassword:", error);
        res.status(500).json({
            success: false,
            message: "Internal Server Error",
            error: error.message
        });
    }
};

const createdEvent = async (req, res) => {
    try {
        
    } catch (error) {
        
    }
}


module.exports = {
    getProfile,
    updateProfile,
    changePassword
};