{"version": "6.0", "nxVersion": "21.0.3", "pathMappings": {"@api/*": ["packages/api/src/app/*"], "@lib/angular/*": ["packages/shared/src/lib/angular/*"], "@lib/common/*": ["packages/shared/src/lib/common/*"], "@lib/motors/*": ["packages/shared/src/lib/motors/*"], "@lib/web/*": ["packages/shared/src/lib/web/*"], "@package/api/*": ["packages/api/*"], "@web/*": ["packages/web/src/app/*"]}, "nxJsonPlugins": [{"name": "@nx/webpack/plugin", "options": {"buildTargetName": "build", "serveTargetName": "serve", "previewTargetName": "preview", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}, {"name": "@nx/eslint/plugin", "options": {"targetName": "eslint:lint"}}], "fileMap": {"nonProjectFiles": [{"file": ".n<PERSON><PERSON><PERSON>", "hash": "16012016240695701518"}, {"file": ".editorconfig", "hash": "1719794214401259375"}, {"file": "icons/hdb.svg", "hash": "4879778315910972033"}, {"file": "migrations.json", "hash": "17932201527180181824"}, {"file": "docker/.env", "hash": "4103111998693287251"}, {"file": "scripts/tsconfig.json", "hash": "18353983480440501371"}, {"file": "README.md", "hash": "4659692991663561630"}, {"file": "nx.json", "hash": "11960502819536276182"}, {"file": "scripts/auto-scrap-ethnic-data.ts", "hash": "6815530315244312361"}, {"file": "info/fetch-data.md", "hash": "4639379583200321855"}, {"file": ".vscode/extensions.json", "hash": "9446014251891221554"}, {"file": ".vscode/propped.code-workspace.example.jsonc", "hash": "17536014819460553405"}, {"file": "docker/docker-compose.yml", "hash": "15168511183648633348"}, {"file": "tsconfig.base.json", "hash": "3616764313532700116"}, {"file": "scripts/tsconfig.tools.json", "hash": "6291094936349180704"}, {"file": ".prettier<PERSON>", "hash": "10375883247522981584"}, {"file": "biome.json", "hash": "17584418622908424225"}, {"file": "pnpm-lock.yaml", "hash": "5735207988902611529"}, {"file": "package.json", "hash": "3288989642490114559"}, {"file": "icons/ura-sg.svg", "hash": "3963856173917623599"}, {"file": "seed/main.sql", "hash": "4262107482548979836"}, {"file": ".prettieri<PERSON>re", "hash": "12269743867629339573"}, {"file": ".giti<PERSON>re", "hash": "14617585689560289816"}, {"file": ".ncurc.js", "hash": "14365778501312195668"}, {"file": ".nxignore", "hash": "15036214574332298676"}, {"file": "eslint.config.mjs", "hash": "4615435830663917960"}], "projectFileMap": {"shared": [{"file": "packages/shared/README.md", "hash": "4539284640988790019"}, {"file": "packages/shared/eslint.config.mjs", "hash": "15604680192751622211", "deps": ["npm:@nx/eslint-plugin"]}, {"file": "packages/shared/form-validators.ts", "hash": "883317453427678337"}, {"file": "packages/shared/jest.config.ts", "hash": "10466342925686151471"}, {"file": "packages/shared/project.json", "hash": "16623848886510138559"}, {"file": "packages/shared/src/index.ts", "hash": "2885981295079960460"}, {"file": "packages/shared/src/lib/angular/action-modal.service.ts", "hash": "6077923661997333186", "deps": ["npm:@angular/core", "npm:@angular/material", "npm:rxjs"]}, {"file": "packages/shared/src/lib/angular/alert-modal.service.ts", "hash": "474233207270095077", "deps": ["npm:@angular/core", "npm:@angular/material", "npm:marked", "npm:rxjs"]}, {"file": "packages/shared/src/lib/angular/angular-helpers.ts", "hash": "6980111372925780041", "deps": ["npm:@angular/core"]}, {"file": "packages/shared/src/lib/angular/auth/models/active-user.model.ts", "hash": "18053046600991365281"}, {"file": "packages/shared/src/lib/angular/auth/models/login-module.ts", "hash": "4920169849738744020"}, {"file": "packages/shared/src/lib/angular/aws-image-loader.ts", "hash": "10794717959395029599", "deps": ["npm:@angular/common"]}, {"file": "packages/shared/src/lib/angular/capacitor/qr-scanner-modal.service.ts", "hash": "1606366480364646018", "deps": ["npm:@angular/core", "npm:@angular/material", "npm:rxjs"]}, {"file": "packages/shared/src/lib/angular/capacitor/qr-scanner.service.ts", "hash": "9259870077081080318", "deps": ["npm:@angular/core"]}, {"file": "packages/shared/src/lib/angular/choice-menu.service.ts", "hash": "1657599914817962736", "deps": ["npm:@angular/cdk", "npm:@angular/core", "npm:@angular/material", "npm:rxjs"]}, {"file": "packages/shared/src/lib/angular/common.fun.ts", "hash": "8351181888552820935"}, {"file": "packages/shared/src/lib/angular/common.pipe.ts", "hash": "12069731358084247536", "deps": ["npm:@angular/core", "npm:date-fns"]}, {"file": "packages/shared/src/lib/angular/controller.hook.ts", "hash": "7360004498405087648", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:rxjs"]}, {"file": "packages/shared/src/lib/angular/dialog-align.ts", "hash": "17644853395539406893"}, {"file": "packages/shared/src/lib/angular/directives/content-editable.directive.ts", "hash": "8947991490927282252", "deps": ["npm:@angular/core", "npm:@angular/forms"]}, {"file": "packages/shared/src/lib/angular/directives/life.directive.ts", "hash": "6793658340328583535", "deps": ["npm:@angular/core"]}, {"file": "packages/shared/src/lib/angular/dom-injector.component.ts", "hash": "15471041606778773997", "deps": ["npm:@angular/core"]}, {"file": "packages/shared/src/lib/angular/dynamic-forms/form-group-field.ts", "hash": "13909039037550578293", "deps": ["npm:@angular/cdk", "npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:rxjs"]}, {"file": "packages/shared/src/lib/angular/dynamic-forms/form-modal.service.ts", "hash": "798447743150489544", "deps": ["npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:rxjs"]}, {"file": "packages/shared/src/lib/angular/dynamic-forms/form-validators.ts", "hash": "17687366532959706571", "deps": ["npm:@angular/forms"]}, {"file": "packages/shared/src/lib/angular/dynamic-forms/matrix-field.ts", "hash": "12676482544575236053", "deps": ["npm:@angular/cdk", "npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:rxjs"]}, {"file": "packages/shared/src/lib/angular/dynamic-forms/matrix-form-field.ts", "hash": "689641304549540361", "deps": ["npm:@angular/cdk", "npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:rxjs"]}, {"file": "packages/shared/src/lib/angular/dynamic-forms/profile-form-modal.service.ts", "hash": "3394750764895315466", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:rxjs"]}, {"file": "packages/shared/src/lib/angular/dynamic-forms/select-async-filters.ts", "hash": "3828845302510083977", "deps": ["npm:@angular/forms", "npm:rxjs"]}, {"file": "packages/shared/src/lib/angular/dynamic-forms/select-filters.ts", "hash": "5899141708079342426", "deps": ["npm:@angular/forms", "npm:rxjs"]}, {"file": "packages/shared/src/lib/angular/dynamic-forms/silver-code-field.ts", "hash": "4722064813465276798", "deps": ["npm:@angular/cdk", "npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:rxjs"]}, {"file": "packages/shared/src/lib/angular/dynamic-forms/silver-field.component.ts", "hash": "15362696413486067949", "deps": ["npm:@angular/cdk", "npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:ngx-mat-select-search"]}, {"file": "packages/shared/src/lib/angular/dynamic-forms/silver-select-field.ts", "hash": "12601901969204552109", "deps": ["npm:@angular/cdk", "npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:rxjs"]}, {"file": "packages/shared/src/lib/angular/dynamic-forms/silver-select.service.ts", "hash": "15047416717246974748", "deps": ["npm:@angular/cdk", "npm:@angular/common", "npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:rxjs"]}, {"file": "packages/shared/src/lib/angular/dynamic-forms/text-array-field.ts", "hash": "8192510236693446660", "deps": ["npm:@angular/cdk", "npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:rxjs"]}, {"file": "packages/shared/src/lib/angular/dynamic-forms/time-picker/silver-time-picker.ts", "hash": "3857150078454006625", "deps": ["npm:@angular/cdk", "npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:date-fns", "npm:rxjs"]}, {"file": "packages/shared/src/lib/angular/dynamic-forms/time-picker/time-picker.component.ts", "hash": "3834685655769709108", "deps": ["npm:@angular/core"]}, {"file": "packages/shared/src/lib/angular/dynamic-forms/time-picker/time-picker.service.ts", "hash": "11247073686760203871", "deps": ["npm:@angular/core", "npm:@angular/material", "npm:rxjs"]}, {"file": "packages/shared/src/lib/angular/file-downloader.service.ts", "hash": "1369402710328181156", "deps": ["npm:@angular/core", "npm:@angular/material", "npm:rxjs"]}, {"file": "packages/shared/src/lib/angular/file-picker.service.ts", "hash": "12134259042203089457", "deps": ["npm:@angular/core"]}, {"file": "packages/shared/src/lib/angular/file-uploader.service.ts", "hash": "14840743475622470854", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/material", "npm:rxjs"]}, {"file": "packages/shared/src/lib/angular/fun.ts", "hash": "16041210412946476366", "deps": ["npm:rxjs"]}, {"file": "packages/shared/src/lib/angular/image-average-color.ts", "hash": "13694581803723297318"}, {"file": "packages/shared/src/lib/angular/image.service.ts", "hash": "8582097170978566676", "deps": ["npm:@angular/core", "npm:pica"]}, {"file": "packages/shared/src/lib/angular/letter-avatar.ts", "hash": "1040029191063452108"}, {"file": "packages/shared/src/lib/angular/mat-colors.ts", "hash": "13929922324829511424"}, {"file": "packages/shared/src/lib/angular/mat-icon.component.ts", "hash": "13113471422137237039", "deps": ["npm:@angular/core"]}, {"file": "packages/shared/src/lib/angular/material.helper.ts", "hash": "14575359670642957882"}, {"file": "packages/shared/src/lib/angular/mobile-search-bar.component.ts", "hash": "14352291197750834487", "deps": ["npm:@angular/core", "npm:@angular/forms"]}, {"file": "packages/shared/src/lib/angular/monaco.service.ts", "hash": "16696675179874415130", "deps": ["npm:@angular/core", "npm:@angular/material", "npm:@angular/platform-browser", "npm:rxjs"]}, {"file": "packages/shared/src/lib/angular/preview-modal.service.ts", "hash": "6051820056136873828", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/material", "npm:rxjs"]}, {"file": "packages/shared/src/lib/angular/progress-modal.service.ts", "hash": "11193828285476994868", "deps": ["npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:rxjs"]}, {"file": "packages/shared/src/lib/angular/pwa-manifest.service.ts", "hash": "5380124050461877896", "deps": ["npm:@angular/core"]}, {"file": "packages/shared/src/lib/angular/search-bar.component.ts", "hash": "17075429790827457515", "deps": ["npm:@angular/core", "npm:@angular/forms"]}, {"file": "packages/shared/src/lib/angular/silver-context-menu.service.ts", "hash": "3299259455499718714", "deps": ["npm:@angular/cdk", "npm:@angular/core", "npm:@angular/material", "npm:rxjs"]}, {"file": "packages/shared/src/lib/angular/silver-menu.service.ts", "hash": "12649689890898533099", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/material", "npm:rxjs"]}, {"file": "packages/shared/src/lib/angular/snack-bar.service.ts", "hash": "12647466956341906671", "deps": ["npm:@angular/core", "npm:@angular/material"]}, {"file": "packages/shared/src/lib/angular/splitter.service.ts", "hash": "11586366565553726", "deps": ["npm:@angular/core", "npm:rxjs"]}, {"file": "packages/shared/src/lib/angular/sql.service.ts", "hash": "5675622473144982262", "deps": ["npm:@angular/core", "npm:idb-keyval"]}, {"file": "packages/shared/src/lib/angular/svg-icon.component.ts", "hash": "10526048579741244136", "deps": ["npm:@angular/core"]}, {"file": "packages/shared/src/lib/angular/television-modal.service.ts", "hash": "6931404333330613906", "deps": ["npm:@angular/core", "npm:@angular/material", "npm:rxjs"]}, {"file": "packages/shared/src/lib/angular/temp-global-store.ts", "hash": "3307438082182723412"}, {"file": "packages/shared/src/lib/angular/theming.service.ts", "hash": "10132792242083080747", "deps": ["npm:rxjs"]}, {"file": "packages/shared/src/lib/common/common.error.ts", "hash": "11377594802899147917"}, {"file": "packages/shared/src/lib/common/const/roles.ts", "hash": "12081030551762873478"}, {"file": "packages/shared/src/lib/common/css-minifier.ts", "hash": "1031144367155057541", "deps": ["npm:csso"]}, {"file": "packages/shared/src/lib/common/csv.ts", "hash": "4801792813081508767"}, {"file": "packages/shared/src/lib/common/enums/tfa-status.ts", "hash": "8418674379465540816"}, {"file": "packages/shared/src/lib/common/fun.ts", "hash": "95094037469939480", "deps": ["npm:rxjs"]}, {"file": "packages/shared/src/lib/common/fuzzy-search.ts", "hash": "4820521282184990784", "deps": ["npm:fuzzy-search"]}, {"file": "packages/shared/src/lib/common/http-status.ts", "hash": "8288160856481517762"}, {"file": "packages/shared/src/lib/common/j-data.interface.ts", "hash": "6012370869292032864"}, {"file": "packages/shared/src/lib/common/jsx-dom.ts", "hash": "7823387598026092795", "deps": ["npm:htm", "npm:jsx-dom"]}, {"file": "packages/shared/src/lib/common/material.ts", "hash": "4025020188910114927"}, {"file": "packages/shared/src/lib/common/preact.ts", "hash": "8402412031790636782", "deps": ["npm:@preact/signals", "npm:htm", "npm:preact"]}, {"file": "packages/shared/src/lib/common/script-loader.ts", "hash": "10982212873354926628"}, {"file": "packages/shared/src/lib/common/t-wind.ts", "hash": "8211800486386451372", "deps": ["npm:@twind/core", "npm:@twind/preset-tailwind"]}, {"file": "packages/shared/src/lib/common/tailwind-merge.ts", "hash": "11253406545127474597", "deps": ["npm:clsx", "npm:tailwind-merge"]}, {"file": "packages/shared/src/lib/common/test-root-service.ts", "hash": "11732864579668862794"}, {"file": "packages/shared/src/lib/shared.ts", "hash": "7390454490555929958"}, {"file": "packages/shared/src/lib/web/audio-buffer-player.service.ts", "hash": "6236541232992363207"}, {"file": "packages/shared/src/lib/web/file.fun.ts", "hash": "1667869849521373701"}, {"file": "packages/shared/src/lib/web/v-html--p.ts", "hash": "8801486972854525307", "deps": ["npm:htm"]}, {"file": "packages/shared/src/lib/web/v-html.ts", "hash": "13941951140820800968", "deps": ["npm:htm"]}, {"file": "packages/shared/tsconfig.json", "hash": "8470976872707230433"}, {"file": "packages/shared/tsconfig.lib.json", "hash": "5899960159110799710"}, {"file": "packages/shared/tsconfig.spec.json", "hash": "10830686744819689279"}], "web": [{"file": "packages/web/eslint.config.mjs", "hash": "15358270705171202087", "deps": ["npm:@nx/eslint-plugin"]}, {"file": "packages/web/one_map_response_json", "hash": "14520762966494993402"}, {"file": "packages/web/project.json", "hash": "11605331351484868203"}, {"file": "packages/web/proxy.conf.json", "hash": "1357254922164409877"}, {"file": "packages/web/public/assets/condo-sales.sqlite", "hash": "5959278091147282497"}, {"file": "packages/web/public/assets/dummy-host-1.png", "hash": "2768361651342070163"}, {"file": "packages/web/public/assets/dummy-host-2.png", "hash": "2088270174361661170"}, {"file": "packages/web/public/assets/dummy-host-3.png", "hash": "14827795308152559673"}, {"file": "packages/web/public/assets/dummy-host-4.png", "hash": "6907682104051050187"}, {"file": "packages/web/public/assets/dummy_prop.png", "hash": "15276898988246684631"}, {"file": "packages/web/public/assets/floor-plan-1.jpg", "hash": "12416349208241755736"}, {"file": "packages/web/public/assets/hdb-sales.sqlite", "hash": "17206946777466408522"}, {"file": "packages/web/public/assets/hdbResaleIndex.png", "hash": "16813245386858741596"}, {"file": "packages/web/public/assets/properties/balcony-1.jpg", "hash": "17170969855133089597"}, {"file": "packages/web/public/assets/properties/balcony-2.jpg", "hash": "4417131823970558958"}, {"file": "packages/web/public/assets/properties/balcony-3.jpg", "hash": "14779515193648434900"}, {"file": "packages/web/public/assets/properties/bedroom-1.jpg", "hash": "8028105548315504290"}, {"file": "packages/web/public/assets/properties/bedroom-2.jpg", "hash": "1715683939068466110"}, {"file": "packages/web/public/assets/properties/kitchen-1.jpg", "hash": "6111277559682164056"}, {"file": "packages/web/public/assets/properties/kitchen-2.jpg", "hash": "4440838226331919996"}, {"file": "packages/web/public/assets/properties/living-room-1.jpg", "hash": "7423427902118118124"}, {"file": "packages/web/public/assets/properties/living-room-2.jpg", "hash": "4384307062344731258"}, {"file": "packages/web/public/assets/properties/master-bedroom.jpg", "hash": "6152723137733722520"}, {"file": "packages/web/src/app/app.component.ts", "hash": "16158325391404446544", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/router", "shared"]}, {"file": "packages/web/src/app/app.config.ts", "hash": "5722769806788250526", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:@angular/platform-browser", "npm:@angular/router", "shared", "npm:ngx-mapbox-gl"]}, {"file": "packages/web/src/app/app.routes.ts", "hash": "15845482079866586220", "deps": ["npm:@angular/router"]}, {"file": "packages/web/src/app/common/common.module.ts", "hash": "7234168037643405211", "deps": ["npm:@angular/common", "npm:@angular/core", "shared"]}, {"file": "packages/web/src/app/component/annotation/annotation-modal.component.ts", "hash": "12951922694810116600", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/material"]}, {"file": "packages/web/src/app/component/annotation/annotation-modal.service.ts", "hash": "12486328121633991595", "deps": ["npm:@angular/core", "npm:@angular/material", "npm:rxjs"]}, {"file": "packages/web/src/app/component/data-chart/data-chart.component.ts", "hash": "2889124824976287817", "deps": ["npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:@angular/router", "shared", "npm:chart.js", "npm:rxjs"]}, {"file": "packages/web/src/app/component/data-chart/dual-data-chart.component.ts", "hash": "4768431432331584456", "deps": ["npm:@angular/core", "npm:@angular/material", "shared", "npm:chart.js", "npm:rxjs"]}, {"file": "packages/web/src/app/component/data-chart/stacked-chart.component.ts", "hash": "13599765595633437687", "deps": ["npm:@angular/core", "npm:chart.js"]}, {"file": "packages/web/src/app/component/data-chart/stacked-table.component.ts", "hash": "3629283676583971984", "deps": ["npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:@angular/router", "shared", "npm:rxjs"]}, {"file": "packages/web/src/app/component/resale-trend-chart/resale-trend-chart.component.ts", "hash": "12103108960105396754", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:chart.js"]}, {"file": "packages/web/src/app/guard/user-login.guard.ts", "hash": "13701158811543154528", "deps": ["npm:@angular/core", "npm:@angular/router", "shared"]}, {"file": "packages/web/src/app/interceptors/auth.interceptor.ts", "hash": "5682623618009183378", "deps": ["npm:@angular/common", "npm:@angular/core", "shared", "npm:rxjs"]}, {"file": "packages/web/src/app/layout/main/animations.ts", "hash": "1123763619940524501", "deps": ["npm:@angular/animations"]}, {"file": "packages/web/src/app/layout/main/header/header.component.html", "hash": "7547716988027081772"}, {"file": "packages/web/src/app/layout/main/header/header.component.scss", "hash": "17485618261077241938"}, {"file": "packages/web/src/app/layout/main/header/header.component.ts", "hash": "7050087125633412417", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/material", "api", "shared", "npm:rxjs"]}, {"file": "packages/web/src/app/layout/main/main.component.ts", "hash": "11916286279318335540", "deps": ["npm:@angular/core", "npm:@angular/material", "npm:@angular/router", "npm:rxjs"]}, {"file": "packages/web/src/app/layout/main/sidenav/sidenav.component.html", "hash": "3187049667177409038"}, {"file": "packages/web/src/app/layout/main/sidenav/sidenav.component.ts", "hash": "1234441096708711923", "deps": ["npm:@angular/core", "npm:@angular/material", "npm:@angular/router", "shared"]}, {"file": "packages/web/src/app/layout/main/widgets/widgets.component.html", "hash": "7735907695943930845"}, {"file": "packages/web/src/app/layout/main/widgets/widgets.component.ts", "hash": "7455125749750388581", "deps": ["npm:@angular/core", "npm:@angular/material"]}, {"file": "packages/web/src/app/pages/account/account.component.html", "hash": "13843427525104987384"}, {"file": "packages/web/src/app/pages/account/account.component.ts", "hash": "8833037943025025118", "deps": ["npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "api", "shared", "npm:rxjs"]}, {"file": "packages/web/src/app/pages/account/totp.ts", "hash": "9583237181983644485", "deps": ["npm:otpa<PERSON>", "npm:qrcode"]}, {"file": "packages/web/src/app/pages/auth/auth.component.html", "hash": "16992564636749297962"}, {"file": "packages/web/src/app/pages/auth/auth.component.ts", "hash": "15682708691329352768", "deps": ["npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:@angular/router", "shared"]}, {"file": "packages/web/src/app/pages/auth/auth.routes.ts", "hash": "16995437723769221091", "deps": ["npm:@angular/router"]}, {"file": "packages/web/src/app/pages/buyer-seller/buyer-sales-listing/buyer-sales-listing.component.html", "hash": "12951991037443809988"}, {"file": "packages/web/src/app/pages/buyer-seller/buyer-sales-listing/buyer-sales-listing.component.ts", "hash": "11342912941957763895", "deps": ["npm:@angular/core", "npm:@angular/router", "api", "npm:@angular/common", "shared"]}, {"file": "packages/web/src/app/pages/buyer-seller/buyer-seller.component.ts", "hash": "8073800688855720355", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/material", "npm:@angular/router"]}, {"file": "packages/web/src/app/pages/buyer-seller/buyer-seller.routes.ts", "hash": "5207357109585170053", "deps": ["npm:@angular/router"]}, {"file": "packages/web/src/app/pages/buyer-seller/make-offer-buyer/buyer-info/buyer-info.component.ts", "hash": "62373119680196466", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/material"]}, {"file": "packages/web/src/app/pages/buyer-seller/make-offer-buyer/make-offer-buyer.component.html", "hash": "2952322425146762334"}, {"file": "packages/web/src/app/pages/buyer-seller/make-offer-buyer/make-offer-buyer.component.ts", "hash": "1642724388301981189", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:@angular/router", "api", "shared"]}, {"file": "packages/web/src/app/pages/buyer-seller/make-offer-buyer/offer-sent-dialog.component.ts", "hash": "4941369698975340164", "deps": ["npm:@angular/core", "npm:@angular/router", "npm:@angular/material", "npm:@angular/common"]}, {"file": "packages/web/src/app/pages/buyer-seller/make-offer-buyer/seller-info/seller-info.component.ts", "hash": "12073157049289256207", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/material"]}, {"file": "packages/web/src/app/pages/buyer-seller/offer-price-estimator/offer-price-estimator.component.html", "hash": "9471457876380828777"}, {"file": "packages/web/src/app/pages/buyer-seller/offer-price-estimator/offer-price-estimator.component.ts", "hash": "2020954185609289709", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:@angular/router"]}, {"file": "packages/web/src/app/pages/buyer-seller/preview-listing/preview-listing-seller-dialog.component.ts", "hash": "16987964264092566365", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/router", "npm:@angular/material"]}, {"file": "packages/web/src/app/pages/buyer-seller/preview-listing/preview-listing.component.html", "hash": "9027775245754870848"}, {"file": "packages/web/src/app/pages/buyer-seller/preview-listing/preview-listing.component.ts", "hash": "16450745889081652401", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:@angular/router", "api", "shared"]}, {"file": "packages/web/src/app/pages/buyer-seller/property-address/property-address.component.ts", "hash": "14331876293892982007", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:@angular/router", "api"]}, {"file": "packages/web/src/app/pages/buyer-seller/property-details/property-details.component.html", "hash": "15418460947910793804"}, {"file": "packages/web/src/app/pages/buyer-seller/property-details/property-details.component.ts", "hash": "2015732816052490058", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:@angular/router"]}, {"file": "packages/web/src/app/pages/buyer-seller/property-photos/property-photos.component.html", "hash": "1227409074054272310"}, {"file": "packages/web/src/app/pages/buyer-seller/property-photos/property-photos.component.ts", "hash": "5380171587722544450", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:@angular/router"]}, {"file": "packages/web/src/app/pages/buyer-seller/sale-conditions/sale-conditions.component.html", "hash": "6365174784037213526"}, {"file": "packages/web/src/app/pages/buyer-seller/sale-conditions/sale-conditions.component.ts", "hash": "8684447675375058109", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:@angular/router"]}, {"file": "packages/web/src/app/pages/buyer-seller/seller-preview/seller-preview.component.html", "hash": "12886503796816764581"}, {"file": "packages/web/src/app/pages/buyer-seller/seller-preview/seller-preview.component.scss", "hash": "1583704752865712154"}, {"file": "packages/web/src/app/pages/buyer-seller/seller-preview/seller-preview.component.ts", "hash": "11355673709388872220", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:@angular/router", "api", "shared"]}, {"file": "packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.html", "hash": "1384168978288190492"}, {"file": "packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts", "hash": "12411285799078358612", "deps": ["npm:@angular/core", "npm:@angular/router", "api", "npm:@angular/common", "shared"]}, {"file": "packages/web/src/app/pages/buyer-seller/selling-price/selling-price.component.html", "hash": "5294124059790167290"}, {"file": "packages/web/src/app/pages/buyer-seller/selling-price/selling-price.component.ts", "hash": "9327310298710827310", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:@angular/router"]}, {"file": "packages/web/src/app/pages/condo-sales/condo-sales.component.html", "hash": "7254316477251519119"}, {"file": "packages/web/src/app/pages/condo-sales/condo-sales.component.ts", "hash": "2523634438935220380", "deps": ["npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "shared"]}, {"file": "packages/web/src/app/pages/condo-sales/condo-sales.routes.ts", "hash": "7113832687821242451", "deps": ["npm:@angular/router"]}, {"file": "packages/web/src/app/pages/condo-sales/condo-sales.service.ts", "hash": "16832075354655962251", "deps": ["npm:@angular/core", "shared"]}, {"file": "packages/web/src/app/pages/condo-transactions/condo-transaction.component.html", "hash": "7400008376010658062"}, {"file": "packages/web/src/app/pages/condo-transactions/condo-transaction.component.ts", "hash": "3157801198027042903", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "api", "shared", "npm:mapbox-gl", "npm:ngx-mapbox-gl"]}, {"file": "packages/web/src/app/pages/condo-transactions/condo-transaction.routes.ts", "hash": "12057882111097433770", "deps": ["npm:@angular/router"]}, {"file": "packages/web/src/app/pages/condo-transactions/condo-transaction.service.ts", "hash": "9241994510399168569", "deps": ["npm:@angular/core", "shared"]}, {"file": "packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html", "hash": "8991676539119229182"}, {"file": "packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.html", "hash": "3622039070227384945"}, {"file": "packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts", "hash": "18000283644769133832", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/forms", "api", "shared"]}, {"file": "packages/web/src/app/pages/hdb-sales/hdb-queries.ts", "hash": "823122797125340135"}, {"file": "packages/web/src/app/pages/hdb-sales/hdb-sales.component.html", "hash": "16504760320924182202"}, {"file": "packages/web/src/app/pages/hdb-sales/hdb-sales.component.ts", "hash": "7946691436261670390", "deps": ["npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "shared", "npm:rxjs"]}, {"file": "packages/web/src/app/pages/hdb-sales/hdb-sales.routes.ts", "hash": "15639229092411275160", "deps": ["npm:@angular/router"]}, {"file": "packages/web/src/app/pages/hdb-sales/hdb-sales.service.ts", "hash": "7460757120927361215", "deps": ["npm:@angular/core", "shared"]}, {"file": "packages/web/src/app/pages/hdb-transactions/hdb-queries.ts", "hash": "17105354044089485907"}, {"file": "packages/web/src/app/pages/hdb-transactions/hdb-transaction.component.html", "hash": "17008792899725181776"}, {"file": "packages/web/src/app/pages/hdb-transactions/hdb-transaction.component.ts", "hash": "8138066473941921348", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "api", "shared", "npm:mapbox-gl", "npm:ngx-mapbox-gl", "npm:rxjs"]}, {"file": "packages/web/src/app/pages/hdb-transactions/hdb-transaction.routes.ts", "hash": "18030595141467567088", "deps": ["npm:@angular/router"]}, {"file": "packages/web/src/app/pages/hdb-transactions/hdb-transaction.service.ts", "hash": "5997328271178193150", "deps": ["npm:@angular/core", "shared"]}, {"file": "packages/web/src/app/pages/mapbox-overview/map-box-overview.component.html", "hash": "12395773901940968106"}, {"file": "packages/web/src/app/pages/mapbox-overview/map-box-overview.component.ts", "hash": "17075099634726956351", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:@angular/router", "api", "shared", "npm:chart.js", "npm:mapbox-gl", "npm:ngx-mapbox-gl"]}, {"file": "packages/web/src/app/pages/mapbox-overview/sales-listing-data.ts", "hash": "3927164004148750884"}, {"file": "packages/web/src/app/pages/overview/overview.component.html", "hash": "11979541767480983009"}, {"file": "packages/web/src/app/pages/overview/overview.component.ts", "hash": "6394194481016826519", "deps": ["npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "api", "shared", "npm:rxjs"]}, {"file": "packages/web/src/app/pages/overview/overview.routes.ts", "hash": "14265665437515322755", "deps": ["npm:@angular/router"]}, {"file": "packages/web/src/app/pages/overview/property-modal.service.tsx", "hash": "10510255091230223184", "deps": ["npm:@angular/core", "shared", "npm:chart.js", "npm:preact", "npm:rxjs"]}, {"file": "packages/web/src/app/pages/overview/proximity-search.service.tsx", "hash": "1109941227690643504", "deps": ["npm:@angular/core", "shared", "npm:preact", "npm:rxjs"]}, {"file": "packages/web/src/app/pages/overview/sales-listing.service.tsx", "hash": "16266100902153289637", "deps": ["npm:@angular/core", "shared", "npm:preact", "npm:rxjs"]}, {"file": "packages/web/src/app/pages/overview/transaction-table.component.ts", "hash": "15036415720621269755", "deps": ["npm:@angular/core"]}, {"file": "packages/web/src/app/pages/overview/virtual-scroll.tsx", "hash": "4409288689041595451", "deps": ["npm:preact"]}, {"file": "packages/web/src/app/pages/property-map/property-map.component.html", "hash": "1440660843166263493"}, {"file": "packages/web/src/app/pages/property-map/property-map.component.ts", "hash": "5750282410198101223", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:@angular/router", "api", "shared", "npm:chart.js", "npm:mapbox-gl", "npm:ngx-mapbox-gl"]}, {"file": "packages/web/src/app/pages/uac/uac.component.html", "hash": "15348891311171871685"}, {"file": "packages/web/src/app/pages/uac/uac.component.ts", "hash": "16573694320785264941", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/material", "api", "shared", "npm:change-case"]}, {"file": "packages/web/src/app/pages/uac/uac.routes.ts", "hash": "12534877556010320522", "deps": ["npm:@angular/router"]}, {"file": "packages/web/src/app/pages/user-dashboard/user-dashboard.component.html", "hash": "4007391084004614724"}, {"file": "packages/web/src/app/pages/user-dashboard/user-dashboard.component.ts", "hash": "867844645233710201", "deps": ["npm:@angular/core", "npm:@angular/material", "npm:@angular/common", "npm:@angular/forms", "npm:@angular/router", "api"]}, {"file": "packages/web/src/app/pages/users/users.component.html", "hash": "525486177785197705"}, {"file": "packages/web/src/app/pages/users/users.component.ts", "hash": "8624419136940339933", "deps": ["npm:@angular/core", "npm:@angular/material", "api", "shared", "npm:rxjs"]}, {"file": "packages/web/src/app/pages/users/users.routes.ts", "hash": "8929566823241782302", "deps": ["npm:@angular/router"]}, {"file": "packages/web/src/app/pages/users/users.service.ts", "hash": "159939805368701461", "deps": ["npm:@angular/core", "shared"]}, {"file": "packages/web/src/app/services/auth.service.ts", "hash": "3734128232049300906", "deps": ["npm:@angular/core", "npm:@angular/forms", "api", "shared"]}, {"file": "packages/web/src/app/services/compress.worker.ts", "hash": "12965707319435950406"}, {"file": "packages/web/src/app/services/design/match-media.service.ts", "hash": "1006589466908323778", "deps": ["npm:@angular/cdk", "npm:@angular/core", "shared", "npm:rxjs"]}, {"file": "packages/web/src/app/services/design/sidebar-menu.service.ts", "hash": "14242022463873662378", "deps": ["shared"]}, {"file": "packages/web/src/app/services/design/sidenav.service.ts", "hash": "17741285211698537606", "deps": ["npm:@angular/core", "npm:@angular/material", "shared", "npm:rxjs"]}, {"file": "packages/web/src/app/services/design/splash-screen.service.ts", "hash": "4578143286526086343", "deps": ["npm:@angular/animations", "npm:@angular/common", "npm:@angular/core", "npm:@angular/router", "shared", "npm:rxjs"]}, {"file": "packages/web/src/app/services/file-compress.service.ts", "hash": "3258995749552160057", "deps": ["npm:@angular/core", "shared", "npm:rxjs"]}, {"file": "packages/web/src/app/services/loader.service.ts", "hash": "9733155172205885504", "deps": ["npm:rxjs"]}, {"file": "packages/web/src/app/services/maps.service.ts", "hash": "12696191686377439520", "deps": ["npm:@angular/core", "npm:@angular/platform-browser", "shared"]}, {"file": "packages/web/src/app/services/permission.service.ts", "hash": "5974752486998873195"}, {"file": "packages/web/src/app/services/sse.service.ts", "hash": "2218961075886343749", "deps": ["npm:rxjs"]}, {"file": "packages/web/src/app/services/temp-cache.service.ts", "hash": "2553321730385696690", "deps": ["npm:@angular/core"]}, {"file": "packages/web/src/app/services/users.service.ts", "hash": "1058879105629737734", "deps": ["npm:@angular/core", "api", "shared"]}, {"file": "packages/web/src/app/services/worker-magic.ts", "hash": "16087418648923799311"}, {"file": "packages/web/src/app/shared/date-format.config.ts", "hash": "9971904352006379349", "deps": ["npm:@angular/material-date-fns-adapter", "npm:@angular/material", "npm:date-fns"]}, {"file": "packages/web/src/app/shared/fun.ts", "hash": "3085077245347324435"}, {"file": "packages/web/src/app/shared/select-filters.ts", "hash": "13327489630502447017", "deps": ["npm:@angular/forms", "shared", "npm:rxjs"]}, {"file": "packages/web/src/bootstrap.ts", "hash": "10408026521224780869", "deps": ["npm:@angular/platform-browser", "shared"]}, {"file": "packages/web/src/environments/.gitignore", "hash": "13832403774524269684"}, {"file": "packages/web/src/environments/environment.eg.ts", "hash": "5728268939971358411"}, {"file": "packages/web/src/index.html", "hash": "15487400052610914989"}, {"file": "packages/web/src/main.ts", "hash": "1930442019803562757"}, {"file": "packages/web/src/polyfills.ts", "hash": "15717161745886097300"}, {"file": "packages/web/src/styles.scss", "hash": "5819546372759609993"}, {"file": "packages/web/src/styles/animation.scss", "hash": "3531089579676825485"}, {"file": "packages/web/src/styles/dialog.scss", "hash": "4474369799945812160"}, {"file": "packages/web/src/styles/material.scss", "hash": "13494475058530263468"}, {"file": "packages/web/src/styles/scroll-bars.scss", "hash": "9950935908676151239"}, {"file": "packages/web/tailwind.config.mjs", "hash": "6663866678146867665", "deps": ["npm:@nx/angular", "npm:da<PERSON><PERSON>i"]}, {"file": "packages/web/tsconfig.app.json", "hash": "7861138828500525160"}, {"file": "packages/web/tsconfig.editor.json", "hash": "9424779124609718922"}, {"file": "packages/web/tsconfig.json", "hash": "14893246478980989041"}], "api": [{"file": "packages/api/esbuild.config.ts", "hash": "513280227367258092", "deps": ["npm:esbuild"]}, {"file": "packages/api/eslint.config.mjs", "hash": "16364332075775709585"}, {"file": "packages/api/project.json", "hash": "9019836789719105221"}, {"file": "packages/api/src/api.server.ts", "hash": "17061040952679317966", "deps": ["shared", "npm:hono"]}, {"file": "packages/api/src/app/common/active-user.ts", "hash": "871366520242252639", "deps": ["shared"]}, {"file": "packages/api/src/app/common/db.ts", "hash": "5199986434029748602", "deps": ["npm:drizzle-orm", "npm:mongoose"]}, {"file": "packages/api/src/app/common/drizzle-helper.ts", "hash": "17135558742411166301", "deps": ["npm:drizzle-orm"]}, {"file": "packages/api/src/app/common/fun.ts", "hash": "15889305340590643122"}, {"file": "packages/api/src/app/common/marked.ts", "hash": "1940955626183902140", "deps": ["npm:marked", "npm:marked-xhtml"]}, {"file": "packages/api/src/app/common/paginator.ts", "hash": "4843681891818485915", "deps": ["shared", "npm:drizzle-orm"]}, {"file": "packages/api/src/app/common/q-gen.ts", "hash": "3688943923831786255"}, {"file": "packages/api/src/app/common/tables.const.ts", "hash": "8925429474886786478"}, {"file": "packages/api/src/app/controllers.ts", "hash": "16993998508262621142"}, {"file": "packages/api/src/app/controllers/auth/auth.controller.ts", "hash": "11375824012995639554", "deps": ["shared", "npm:date-fns"]}, {"file": "packages/api/src/app/controllers/auth/auth.entity.ts", "hash": "1520583923024768294", "deps": ["npm:drizzle-orm"]}, {"file": "packages/api/src/app/controllers/auth/auth.service.ts", "hash": "8882907488822606343", "deps": ["shared", "npm:date-fns", "npm:drizzle-orm", "npm:j<PERSON><PERSON><PERSON><PERSON>", "npm:otpa<PERSON>"]}, {"file": "packages/api/src/app/controllers/auth/files.entity.ts", "hash": "14034822938947764243", "deps": ["npm:drizzle-orm"]}, {"file": "packages/api/src/app/controllers/auth/roles.service.ts", "hash": "14279935822368852680", "deps": ["npm:drizzle-orm"]}, {"file": "packages/api/src/app/controllers/bus-stops/bus-stops.controller.ts", "hash": "13461370607456020220"}, {"file": "packages/api/src/app/controllers/bus-stops/bus-stops.model.ts", "hash": "11329772215921928108", "deps": ["npm:mongoose"]}, {"file": "packages/api/src/app/controllers/condo/condo-project-type.model.ts", "hash": "4152588863451232908", "deps": ["npm:mongoose"]}, {"file": "packages/api/src/app/controllers/condo/condo-transaction-test.model.ts", "hash": "7325696350392029171", "deps": ["npm:mongoose"]}, {"file": "packages/api/src/app/controllers/condo/condo.controller.ts", "hash": "5350147189314829125", "deps": ["npm:mongoose"]}, {"file": "packages/api/src/app/controllers/condo/condo.model.ts", "hash": "12318361995994234206", "deps": ["npm:mongoose"]}, {"file": "packages/api/src/app/controllers/hawker/hawker-center.model.ts", "hash": "2509372583417870417", "deps": ["npm:mongoose"]}, {"file": "packages/api/src/app/controllers/hawker/hawker.controller.ts", "hash": "16469588303677770970"}, {"file": "packages/api/src/app/controllers/hdb-sales/hdb-sales-offer.controller.ts", "hash": "2757914186967091317"}, {"file": "packages/api/src/app/controllers/hdb-sales/hdb-sales-offer.model.ts", "hash": "3337273320767355725", "deps": ["npm:mongoose"]}, {"file": "packages/api/src/app/controllers/hdb-sales/hdb-sales.controller.ts", "hash": "369934663460254659"}, {"file": "packages/api/src/app/controllers/hdb-sales/hdb-sales.model.ts", "hash": "11310835801323827835", "deps": ["npm:mongoose"]}, {"file": "packages/api/src/app/controllers/hdb-sales/sales-chat.controller.ts", "hash": "8066218314470453649"}, {"file": "packages/api/src/app/controllers/hdb-sales/sales-chat.model.ts", "hash": "10515299404794902505", "deps": ["npm:mongoose"]}, {"file": "packages/api/src/app/controllers/hdb-sales/transaction-resale-progress.controller.ts", "hash": "7100654881171943841", "deps": ["npm:mongoose"]}, {"file": "packages/api/src/app/controllers/hdb-sales/transaction-resale-progress.model.ts", "hash": "8204876534086318488", "deps": ["npm:mongoose"]}, {"file": "packages/api/src/app/controllers/hdb/check-ethnic-quota.fun.ts", "hash": "16021399406735435548", "deps": ["shared", "npm:fetch-cookie"]}, {"file": "packages/api/src/app/controllers/hdb/hdb-apartment-type.model.ts", "hash": "17752839586346912099", "deps": ["npm:mongoose"]}, {"file": "packages/api/src/app/controllers/hdb/hdb-building-type.model.ts", "hash": "17523880834163860336", "deps": ["npm:mongoose"]}, {"file": "packages/api/src/app/controllers/hdb/hdb-towns.model.ts", "hash": "17100839304776652725", "deps": ["npm:mongoose"]}, {"file": "packages/api/src/app/controllers/hdb/hdb-transaction-dump.model.ts", "hash": "13387100050185372061", "deps": ["npm:mongoose"]}, {"file": "packages/api/src/app/controllers/hdb/hdb-transaction-test.model.ts", "hash": "1111415177088278081", "deps": ["npm:mongoose"]}, {"file": "packages/api/src/app/controllers/hdb/hdb.controller.ts", "hash": "17703952508934596579", "deps": ["npm:mongoose"]}, {"file": "packages/api/src/app/controllers/hdb/hdb.model.ts", "hash": "3862520159977679548", "deps": ["npm:mongoose"]}, {"file": "packages/api/src/app/controllers/hospitals/hospital.controller.ts", "hash": "10680428182889342190"}, {"file": "packages/api/src/app/controllers/hospitals/hospitals.model.ts", "hash": "15080393989097600406", "deps": ["npm:mongoose"]}, {"file": "packages/api/src/app/controllers/metro/metro.controller.ts", "hash": "5133691798687108781"}, {"file": "packages/api/src/app/controllers/metro/metro.model.ts", "hash": "5293972544807581256", "deps": ["npm:mongoose"]}, {"file": "packages/api/src/app/controllers/one-map/one-map-search.controller.ts", "hash": "17801571675867413452"}, {"file": "packages/api/src/app/controllers/sales-listings/user-sales-listing.controller.ts", "hash": "209136923035931168"}, {"file": "packages/api/src/app/controllers/sales-listings/user-sales-listing.model.ts", "hash": "14615621152142357897", "deps": ["npm:mongoose"]}, {"file": "packages/api/src/app/controllers/schools/kindergarten-school.model.ts", "hash": "10103871783237369670", "deps": ["npm:mongoose"]}, {"file": "packages/api/src/app/controllers/schools/school.controller.ts", "hash": "14671504082509452285"}, {"file": "packages/api/src/app/controllers/schools/school.model.ts", "hash": "11234173206347894408", "deps": ["npm:mongoose"]}, {"file": "packages/api/src/app/controllers/settings/settings.controller.ts", "hash": "16776359846933610452"}, {"file": "packages/api/src/app/controllers/settings/settings.entity.ts", "hash": "13186351197273147699", "deps": ["npm:drizzle-orm"]}, {"file": "packages/api/src/app/data/salesData.json", "hash": "7083364334767686368"}, {"file": "packages/api/src/app/guards/auth.guard.ts", "hash": "9675790580157815850", "deps": ["npm:j<PERSON><PERSON><PERSON><PERSON>"]}, {"file": "packages/api/src/app/guards/guards.ts", "hash": "5784433473882903933", "deps": ["npm:hono"]}, {"file": "packages/api/src/app/guards/rate-limit.guard.ts", "hash": "18370154428064835154", "deps": ["npm:@hono/node-server", "shared"]}, {"file": "packages/api/src/app/hooks/auth.hooks.ts", "hash": "15804269114331236900", "deps": ["npm:hono"]}, {"file": "packages/api/src/app/hooks/ctx.hooks.ts", "hash": "2622460151366743893", "deps": ["npm:hono"]}, {"file": "packages/api/src/app/models/metro-station.model.ts", "hash": "11256095362361117944", "deps": ["npm:mongoose"]}, {"file": "packages/api/src/app/receptor/compression.receptor.ts", "hash": "17946085706716001948", "deps": ["npm:hono"]}, {"file": "packages/api/src/app/receptor/receptors.ts", "hash": "12280336182280697838", "deps": ["npm:hono"]}, {"file": "packages/api/src/app/rpc.ts", "hash": "16376889882708466773", "deps": ["shared"]}, {"file": "packages/api/src/app/services/encryption.service.ts", "hash": "14363895319600871710"}, {"file": "packages/api/src/app/services/file-mgr.service.ts", "hash": "6527807822389880812", "deps": ["npm:@aws-sdk/client-s3", "npm:@aws-sdk/s3-request-presigner", "npm:@aws-sdk/types", "npm:aws-sdk", "npm:drizzle-orm"]}, {"file": "packages/api/src/app/services/hash.service.ts", "hash": "3982458526067189695"}, {"file": "packages/api/src/app/services/mail.service.ts", "hash": "13752066625176313593", "deps": ["shared", "npm:nodemailer"]}, {"file": "packages/api/src/app/services/mysql-json.service.ts", "hash": "2755480201765449003", "deps": ["npm:drizzle-orm"]}, {"file": "packages/api/src/app/services/services.register.ts", "hash": "5243302946156001654"}, {"file": "packages/api/src/app/templates/jsx/mail.template.tsx", "hash": "12169744240690373891", "deps": ["shared"]}, {"file": "packages/api/src/app/templates/jsx/twind-processor.ts", "hash": "16692153661304934766", "deps": ["npm:@twind/core", "npm:@twind/preset-tailwind"]}, {"file": "packages/api/src/environments/.gitignore", "hash": "16942753468364701546"}, {"file": "packages/api/src/environments/env-modules.ts", "hash": "1617675109020587056", "deps": ["npm:mysql2"]}, {"file": "packages/api/src/environments/environment.eg.ts", "hash": "12685557430169274821", "deps": ["npm:chalk"]}, {"file": "packages/api/src/errors/common.error.ts", "hash": "11853207801626925025", "deps": ["shared"]}, {"file": "packages/api/src/main.ts", "hash": "261116858366040120", "deps": ["npm:@hono/node-server", "shared", "npm:hono"]}, {"file": "packages/api/tsconfig.app.json", "hash": "1994540794194953386"}, {"file": "packages/api/tsconfig.json", "hash": "10933117233973928844"}]}}}