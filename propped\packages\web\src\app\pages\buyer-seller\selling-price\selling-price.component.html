<div class="w-full p-4">
  <div class="rounded-lg shadow-lg p-6 border border-blue-100">
    <!-- Main Content -->
    <div class="space-y-6">
      <!-- 1. Set Selling Price -->
      <div class="border border-blue-100 rounded-lg p-6 shadow-sm">
        <h3 class="text-lg font-semibold mb-2">Set Selling Price</h3>
        <div class="flex items-center">
          <span class="text-lg font-medium mr-2">S$</span>
          <input
            type="text"
            class="w-full p-3 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            [(ngModel)]="manualSellingPrice"
            (input)="formatCurrency('manual')"
          />
        </div>
        <p class="text-sm mt-2">
          Not sure what price to set, use our calculator or HDB transaction analysis tool to help
          give you a better idea.
        </p>
      </div>

      <!-- 2. Selling <PERSON> Estimator -->
      <div class="border border-blue-100 rounded-lg p-6 shadow-sm">
        <h3 class="text-xl font-semibold mb-4">Selling Price Estimator</h3>

        <!-- Target PSF Method -->
        <div class="mb-6">
          <h4 class="text-lg font-semibold mb-2">Target PSF Method</h4>
          <p class="text-sm mb-4">Enter either Target Selling PSF or Target Selling Price</p>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <p class="block text-sm font-medium mb-1">Your Unit Size</p>
              <div class="flex">
                <input
                  type="text"
                  class="flex-grow p-2 border border-gray-300 rounded-l focus:outline-none focus:ring-2 focus:ring-blue-500"
                  [(ngModel)]="unitSize"
                  (input)="calculateFromUnitSize()"
                />
                <span
                  class="bg-gray-100 px-3 py-2 border border-l-0 border-gray-300 rounded-r text-gray-600"
                  >Sq Ft</span
                >
              </div>
            </div>
            <div class="flex items-end">
              <button
                class="px-4 py-2 text-blue-600 border border-blue-600 rounded hover:bg-blue-50"
              >
                Edit
              </button>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div>
              <p class="block text-sm font-medium mb-1">Target Selling Price PSF</p>
              <div class="flex">
                <span
                  class="bg-gray-100 px-3 py-2 border border-r-0 border-gray-300 rounded-l dark:text-black"
                  >$</span
                >
                <input
                  type="text"
                  class="flex-grow p-2 border border-gray-300 rounded-r focus:outline-none focus:ring-2 focus:ring-blue-500"
                  [(ngModel)]="targetPSF"
                  (input)="calculateFromPSF()"
                />
              </div>
            </div>
            <div class="flex items-center justify-center">
              <span class="text-gray-500">Or</span>
            </div>
            <div>
              <p class="block text-sm font-medium text-gray-700 mb-1">Target Selling Price</p>
              <div class="flex">
                <span
                  class="px-3 py-2 bg-gray-100 border border-r-0 border-gray-300 rounded-l dark:text-black"
                  >$</span
                >
                <input
                  type="text"
                  class="flex-grow p-2 border border-gray-300 rounded-r focus:outline-none focus:ring-2 focus:ring-blue-500"
                  [(ngModel)]="targetSellingPrice"
                  (input)="calculateFromSellingPrice()"
                />
              </div>
            </div>
          </div>

          <!-- Reference Data -->
          <div class="mt-4 border border-gray-200 rounded-lg">
            <div
              class="flex justify-between items-center p-3 cursor-pointer"
              (click)="togglePSFReference()"
            >
              <h5 class="text-sm font-medium">Useful Reference Data</h5>
              <button class="text-xs text-blue-600">
                {{ showPSFReference ? 'Hide' : 'Show' }}
              </button>
            </div>

            @if (showPSFReference) {
              <div class="p-3 border-t border-gray-200">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div class="flex justify-between">
                    <span class="text-sm">Highest PSF in Sengkang</span>
                    <span class="text-sm font-medium">${{ referenceData.highestPSF }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm">Most Recent Transacted PSF in Sengkang</span>
                    <span class="text-sm font-medium">${{ referenceData.recentPSF }}</span>
                  </div>
                  <div class="flex justify-between items-center">
                    <div class="flex items-center">
                      <span class="text-sm mr-2">Highest PSF within</span>
                      <select class="text-sm border border-gray-300 rounded px-2 py-1">
                        <option>2 km</option>
                        <option>4 km</option>
                        <option>6 km</option>
                      </select>
                    </div>
                    <span class="text-sm font-medium">${{ referenceData.highestPSFNearby }}</span>
                  </div>
                  <div class="flex justify-between items-center">
                    <div class="flex items-center">
                      <span class="text-sm mr-2">Most Recent Transacted PSF within</span>
                      <select class="text-sm border border-gray-300 rounded px-2 py-1">
                        <option>2 km</option>
                        <option>4 km</option>
                        <option>6 km</option>
                      </select>
                    </div>
                    <span class="text-sm font-medium">${{ referenceData.recentPSFNearby }}</span>
                  </div>
                </div>
              </div>
            }
          </div>
        </div>

        <!-- Annualised Capital Gain Method -->
        <div>
          <h4 class="text-lg font-semibold">Annualised Capital Gain Method</h4>
          <p class="text-sm mb-6">Enter either Target Selling Price or Actualised Capital Gain</p>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <p class="block text-sm font-medium mb-1">Your Purchase Price</p>
              <div class="flex">
                <span
                  class="bg-gray-100 px-3 py-2 border border-r-0 border-gray-300 rounded-l dark:text-black"
                  >$</span
                >
                <input
                  type="text"
                  class="flex-grow p-2 border border-gray-300 rounded-r focus:outline-none focus:ring-2 focus:ring-blue-500"
                  [(ngModel)]="purchasePrice"
                  (input)="calculateCapitalGain()"
                />
              </div>
            </div>
            <div>
              <p class="block text-sm font-medium mb-1">&nbsp;</p>
              <select
                class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                [(ngModel)]="purchaseDate"
                (change)="calculateCapitalGain()"
              >
                <option value="Jan 2017">Jan 2017</option>
                <option value="Jan 2018">Jan 2018</option>
                <option value="Jan 2019">Jan 2019</option>
                <option value="Jan 2020">Jan 2020</option>
                <option value="Jan 2021">Jan 2021</option>
                <option value="Jan 2022">Jan 2022</option>
                <option value="Jan 2023">Jan 2023</option>
                <option value="Jan 2024">Jan 2024</option>
              </select>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <p class="block text-sm font-medium mb-1">Target Selling Price</p>
              <div class="flex">
                <span
                  class="bg-gray-100 px-3 py-2 border border-r-0 border-gray-300 rounded-l text-gray-600 dark:text-black"
                  >$</span
                >
                <input
                  type="text"
                  class="flex-grow p-2 border border-gray-300 rounded-r focus:outline-none focus:ring-2 focus:ring-blue-500"
                  [(ngModel)]="capitalGainSellingPrice"
                  (input)="calculateCapitalGain()"
                />
              </div>
            </div>
            <div>
              <p class="block text-sm font-medium text-gray-700 mb-1">&nbsp;</p>
              <select
                class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                [(ngModel)]="sellingDate"
                (change)="calculateCapitalGain()"
              >
                <option value="Oct 2024">Oct 2024</option>
                <option value="Nov 2024">Nov 2024</option>
                <option value="Dec 2024">Dec 2024</option>
                <option value="Jan 2025">Jan 2025</option>
                <option value="Feb 2025">Feb 2025</option>
                <option value="Mar 2025">Mar 2025</option>
                <option value="Apr 2025">Apr 2025</option>
              </select>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <p class="block text-sm font-medium text-gray-700 mb-1">Profit</p>
              <div class="flex">
                <span
                  class="px-3 py-2 border border-r-0 border-gray-300 rounded-l bg-gray-50 text-gray-600 dark:text-black"
                  >$</span
                >
                <input
                  type="text"
                  class="flex-grow p-2 border border-gray-300 rounded-r"
                  [value]="profit"
                  readonly
                />
              </div>
            </div>
            <div>
              <p class="block text-sm font-medium text-gray-700 mb-1">&nbsp;</p>
              <input
                type="text"
                class="w-full p-2 border border-gray-300 rounded"
                [value]="holdingPeriod"
                readonly
              />
            </div>
          </div>

          <div class="mb-4">
            <p class="block text-sm font-medium mb-1">Actualised Capital Gain</p>
            <div class="flex">
              <input
                type="text"
                class="flex-grow p-2 border border-gray-300 rounded-l"
                [value]="capitalGainPercentage"
                readonly
              />
              <span
                class="bg-gray-100 px-3 py-2 border border-l-0 border-gray-300 rounded-r text-gray-600 dark:text-black"
                >%</span
              >
            </div>
          </div>

          <!-- Reference Data -->
          <div class="mt-4 border border-gray-200 rounded-lg">
            <div
              class="flex justify-between items-center p-3 cursor-pointer"
              (click)="toggleCapitalGainReference()"
            >
              <h5 class="text-sm font-medium">Useful Reference Data</h5>
              <button class="text-xs">
                {{ showCapitalGainReference ? 'Hide' : 'Show' }}
              </button>
            </div>

            @if (showCapitalGainReference) {
              <div class="p-3 border-t border-gray-200">
                <div class="mb-2">
                  <span class="text-sm">Singapore Resale HDB Price Index (same period)</span>
                  <span class="ml-2 text-sm font-medium">{{ capitalGainPercentage }}%</span>
                </div>

                <div class="grid grid-cols-2 gap-4 mb-2">
                  <div>
                    <div class="text-sm">Data Used</div>
                    <div class="text-sm">{{ purchaseDate }}</div>
                    <div class="text-sm">{{ sellingDate }}</div>
                  </div>
                  <div>
                    <div class="text-sm">HDB Resale Price Index</div>
                    <div class="text-sm">{{ referenceData.hdbIndexPurchase }}</div>
                    <div class="text-sm">{{ referenceData.hdbIndexSelling }}</div>
                  </div>
                </div>

                <div class="text-right">
                  <button
                    class="text-xs text-blue-600 border border-blue-600 rounded px-2 py-1 dark:text-white"
                    (click)="showHdbIndexChart()"
                  >
                    See Chart
                  </button>
                </div>

                <p class="text-xs mt-2">
                  Use this to calculate your actualised gain from your purchase price
                </p>
              </div>
            }
          </div>
        </div>
      </div>
    </div>

    <!-- Navigation Buttons -->
    <div class="flex justify-between mt-8">
      <div class="flex gap-2">
        <button
          routerLink="/buyer-seller/step4"
          class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
        >
          Back
        </button>
        <button
          class="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
        >
          Save Draft
        </button>
      </div>

      <button
        routerLink="/buyer-seller/step5"
        class="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
      >
        Continue to Sale Conditions
      </button>
    </div>

    <!-- HDB Index Chart Modal -->
    @if (showChartModal) {
      <div class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
        <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-3xl mx-4 dark:bg-gray-800">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg text-center font-bold dark:text-white">HDB Resale Price Index</h3>
            <button (click)="closeHdbIndexChart()">
              <mat-icon>close</mat-icon>
            </button>
          </div>
          <div class="overflow-auto max-h-[70vh]">
            <img
              src="assets/hdbResaleIndex.png"
              alt="HDB Resale Price Index Chart"
              class="w-full aspect-square h-auto rounded-lg"
            />
          </div>
        </div>
      </div>
    }
  </div>
</div>
