<div class="p-4">
  <h2 class="text-2xl font-bold mb-6 text-gray-800 border-b pb-2">My Sales Listings</h2>
  
  <!-- My Listings Section -->
  @if (isLoading) {
    <div class="flex justify-center py-10">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
    </div>
  } @else if (mySalesListings.length === 0) {
    <div class="flex flex-col items-center justify-center py-10 bg-white rounded-lg shadow-sm border border-gray-200">
      <img src="assets/empty-state.svg" alt="No listings" class="w-32 h-32 mb-4 opacity-60" onerror="this.src='assets/dummy_prop.png'; this.onerror=null;">
      <p class="text-gray-500 text-lg">You haven't created any sales listings yet.</p>
      <p class="text-sm text-gray-400 mt-2">Click on "List a New Property" to get started.</p>
    </div>
  } @else {
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      @for (listing of mySalesListings; track $index) {
        <div class="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300 border border-gray-100 flex flex-col h-full">
          <!-- Property Image with Price Badge -->
          <div class="relative">
            <img [src]="'./assets/dummy_prop.png'" alt="Property photo" class="w-full h-48 object-cover">
            <div class="absolute top-0 right-0 bg-blue-600 text-white px-4 py-1 rounded-bl-lg font-bold">
              S$ {{ listing.price | number:'1.0-0' }}
            </div>
            <div class="absolute bottom-0 left-0 bg-black bg-opacity-60 text-white px-3 py-1 text-xs">
              {{ listing.flatType || 'HDB' }} • {{ listing.bedrooms || '?' }} BR • {{ listing.bathrooms || '?' }} BA
            </div>
            
            <!-- Offers Badge (Always shown) -->
            <div class="absolute top-0 left-0 bg-green-600 text-white px-3 py-1 rounded-br-lg font-bold flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Check Offers
            </div>
          </div>

          <!-- Property Details -->
          <div class="p-4 flex-grow">
            <div class="flex justify-between items-start mb-2">
              <h3 class="font-bold text-lg text-gray-800 line-clamp-1">{{ listing.street }} (Block {{ listing.blockNumber }})</h3>
              <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">{{ listing.psf }} PSF</span>
            </div>

            <p class="text-gray-600 mb-2">{{ listing.town }}</p>

            <div class="flex items-center text-xs text-gray-500 mb-3">
              <span class="mr-2">ID: {{ listing.listingId }}</span>
              <span class="flex-grow"></span>
              <span>Listed {{ listing.createdAt | date:'mediumDate' }}</span>
            </div>

            <!-- Sale Conditions -->
            @if (listing.salesCondition && listing.salesCondition.length > 0) {
              <div class="mb-3">
                <h4 class="text-sm font-semibold text-gray-700 mb-1 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Sale Conditions
                </h4>
                <ul class="list-disc ml-5 text-xs text-gray-600">
                  @for (cond of listing.salesCondition; track $index) {
                    <li>{{ cond }}</li>
                  }
                </ul>
              </div>
            }
          </div>

          <!-- Action Buttons -->
          <div class="p-4 bg-gray-50 border-t border-gray-100">
            <div class="grid grid-cols-2 gap-2">
              <button
                class="bg-green-600 text-white rounded-lg px-3 py-2 text-sm font-medium shadow-sm hover:bg-green-700 transition-colors flex items-center justify-center"
                (click)="viewOffers(listing)"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Check Offers
              </button>
              <button
                class="bg-amber-500 text-white rounded-lg px-3 py-2 text-sm font-medium shadow-sm hover:bg-amber-600 transition-colors flex items-center justify-center"
                (click)="editListing(listing)"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                Edit Listing
              </button>
            </div>
            <button
              class="w-full mt-2 bg-gray-700 text-white rounded-lg px-3 py-2 text-sm font-medium shadow-sm hover:bg-gray-800 transition-colors flex items-center justify-center"
              (click)="previewListing(listing)"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
              Preview Listing
            </button>
          </div>
        </div>
      }
      
      <!-- Add New Property Card -->
      <div class="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300 border border-gray-100 cursor-pointer flex flex-col h-full" (click)="createNewListing()">
        <div class="p-6 flex items-center justify-center flex-grow">
          <div class="flex flex-col items-center text-center">
            <div class="bg-blue-100 rounded-full p-4 mb-3">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
            </div>
            <h3 class="font-bold text-lg text-gray-800 mb-2">List a New Property</h3>
            <p class="text-gray-600 text-sm">Create a new sales listing for your property</p>
          </div>
        </div>
      </div>
    </div>
  }
</div>
