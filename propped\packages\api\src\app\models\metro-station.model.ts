import { type Model, Schema, model } from 'mongoose';

interface IMetroStation {
  mrtName: string;
  location: {
    type: 'Point';
    coordinates: [number, number];
  };
}

const MetroStationSchema = new Schema<IMetroStation>(
  {
    mrtName: { type: String, required: true },
    location: {
      type: { type: String, enum: ['Point'], default: 'Point' },
      coordinates: { type: [Number], required: true },
    },
  },
  { timestamps: true },
);

MetroStationSchema.index({ location: '2dsphere' });

export const MetroStationModel: Model<IMetroStation> = model<IMetroStation>(
  'MetroStation',
  MetroStationSchema,
);
