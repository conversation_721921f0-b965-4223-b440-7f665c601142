-- --------------------------------------------------------
-- Host:                         localhost
-- Server version:               8.0.33 - MySQL Community Server - GPL
-- Server OS:                    Linux
-- HeidiSQL Version:             12.8.0.6908
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;s
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIG<PERSON>_KEY_CHECKS=@@FOREI<PERSON><PERSON>_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;


-- Dumping database structure for propped
CREATE DATABASE IF NOT EXISTS `propped` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci */ /*!80016 DEFAULT ENCRYPTION='N' */;
USE `propped`;

-- Dumping structure for table propped.files
CREATE TABLE IF NOT EXISTS `files` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `tableId` int unsigned NOT NULL,
  `itemId` int unsigned NOT NULL,
  `name` varchar(255) NOT NULL,
  `desc` text,
  `size` int NOT NULL,
  `type` varchar(255) NOT NULL,
  `path` varchar(255) NOT NULL,
  `meta` json DEFAULT NULL,
  `createdAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=50 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- Dumping structure for table propped.roles
CREATE TABLE IF NOT EXISTS `roles` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table propped.roles: ~5 rows (approximately)
INSERT INTO `roles` (`id`, `name`) VALUES
	(1, 'user'),
	(2, 'admin'),
	(3, 'master'),
	(4, 'super_admin'),
	(5, 'buyer'),
	(6, 'seller');

-- Dumping structure for table propped.settings
CREATE TABLE IF NOT EXISTS `settings` (
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`key`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table propped.settings: ~0 rows (approximately)
INSERT INTO `settings` (`key`, `value`) VALUES
	('permission', '{"canSeeAccount":[4,3,2,1],"canSeeOverview":[2,4,3,1],"canSeeUAC":[3,4,2],"canSeeUsers":[4,3,2],"canSeeHDBSales":[4,3,2],"canSeeCondoSales":[2,3,4]}');

-- Dumping structure for table propped.users
CREATE TABLE IF NOT EXISTS `users` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `pic` int unsigned DEFAULT NULL,
  `isActive` tinyint NOT NULL DEFAULT '1',
  `createdAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `meta` json NOT NULL,
  `secret` json NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `IDX_97672ac88f789774dd47f7c8be` (`email`),
  UNIQUE KEY `IDX_fe0bb3f6520ee0469504521e71` (`username`),
  UNIQUE KEY `REL_757288f313c4dc11e3ddb8b03c` (`pic`),
  CONSTRAINT `users_pic_to_files_id` FOREIGN KEY (`pic`) REFERENCES `files` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table propped.users: ~3 rows (approximately)
INSERT INTO `users` (`id`, `name`, `username`, `email`, `password`, `pic`, `isActive`, `createdAt`, `updatedAt`, `meta`, `secret`) VALUES
	(1, 'Master', 'master', '<EMAIL>', 's$14$8$1$30$fT2jQiMrE5NR3CjZAeRzuZCcOk95$b37ushVNa1Ac30tVXUz6p0Z3+wROLmY/QKJjHVXk', NULL, 1, '2025-02-17 13:35:39.668424', '2025-02-17 13:35:39.668424', '{"TFARequire": false}', '{}'),
	(2, 'superadmin', 'superadmin', '<EMAIL>', 's$14$8$1$32$9XXTZcA7oM6/8D1F0m4N$8ZqI06NAaUsjAApssBkAIsado19j8nn06VWQUXFnsR8=', NULL, 1, '2025-02-17 13:35:39.668424', '2025-02-17 13:35:39.668424', '{"TFARequire": false}', '{}'),
	(3, 'admin12', 'admin12', '<EMAIL>', 's$14$8$1$30$bq+dAfzrit8WGfdHxS5HacXbH61r$01HbTI4vJsdKA9tBv/X9XQdPjXuH5sEQNQ48AHOf', NULL, 1, '2025-02-17 13:35:39.668424', '2025-02-17 13:35:39.668424', '{}', '{}'),
	(4, 'james123', 'buyerJames123', '<EMAIL>', 's$14$8$1$32$9XXTZcA7oM6/8D1F0m4N$8ZqI06NAaUsjAApssBkAIsado19j8nn06VWQUXFnsR8=', NULL, 1, '2025-02-17 13:35:39.668424', '2025-02-17 13:35:39.668424', '{"TFARequire": false}', '{}'),
	(5, 'tommy345', 'sellerTommy456', '<EMAIL>', 's$14$8$1$32$9XXTZcA7oM6/8D1F0m4N$8ZqI06NAaUsjAApssBkAIsado19j8nn06VWQUXFnsR8=', NULL, 1, '2025-02-17 13:35:39.668424', '2025-02-17 13:35:39.668424', '{"TFARequire": false}', '{}');
-- Dumping structure for table propped.user_role
CREATE TABLE IF NOT EXISTS `user_role` (
  `userId` int unsigned NOT NULL,
  `roleId` int unsigned NOT NULL,
  `meta` json DEFAULT NULL,
  PRIMARY KEY (`userId`,`roleId`),
  KEY `IDX_ab40a6f0cd7d3ebfcce082131f` (`userId`),
  KEY `IDX_dba55ed826ef26b5b22bd39409` (`roleId`),
  CONSTRAINT `FK_ab40a6f0cd7d3ebfcce082131fd` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_dba55ed826ef26b5b22bd39409b` FOREIGN KEY (`roleId`) REFERENCES `roles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table propped.user_role: ~3 rows (approximately)
INSERT INTO `user_role` (`userId`, `roleId`, `meta`) VALUES
	(1, 3, NULL),
	(2, 4, NULL),
	(3, 1, NULL),
	(4, 5, NULL),
	(5, 6, NULL);

-- Dumping structure for table propped.user_token
CREATE TABLE IF NOT EXISTS `user_token` (
  `email` varchar(255) NOT NULL,
  `token` varchar(6) NOT NULL DEFAULT '',
  `updatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
