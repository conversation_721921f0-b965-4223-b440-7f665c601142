import type { AfterViewInit, OnDestroy, OnInit } from '@angular/core';
import { Directive, EventEmitter, Output } from '@angular/core';

export type LifeCycleStatus = 'OnInit' | 'AfterViewInit' | 'OnDestroy';

@Directive({ selector: '[appLife]' })
export class LifeDirective implements OnInit, OnDestroy, AfterViewInit {
  @Output() lifeStatus = new EventEmitter();

  ngOnInit() {
    this.lifeStatus.emit('OnInit');
  }

  ngAfterViewInit(): void {
    this.lifeStatus.emit('AfterViewInit');
  }

  ngOnDestroy(): void {
    this.lifeStatus.emit('OnDestroy');
  }
}
