import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { Injectable, inject } from '@angular/core';
import { EnsureSingleInstance } from '@lib/common/test-root-service';
import { BehaviorSubject } from 'rxjs';

const matcher = [
  Breakpoints.XSmall,
  Breakpoints.Small,
  Breakpoints.Medium,
  Breakpoints.Large,
  Breakpoints.XLarge,
];

const matcherObj = {
  [Breakpoints.XSmall]: 'xs',
  [Breakpoints.Small]: 'sm',
  [Breakpoints.Medium]: 'md',
  [Breakpoints.Large]: 'lg',
  [Breakpoints.XLarge]: 'xl',
};

@Injectable({ providedIn: 'root' })
export class MatchMediaService {
  readonly #breakpointObserver = inject(BreakpointObserver);
  private activeMediaQuery: string;
  public onMediaChange: BehaviorSubject<string> = new BehaviorSubject<string>('');

  constructor() {
    EnsureSingleInstance(this);
    this.activeMediaQuery = '';
    this.#breakpointObserver.observe(matcher).subscribe((changes) => {
      const result = matcher.find((v) => changes.breakpoints[v]);
      if (result && this.activeMediaQuery !== result) {
        this.activeMediaQuery = result;
        this.onMediaChange.next(matcherObj[result]);
      }
    });
  }
}
