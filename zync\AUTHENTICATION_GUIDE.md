# Zync Authentication System Documentation

## Overview

This document describes the comprehensive authentication system implemented in the Zync project. The system includes registration, login with 2FA support, password reset, and TOTP (Time-based One-Time Password) functionality.

## Features

- ✅ **3-Step Registration Flow**: Email verification → OTP verification → Account creation
- ✅ **Login with 2FA Support**: Username/Email + Password → Optional 2FA verification
- ✅ **TOTP Authentication**: Google Authenticator compatible
- ✅ **Password Reset Flow**: Secure token-based password reset
- ✅ **Email Validation**: Comprehensive email verification system
- ✅ **JWT Token Management**: Different expiration times for web/mobile
- ✅ **Security Features**: Password strength validation, encryption, rate limiting

## Database Schema

### Updated Tables

1. **users** - Enhanced with new fields:
   - `username` - Unique username (3-30 characters)
   - `meta` - JSON field for 2FA settings: `{"TFARequire": boolean}`
   - `secret` - JSON field for TOTP secrets: `{"totp": "encrypted_secret"}`
   - `is_email_verified` - Email verification status
   - `isActive` - Account active status
   - `role` - User role (USER, ADMIN, MODERATOR)

2. **userTokenTable** - OTP management:
   - `email` - User email (Primary Key)
   - `token` - 5-digit OTP
   - `updatedAt` - Token creation/update time

3. **tokens** - Session management:
   - `user_id` - Foreign key to users
   - `token` - JWT token
   - `device_type` - web/android/ios
   - `expires_at` - Token expiration

## API Endpoints

### Public Endpoints (No Authentication Required)

#### Registration Flow
```
POST /auth/register
POST /auth/verify-register-otp
POST /auth/complete-registration
```

#### Login Flow
```
POST /auth/login
POST /auth/verify-tfa-email
POST /auth/verify-totp
```

#### Password Reset Flow
```
POST /auth/forgot-password
POST /auth/verify-forgot-password-otp
POST /auth/reset-password
```

#### Email Activation
```
POST /auth/active-mail
POST /auth/verify-active-mail-otp
```

#### Validation
```
POST /auth/check-email-exists
POST /auth/check-username-exists
```

### Protected Endpoints (Authentication Required)

#### 2FA Management
```
POST /auth/toggle-tfa
POST /auth/setup-totp
POST /auth/confirm-totp-setup
```

#### Session Management
```
POST /auth/logout
```

## Authentication Flows

### 1. Registration Flow (3 Steps)

**Step 1: Email Verification**
```javascript
POST /auth/register
{
  "email": "<EMAIL>"
}
```

**Step 2: OTP Verification**
```javascript
POST /auth/verify-register-otp
{
  "email": "<EMAIL>",
  "otp": "12345"
}
```

**Step 3: Account Creation**
```javascript
POST /auth/complete-registration
{
  "email": "<EMAIL>",
  "name": "John Doe",
  "username": "johndoe",
  "password": "SecurePass123!",
  "confirmPassword": "SecurePass123!"
}
```

### 2. Login Flow

**Normal Login (No 2FA)**
```javascript
POST /auth/login
{
  "identifier": "johndoe", // username or email
  "password": "SecurePass123!"
}
```

**2FA Required Response**
```javascript
{
  "success": true,
  "message": "2FA required. Choose your verification method.",
  "requireTFA": true,
  "tfaOptions": {
    "totp": true,
    "email": true
  },
  "tempToken": "temporary_jwt_token"
}
```

**Email OTP Verification**
```javascript
POST /auth/verify-tfa-email
{
  "tempToken": "temporary_jwt_token",
  "otp": "12345"
}
```

**TOTP Verification**
```javascript
POST /auth/verify-totp
{
  "tempToken": "temporary_jwt_token",
  "totpCode": "123456"
}
```

### 3. Password Reset Flow

**Step 1: Request Reset**
```javascript
POST /auth/forgot-password
{
  "email": "<EMAIL>"
}
```

**Step 2: Verify Token**
```javascript
POST /auth/verify-forgot-password-otp
{
  "email": "<EMAIL>",
  "token": "12345"
}
```

**Step 3: Reset Password**
```javascript
POST /auth/reset-password
{
  "tempResetToken": "temporary_reset_token",
  "newPassword": "NewSecurePass123!",
  "confirmPassword": "NewSecurePass123!"
}
```

### 4. 2FA Setup

**Enable 2FA**
```javascript
POST /auth/toggle-tfa
{
  "enable": true
}
```

**Setup TOTP**
```javascript
POST /auth/setup-totp
// Returns QR code and manual entry key
```

**Confirm TOTP Setup**
```javascript
POST /auth/confirm-totp-setup
{
  "tempSecret": "encrypted_secret",
  "totpCode": "123456"
}
```

## Security Features

### Password Requirements
- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one number
- At least one special character (@$!%*?&)

### Username Requirements
- 3-30 characters
- Alphanumeric and underscore only
- Must be unique

### Token Expiration
- **Web**: 8 hours
- **Android/iOS**: 30 days
- **Temporary tokens**: 10-15 minutes

### OTP Security
- 5-digit random codes
- 10-minute expiration
- Stored in separate token table
- Automatically deleted after use

## Installation and Setup

1. **Install Dependencies**
```bash
npm install speakeasy qrcode crypto
```

2. **Update Database Schema**
```bash
node src/scripts/runDatabaseUpdate.js
```

3. **Environment Variables**
```env
SECRET_KEY=your_secret_key
APP_NAME=Your App Name
SMTP_EMAIL_USER=<EMAIL>
SMTP_EMAIL_PASS=your_app_password
```

4. **Test the System**
```bash
node src/scripts/testAuthSystem.js
```

## Error Handling

All endpoints return consistent error responses:
```javascript
{
  "success": false,
  "message": "Error description",
  "error": "Additional error details (in development)"
}
```

## Security Considerations

1. **Rate Limiting**: Implement rate limiting on authentication endpoints
2. **HTTPS Only**: Always use HTTPS in production
3. **Environment Variables**: Never commit secrets to version control
4. **Token Storage**: Store JWT tokens securely on client side
5. **Database Security**: Use parameterized queries (already implemented)
6. **Email Security**: Use app-specific passwords for email services

## Testing

The system includes comprehensive tests for:
- OTP generation and validation
- JWT token creation and verification
- TOTP setup and verification
- Encryption/decryption
- Input validation
- Password strength validation

Run tests with:
```bash
node src/scripts/testAuthSystem.js
```

## Support

For issues or questions about the authentication system, please refer to the code comments or create an issue in the project repository.
