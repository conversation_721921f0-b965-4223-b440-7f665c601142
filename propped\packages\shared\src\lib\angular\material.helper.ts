import { MaterialColors, shades } from './mat-colors';

const generate = () => {
  return Object.entries(MaterialColors)
    .map(([key, value]) => {
      return shades
        .map(
          (shade, shadeIx) =>
            `.${key}-${shade}-bg {background-color: ${value[0][shadeIx]}!important;}` +
            `.${key}-${shade}-fg {color: ${value[0][shadeIx]}!important;}` +
            `.${key}-${shade} {color: ${value[1][shadeIx]}!important;background-color: ${value[0][shadeIx]}!important}`,
        )
        .join('');
    })
    .join('');
};

const injectStyleSheetInHead = (css: string, desc = 'helpers') => {
  const head = document.head || document.getElementsByTagName('head')[0];
  const style = document.createElement('style');
  style.setAttribute('desc', desc);
  head.appendChild(style);
  if ((style as any).styleSheet) {
    // This is required for IE8 and below.
    (style as any).styleSheet.cssText = css;
  } else {
    style.appendChild(document.createTextNode(css));
  }
};

export const LoadHelperStyleSheet = () => {
  injectStyleSheetInHead(generate(), 'color-helpers');
};
