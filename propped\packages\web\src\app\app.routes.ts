import type { Route } from '@angular/router';
import { UserLoginGuard, UserNotLoginGuard, UserPermissionGuard } from './guard/user-login.guard';
import { MainLayoutComponent } from './layout/main/main.component';
import { PERMISSIONS } from './services/permission.service';

export const appRoutes: Route[] = [
  {
    path: '',
    canActivate: [UserLoginGuard],
    component: MainLayoutComponent,
    children: [
      {
        path: 'hdb-sales',
        canActivate: [UserPermissionGuard],
        data: { perm: PERMISSIONS.canSeeHDBSales },
        loadChildren: () => import('./pages/hdb-sales/hdb-sales.routes').then((v) => v.routes),
      },
      {
        path: 'condo-sales',
        canActivate: [UserPermissionGuard],
        data: { perm: PERMISSIONS.canSeeCondoSales },
        loadChildren: () => import('./pages/condo-sales/condo-sales.routes').then((v) => v.routes),
      },
      {
        path: 'account',
        canActivate: [UserPermissionGuard],
        data: { perm: PERMISSIONS.canSeeAccount },
        loadComponent: () =>
          import('./pages/account/account.component').then((v) => v.AccountComponent),
      },
      {
        path: 'users',
        canActivate: [UserPermissionGuard],
        data: { perm: PERMISSIONS.canSeeUsers },
        loadChildren: () => import('./pages/users/users.routes').then((v) => v.routes),
      },
      {
        path: 'uac',
        canActivate: [UserPermissionGuard],
        data: { perm: PERMISSIONS.canSeeUAC },
        loadChildren: () => import('./pages/uac/uac.routes').then((v) => v.routes),
      },
      {
        path: '',
        canActivate: [UserPermissionGuard],
        data: { perm: PERMISSIONS.canSeeOverview },
        loadChildren: () => import('./pages/overview/overview.routes').then((v) => v.routes),
      },
      {
        path: 'hdb-transactions',
        canActivate: [UserPermissionGuard],
        data: { perm: PERMISSIONS.canSeeHDBSales },
        loadChildren: () =>
          import('./pages/hdb-transactions/hdb-transaction.routes').then((v) => v.routes),
      },
      {
        path: 'condo-transactions',
        canActivate: [UserPermissionGuard],
        data: { perm: PERMISSIONS.canSeeCondoSales },
        loadChildren: () =>
          import('./pages/condo-transactions/condo-transaction.routes').then((v) => v.routes),
      },
      {
        path: 'hdb-resale-trend',
        canActivate: [UserPermissionGuard],
        data: { perm: PERMISSIONS.canSeeOverview },
        loadComponent: () =>
          import('./pages/hdb-resale-trend/hdb-resale-trend.component').then(
            (v) => v.HdbResaleTrendComponent,
          ),
      },
      {
        path: 'buyer-seller',
        canActivate: [UserPermissionGuard],
        data: { perm: PERMISSIONS.canSeeOverview },
        loadChildren: () =>
          import('./pages/buyer-seller/buyer-seller.routes').then((v) => v.routes),
      },
      {
        path: 'mapbox-overview',
        canActivate: [UserPermissionGuard],
        data: { perm: PERMISSIONS.canSeeOverview },
        loadComponent: () =>
          import('./pages/mapbox-overview/map-box-overview.component').then(
            (v) => v.MapBoxOverviewComponent,
          ),
      },
      {
        path: 'user-dashboard',
        canActivate: [UserPermissionGuard],
        data: { perm: PERMISSIONS.canSeeOverview },
        loadComponent: () =>
          import('./pages/user-dashboard/user-dashboard.component').then(
            (v) => v.UserDashboard,
          ),
      },
    ],
  },
  {
    path: 'auth',
    canActivate: [UserNotLoginGuard],
    loadChildren: () => import('./pages/auth/auth.routes').then((v) => v.routes),
  },
];
