const authUtils = require('../utils/authUtils');

/**
 * Test script to validate the authentication utilities
 */
const testAuthSystem = async () => {
  console.log('🧪 Testing Authentication System...\n');

  try {
    // Test 1: OTP Generation
    console.log('1. Testing OTP Generation...');
    const otp1 = Math.floor(10000 + Math.random() * 90000).toString();
    const otp2 = Math.floor(10000 + Math.random() * 90000).toString();
    console.log(`✅ Generated OTP 1: ${otp1}`);
    console.log(`✅ Generated OTP 2: ${otp2}`);
    console.log(`✅ OTPs are different: ${otp1 !== otp2}\n`);

    // Test 2: JWT Token Generation
    console.log('2. Testing JWT Token Generation...');
    const payload = { userId: 123, username: 'testuser', email: '<EMAIL>' };
    
    const webToken = authUtils.generateJWTToken(payload, 'web');
    console.log(`✅ Web Token: ${webToken.token.substring(0, 50)}...`);
    console.log(`✅ Web Expiry: ${webToken.expiresIn}`);
    
    const mobileToken = authUtils.generateJWTToken(payload, 'android');
    console.log(`✅ Mobile Token: ${mobileToken.token.substring(0, 50)}...`);
    console.log(`✅ Mobile Expiry: ${mobileToken.expiresIn}\n`);

    // Test 3: TOTP Secret Generation
    console.log('3. Testing TOTP Secret Generation...');
    const totpData = await authUtils.generateTOTPSecret('testuser', '<EMAIL>');
    console.log(`✅ TOTP Secret: ${totpData.secret}`);
    console.log(`✅ Manual Entry Key: ${totpData.manualEntryKey}`);
    console.log(`✅ QR Code generated: ${totpData.qrCode.length > 0}`);
    console.log(`✅ OTP Auth URL: ${totpData.otpauth_url}\n`);

    // Test 4: TOTP Verification (simulate)
    console.log('4. Testing TOTP Verification...');
    // Generate a current TOTP token for testing
    const speakeasy = require('speakeasy');
    const currentToken = speakeasy.totp({
      secret: totpData.secret,
      encoding: 'base32'
    });
    console.log(`✅ Current TOTP Token: ${currentToken}`);
    
    const isValidTOTP = authUtils.verifyTOTPToken(currentToken, totpData.secret);
    console.log(`✅ TOTP Verification: ${isValidTOTP}\n`);

    // Test 5: Encryption/Decryption
    console.log('5. Testing Encryption/Decryption...');
    const originalText = 'This is a secret message';
    const encrypted = authUtils.encrypt(originalText);
    const decrypted = authUtils.decrypt(encrypted);
    console.log(`✅ Original: ${originalText}`);
    console.log(`✅ Encrypted: ${encrypted}`);
    console.log(`✅ Decrypted: ${decrypted}`);
    console.log(`✅ Encryption/Decryption works: ${originalText === decrypted}\n`);

    // Test 6: Email Validation
    console.log('6. Testing Email Validation...');
    const validEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];
    const invalidEmails = ['invalid-email', '@domain.com', 'user@', 'user@domain'];
    
    validEmails.forEach(email => {
      console.log(`✅ ${email}: ${authUtils.isValidEmail(email)}`);
    });
    
    invalidEmails.forEach(email => {
      console.log(`❌ ${email}: ${authUtils.isValidEmail(email)}`);
    });
    console.log();

    // Test 7: Username Validation
    console.log('7. Testing Username Validation...');
    const validUsernames = ['user123', 'test_user', 'username', 'user_name_123'];
    const invalidUsernames = ['us', 'user-name', 'user@name', 'a'.repeat(31)];
    
    validUsernames.forEach(username => {
      console.log(`✅ ${username}: ${authUtils.isValidUsername(username)}`);
    });
    
    invalidUsernames.forEach(username => {
      console.log(`❌ ${username}: ${authUtils.isValidUsername(username)}`);
    });
    console.log();

    // Test 8: Password Validation
    console.log('8. Testing Password Validation...');
    const passwords = [
      'Password123!', // Valid
      'password123!', // Missing uppercase
      'PASSWORD123!', // Missing lowercase
      'Password!', // Missing number
      'Password123', // Missing special char
      'Pass1!', // Too short
    ];
    
    passwords.forEach(password => {
      const validation = authUtils.validatePassword(password);
      console.log(`${validation.isValid ? '✅' : '❌'} ${password}: ${validation.isValid ? 'Valid' : validation.errors.join(', ')}`);
    });

    console.log('\n🎉 All authentication utility tests completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
};

// Run tests
testAuthSystem();
