<div class="mx-auto rounded-lg shadow-lg p-6 mb-8 w-full">
  <!-- Header -->
  <h1 class="text-2xl font-bold text-center mb-2">Make Offer</h1>
  <div class="border-b border-gray-200 mb-6"></div>

  <!-- Offer Price Input Section -->
  <form class="mb-6">
    <div class="flex items-center mb-1">
      <span class="text-xl font-bold mr-2">S$</span>
      <input
        id="offerPrice"
        type="number"
        class="w-full border border-gray-300 rounded-lg px-4 py-2 text-xl font-semibold focus:outline-none focus:ring-2 focus:ring-blue-500"
        [(ngModel)]="offerPrice"
        name="offerPrice"
        [readonly]="!isEditable"
      />
    </div>
    <div class="flex items-center justify-between flex-wrap my-2">
      @if(!offerCreated){
      <p class="text-sm">
        Your offer price is {{ offerDifference | currency: 'SGD' : 'symbol' : '1.0-0' }} or
        {{ offerDifferencePercent }}% below the asking price
      </p>
      }
      @else{
      <p class="text-sm font-semibold underline">You have already made an Offer for the current sales listing</p>
      }
      <button
        [class.opacity-60]="offerCreated"
        [disabled]="offerCreated"
        class="bg-blue-600 text-white text-lg font-bold py-2 px-4 rounded-lg flex items-center justify-center mb-4 hover:bg-blue-700 transition-colors"
        (click)="onPreviewOffer()"
        type="button"
      >
        Preview Offer
      </button>
    </div>
  </form>

  <!-- Offer Initiator Info -->
  <div class="flex items-center mb-6">
    <span class="font-semibold text-xl mr-4">Buyer</span>
    <div
      class="bg-gray-100 rounded-full px-4 py-2 flex items-center border border-blue-200 dark:bg-gray-700"
    >
      <div class="w-8 h-8 rounded-full bg-blue-300 flex items-center justify-center mr-2">
        <mat-icon class="text-white text-2xl">person</mat-icon>
      </div>
      <span class="font-bold text-lg mr-2">{{ userName }}</span>
      <span class="flex items-center ml-2">
        <mat-icon class="text-green-600 text-base mr-1">check</mat-icon>
        <span class="text-xs text-blue-700 font-semibold">HFE Verified</span>
        <span class="text-xs text-gray-600 ml-1 dark:text-white"
          >(Valid till 15<sup>th</sup> June 2026)</span
        >
      </span>
    </div>
  </div>

  <!-- Preview Section -->
  @if (showPreview) {
    <div class="w-full mx-auto p-6 my-4 border border-gray-300 rounded-sm">
      <mat-icon class="float-right cursor-pointer" (click)="showPreview = !showPreview"
        >cancel</mat-icon
      >
      <h2 class="text-xl font-bold text-center mb-4">
        Please Review the<br />Terms & Conditions Once More
      </h2>
      <div class="mb-4">
        <div class="text-sm font-semibold mb-1">Terms & Conditions</div>
        <ol class="list-decimal pl-5 text-blue-500 text-xs mb-2">
          <li>
            All offers, once sent, are considered an official offer price and cannot be rescinded
          </li>
          <li>
            All offers will last for 14 days and if seller does not respond, will be considered
            voided.
          </li>
          <li>
            You can only make a new offer once the seller rejects the current offer or if the offer
            has been voided.
          </li>
        </ol>
        <button
          class="w-full bg-blue-600 text-white rounded-lg py-2 font-bold mb-4"
          [class.opacity-60]="agreedTerms"
          [disabled]="agreedTerms"
          (click)="agreedTerms = true"
        >
          I agree to the Terms & Conditions
        </button>
      </div>
      <div class="border-b border-gray-300 my-4"></div>
      <!-- Listing Overview -->
      <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
        <div>
          <div class="text-lg font-bold">
            {{ salesListingData.street }}<br />{{ salesListingData.blockNumber || salesListingData.blockNo }}
          </div>
          <div class="text-sm text-gray-600 mb-1">{{ salesListingData.town }}</div>
          <div class="text-2xl font-bold text-black">
            S$ {{ salesListingData.price || salesListingData.salesPrice | number }}
          </div>
          <div class="text-xs text-gray-600">
            Listed on {{ salesListingData.listedDate || '17th August 2024' }} (Listing ID:
            {{ salesListingData.listingId || '01234' }})
          </div>
        </div>
        <div class="mt-2 md:mt-0 md:ml-4 flex-shrink-0">
          <img
            src="./assets/dummy_prop.png"
            alt="property"
            class="w-32 h-20 object-cover rounded-lg shadow"
          />
        </div>
      </div>
      <div class="mb-2">
        <div class="font-bold text-sm mb-1">Sale Conditions</div>
        <ul class="list-disc pl-5 text-sm">
          @for (condition of salesListingData.salesCondition || salesListingData.saleConditions || []; track $index) {
            <li>{{ condition }}</li>
          }
        </ul>
      </div>

      <button
        class="w-full bg-blue-700 text-white rounded-lg py-2 font-bold mb-4"
        [class.opacity-60]="agreedSale"
        [disabled]="agreedSale"
        (click)="agreedSale = true"
      >
        I agree to the Seller's Sale Conditions
      </button>
      <div class="border-b border-gray-300 my-4"></div>
      <div class="text-center text-lg font-semibold mb-2">You are making a price offer of</div>
      <div class="text-center text-3xl font-bold mb-4">S$ {{ offerPrice | number }}</div>
      <button
        class="w-full text-white rounded-lg py-3 font-bold text-lg"
        [ngClass]="{
          'bg-blue-200': !canProceed(),
          'bg-blue-600': canProceed(),
        }"
        [disabled]="!canProceed()"
        (click)="onProceedOffer()"
      >
        Proceed to Make Offer!
      </button>
    </div>
  }

  @if (!showPreview) {
    <!-- Terms & Conditions -->
    <div class="p-4 rounded-lg border border-gray-200">
      <h2 class="font-semibold mb-2">Terms & Conditions</h2>
      <ol class="list-decimal pl-5 text-sm space-y-1">
        <li>
          All offers, once sent, are considered an official offer price and cannot be rescinded.
        </li>
        <li>
          All offers will last for 14 days and if the seller does not respond, will be considered
          voided.
        </li>
        <li>
          You can only make a new offer once the seller rejects the current offer or if the offer
          has been voided.
        </li>
      </ol>
    </div>
  }

  @if (offerAccepted) {
    <div class="mt-8 border-2 border-gray-200 rounded-xl p-4">
      <div class="text-center text-xl font-bold mb-4 md:text-2xl">
        Block 720a Resale<br />Overall Progress
      </div>
      <div class="flex justify-between items-center mb-4">
        <app-buyer-info [username]="userName" />
        <app-seller-info [username]="sellerInfo?.username" />
      </div>
      <div class="grid grid-cols-1 gap-2">
        <div class="flex items-center mb-2">
          <mat-icon class="text-green-400 mr-2">check_circle</mat-icon>
          <span class="font-bold text-center w-full">1. Price Acceptance</span>
        </div>
        <div class="flex items-center mb-2">
          <mat-icon class="text-green-400 mr-2">check_circle</mat-icon>
          <span class="font-bold text-center w-full">2. Buyer Places Option Fee</span>
        </div>
        <div class="flex items-center mb-2">
          <mat-icon class="text-gray-300 mr-2">radio_button_unchecked</mat-icon>
          <span class="font-bold text-center w-full">3. Seller Receives Option Fee</span>
        </div>
        <div class="flex items-center mb-2">
          <mat-icon class="text-gray-300 mr-2">radio_button_unchecked</mat-icon>
          <span class="font-bold text-center w-full">4. Seller Issues OTP - Day 0/21</span>
        </div>
        <div class="flex items-center mb-2">
          <mat-icon class="text-gray-300 mr-2">radio_button_unchecked</mat-icon>
          <span class="font-bold text-center w-full">5. Buyer Obtains Valuation</span>
        </div>
        <div class="flex items-center mb-2">
          <mat-icon class="text-gray-300 mr-2">radio_button_unchecked</mat-icon>
          <span class="font-bold text-center w-full">6. Buyer Obtains Loan Approval</span>
        </div>
        <div class="flex items-center mb-2">
          <mat-icon class="text-gray-300 mr-2">radio_button_unchecked</mat-icon>
          <span class="font-bold text-center w-full">7. Buyer Exercises Option</span>
        </div>
        <div class="flex items-center mb-2">
          <mat-icon class="text-gray-300 mr-2">radio_button_unchecked</mat-icon>
          <span class="font-bold text-center w-full">8. Buyer Applies for Bank Loan</span>
        </div>
        <div class="flex items-center mb-2">
          <mat-icon class="text-gray-300 mr-2">radio_button_unchecked</mat-icon>
          <span class="font-bold text-center w-full">9. Buyer Signs Bank Loan Documents</span>
        </div>
        <div class="flex items-center mb-2">
          <mat-icon class="text-gray-300 mr-2">radio_button_unchecked</mat-icon>
          <span class="font-bold text-center w-full">10. Buyer Signs S&P Agreement</span>
        </div>
        <div class="flex items-center mb-2">
          <mat-icon class="text-gray-300 mr-2">radio_button_unchecked</mat-icon>
          <span class="font-bold text-center w-full">11. Buyer Pays Stamp Duty</span>
        </div>
        <div class="flex items-center mb-2">
          <mat-icon class="text-gray-300 mr-2">radio_button_unchecked</mat-icon>
          <span class="font-bold text-center w-full">12. Buyer Pays Legal Fees</span>
        </div>
        <div class="flex items-center mb-2">
          <mat-icon class="text-gray-300 mr-2">radio_button_unchecked</mat-icon>
          <span class="font-bold text-center w-full">13. Buyer Pays Agent Commission</span>
        </div>
        <div class="flex items-center mb-2">
          <mat-icon class="text-gray-300 mr-2">radio_button_unchecked</mat-icon>
          <span class="font-bold text-center w-full">14. Buyer Pays Property Tax</span>
        </div>
      </div>
    </div>
  }
</div>

<!-- Mobile Responsiveness -->
<style>
  @media (max-width: 640px) {
    .max-w-lg {
      max-width: 100% !important;
      padding: 1rem !important;
    }
    .text-2xl {
      font-size: 1.25rem !important;
    }
    .text-lg {
      font-size: 1rem !important;
    }
    .py-3 {
      padding-top: 0.75rem !important;
      padding-bottom: 0.75rem !important;
    }
    .px-4 {
      padding-left: 1rem !important;
      padding-right: 1rem !important;
    }
  }
</style>
