import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-offer-price-estimator',
  templateUrl: './offer-price-estimator.component.html',
  standalone: true,
  imports: [CommonModule, FormsModule, MatIconModule, RouterModule],
})
export class OfferPriceEstimatorComponent {
  // Unit size
  unitSize = 990;
  isEditingUnitSize = false;

  // Target PSF Method
  targetPSF = 750;
  targetOfferPrice = 742500;
  sellerPrice = 750000;
  priceDifference = -7500;

  // Reference data visibility
  showPSFReferenceData = false;
  showCapitalGainReferenceData = false;

  // Reference data for PSF
  highestPSFInSengkang = 683;
  recentPSFInSengkang = 812;
  radiusOptions = ['1km', '2km', '4km'];
  selectedRadius = '1km';
  highestPSFInRadius = 680;
  recentPSFInRadius = 760;

  // Annualised Capital Gain Method
  purchasePrice = 455000;
  purchaseDate = 'Jan 2017';
  offerDate = 'Oct 2024';
  profit = 188500;
  annualisedCapitalGain = 4.17;
  durationYears = 7;
  durationMonths = 7;

  // Reference data for Capital Gain
  hdbResaleIndex = 4.5;
  indexStartDate = 'Jan 2017';
  indexStartValue = 133.9;
  indexEndDate = 'Oct 2024';
  indexEndValue = 200.9;

  constructor() {
    this.calculateTargetOfferPrice();
    this.calculatePriceDifference();
    this.calculateProfit();
  }

  // Toggle edit mode for unit size
  toggleEditUnitSize(): void {
    this.isEditingUnitSize = !this.isEditingUnitSize;
    if (!this.isEditingUnitSize) {
      this.calculateTargetOfferPrice();
    }
  }

  // Calculate target offer price based on PSF
  calculateTargetOfferPrice(): void {
    this.targetOfferPrice = this.targetPSF * this.unitSize;
    this.calculatePriceDifference();
  }

  // Calculate difference from seller's price
  calculatePriceDifference(): void {
    this.priceDifference = this.targetOfferPrice - this.sellerPrice;
  }

  // Update target price and recalculate
  updateTargetPSF(): void {
    this.calculateTargetOfferPrice();
  }

  // Update target price directly
  updateTargetPrice(): void {
    if (this.unitSize > 0) {
      this.targetPSF = this.targetOfferPrice / this.unitSize;
    }
    this.calculatePriceDifference();
  }

  // Toggle PSF reference data visibility
  togglePSFReferenceData(): void {
    this.showPSFReferenceData = !this.showPSFReferenceData;
  }

  // Toggle Capital Gain reference data visibility
  toggleCapitalGainReferenceData(): void {
    this.showCapitalGainReferenceData = !this.showCapitalGainReferenceData;
  }

  // Calculate profit
  calculateProfit(): void {
    this.profit = this.targetOfferPrice - this.purchasePrice;
    this.calculateAnnualisedCapitalGain();
  }

  // Calculate annualised capital gain
  calculateAnnualisedCapitalGain(): void {
    // Simple calculation for demo purposes
    // In a real app, this would use actual date objects and more complex calculations
    this.annualisedCapitalGain = Number(
      (
        (this.profit / this.purchasePrice / (this.durationYears + this.durationMonths / 12)) *
        100
      ).toFixed(2),
    );
  }

  // Format price with commas
  formatPrice(price: number): string {
    return price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }

  // Update purchase date
  updatePurchaseDate(date: string): void {
    this.purchaseDate = date;
    // In a real app, this would recalculate duration and capital gain
  }

  // Update offer date
  updateOfferDate(date: string): void {
    this.offerDate = date;
    // In a real app, this would recalculate duration and capital gain
  }

  // Proceed with the offer
  proceedWithOffer(): void {
    // In a real app, this would save the offer and navigate to the next step
    console.log('Proceeding with offer of $' + this.formatPrice(this.targetOfferPrice));
  }
}
