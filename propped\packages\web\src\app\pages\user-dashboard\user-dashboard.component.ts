import { Component, type OnInit } from '@angular/core';
import { SellerInfoComponent } from '../buyer-seller/make-offer-buyer/seller-info/seller-info.component';
import { MatIconModule } from '@angular/material/icon';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from '@web/services/auth.service';
import { apiRPC,injectController } from '@api/rpc';
import { inject } from '@angular/core';
import { BuyerSalesListingComponent } from "../buyer-seller/buyer-sales-listing/buyer-sales-listing.component";
import { BuyerInfoComponent } from '../buyer-seller/make-offer-buyer/buyer-info/buyer-info.component';

@Component({
  selector: 'app-user-dashboard',
  standalone: true,
  imports: [CommonModule, MatIconModule, SellerInfoComponent, FormsModule, BuyerInfoComponent],
  templateUrl: './user-dashboard.component.html',
  styleUrls: [],
})
export class UserDashboard implements OnInit{

  readonly #hdbSalesController = injectController(apiRPC.HDBSalesController);
  readonly #hdbSalesOfferController = injectController(apiRPC.HDBSalesOfferController);
  readonly #resaleTransactionController = injectController(apiRPC.HDBSalesTransactionController);

  hasSalesListing = false;
  salesListing: any = null;
  salesListings: any[] = [];
  buyerAcceptedOffers: any[] = [];
  sellerAcceptedOffers: any[] = [];
  buyerTransactionProgressDetails: any = null;
  sellerTransactionProgressDetails: any = null;
  selectedOffer: any = null;
  showBuyerChecklist = false;
  showSellerChecklist = false;

  // Dummy user profile
  userProfile = {
    username: 'Ben01234#',
    role: 'seller',
  };

  userId: number | null;
  readonly authService = inject(AuthService);
  readonly router = inject(Router);

  async ngOnInit() {
    const user = await this.authService.getActiveUser();
    this.userId = user.id;

    // Fetch sales listings for this user
    const res = await this.#hdbSalesController.getSalesListingsByUserId({ user_id: this.userId });
    console.log("RESPONSE FOR SALES LISTING",res);
    if (res?.data && res.data.length > 0) {
      this.hasSalesListing = true;
      this.salesListing = res.data[0]; // Show the first listing for now
      this.salesListings = res.data;
    } else {
      this.hasSalesListing = false;
      this.salesListing = null;
      this.salesListings = [];
    }

    if (this.userId) {
      try {
        const offersRes = await this.#hdbSalesOfferController.getAcceptedOffersByBuyerId({ buyerId: this.userId });
        const sellerOffersRes = await this.#hdbSalesOfferController.getAcceptedOffersBySellerId({ sellerId: this.userId });
        console.log("ACCEPTED OFFERS FOR USER", offersRes);
        console.log("ACCEPTED OFFERS FOR SELLER", sellerOffersRes);
        if (offersRes?.data) {
          this.buyerAcceptedOffers = offersRes.data;
        }
        if (sellerOffersRes?.data) {
          this.sellerAcceptedOffers = sellerOffersRes.data;
        }
      } catch (error) {
        console.error("Error fetching accepted offers:", error);
        this.buyerAcceptedOffers = [];
        this.sellerAcceptedOffers = [];
      }
    }
}

  // Collapsible sections state
  resaleProcessOpen = false;
  chatSections = [
    {
      label: 'Singpass Verified Users',
      open: false,
      users: ['Alice (Singpass)', 'Bob (Singpass)'],
    },
    {
      label: 'Unverified Users',
      open: false,
      users: ['Charlie', 'David'],
    },
    {
      label: 'Propped AI Chat & Helpdesk',
      open: false,
      users: ['Propped AI', 'Helpdesk'],
    },
    {
      label: 'Likely Agents',
      open: false,
      users: ['Agent Smith', 'Agent Lee'],
    },
  ];

  // Navigate to seller preview with the listing ID
  goToSellerPreview(listingId: string) {
    console.log("GO TO PREVIEW LISTING",listingId);
    this.router.navigate(['/buyer-seller/preview'], {
      queryParams: { listingId: listingId }
    });
  }

  // Chat input
  chatInput = '';

  // Methods for toggling
  toggleResaleProcess() {
    this.resaleProcessOpen = !this.resaleProcessOpen;
  }

  toggleChatSection(idx: number) {
    this.chatSections[idx].open = !this.chatSections[idx].open;
  }

  exportChat() {
    // Dummy export logic
    alert('Chat exported!');
  }

  sendMessage() {
    if (this.chatInput.trim()) {
      alert('Message sent: ' + this.chatInput);
      this.chatInput = '';
    }
  }

  goToBuyerSeller() {
    this.router.navigate(['/buyer-seller']);
  }

  async getTransactionDetails(offer: any){
    console.log("GET TRANSACTION DETAILS", offer);
    try {
      this.selectedOffer = offer;
      // Fetch transaction details
      const transactionProgress = await this.#resaleTransactionController.getTransactionByBuyerAndListing({
        buyerId: offer.buyerId,
        listingId: offer.listingDetails._id
      });

      console.log("TRANSACTION PROGRESS", transactionProgress);

      if (transactionProgress?.data) {
        this.buyerTransactionProgressDetails = transactionProgress.data;

        // Calculate completion percentage
        const completedCount = this.getCompletedCheckpointsCount();
        this.buyerTransactionProgressDetails.completionPercentage = Math.round((completedCount / 14) * 100);
      }
    } catch (error) {
      console.error("Error fetching transaction details:", error);
    }
  }

  async getSellerTransactionDetails(offer: any){
    console.log("GET SELLER TRANSACTION DETAILS", offer);
    try {
      this.selectedOffer = offer;
      // Fetch transaction details
      const transactionProgress = await this.#resaleTransactionController.getTransactionBySellerAndListing({
        sellerId: offer.sellerId,
        listingId: offer.listingDetails._id
      });

      console.log("TRANSACTION PROGRESS", transactionProgress);

      if (transactionProgress?.data) {
        this.sellerTransactionProgressDetails = transactionProgress.data;

        // Calculate completion percentage
        const completedCount = this.getCompletedCheckpointsCount();
        this.sellerTransactionProgressDetails.completionPercentage = Math.round((completedCount / 14) * 100);
      }
    } catch (error) {
      console.error("Error fetching transaction details:", error);
    }
  }

  closeTransactionDetails() {
    this.buyerTransactionProgressDetails = null;
    this.selectedOffer = null;
  }

  getCompletedCheckpointsCount(): number {
    if (!this.buyerTransactionProgressDetails?.checkpoints) return 0;

    const checkpoints = this.buyerTransactionProgressDetails.checkpoints;
    let count = 0;

    // Count completed checkpoints
    if (checkpoints.priceAcceptance?.completed) count++;
    if (checkpoints.buyerOptionFee?.completed) count++;
    if (checkpoints.sellerOptionFee?.completed) count++;
    if (checkpoints.sellerIssuesOTP?.completed) count++;
    if (checkpoints.buyerValuation?.completed) count++;
    if (checkpoints.buyerLoanApproval?.completed) count++;
    if (checkpoints.buyerExercisesOption?.completed) count++;
    if (checkpoints.buyerExerciseFee?.completed) count++;
    if (checkpoints.sellerExerciseFee?.completed) count++;
    if (checkpoints.hdbPortalSubmission?.completed) count++;
    if (checkpoints.hdbAppointment?.completed) count++;
    if (checkpoints.approvalLetter?.completed) count++;
    if (checkpoints.finalCompletion?.completed) count++;
    if (checkpoints.paymentToPropped?.completed) count++;

    return count;
  }
  getCompletedSellerCheckpointsCount() {
    if (!this.sellerTransactionProgressDetails?.sellerCheckpoints) return 0;

    const checkpoints = this.sellerTransactionProgressDetails.sellerCheckpoints;
    let count = 0;

    for (const key in checkpoints) {
      if (checkpoints[key]?.completed) {
        count++;
      }
    }

    return count;
  }

  getCompletedBuyerCheckpointsCount(): number {
    if (!this.buyerTransactionProgressDetails?.buyerCheckpoints) return 0;

    const checkpoints = this.buyerTransactionProgressDetails.buyerCheckpoints;
    let completedCount = 0;

    // Count completed checkpoints
    Object.keys(checkpoints).forEach(key => {
      if (checkpoints[key]?.completed) {
        completedCount++;
      }
    });

    return completedCount;
  }

  // Toggle seller checklist visibility
  toggleSellerChecklist() {
    this.showSellerChecklist = !this.showSellerChecklist;
  }

  // Toggle buyer checklist visibility
  toggleBuyerChecklist() {
    this.showBuyerChecklist = !this.showBuyerChecklist;
  }


}
