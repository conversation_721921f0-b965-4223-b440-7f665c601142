import { Injectable, inject } from '@angular/core';
import { SQLWebService } from '@lib/angular/sql.service';
import { isNil } from '@lib/common/fun';
import { type JDataConfig } from '@lib/common/j-data.interface';

// # Schema
/*
-- Lookup table for projects
CREATE TABLE IF NOT EXISTS projects (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL UNIQUE
);

-- Lookup table for districts
CREATE TABLE IF NOT EXISTS districts (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL UNIQUE
);

-- Lookup table for streets
CREATE TABLE IF NOT EXISTS streets (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL UNIQUE
);

-- Lookup table for market segments
CREATE TABLE IF NOT EXISTS market_segments (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL UNIQUE
);

-- Lookup table for type of areas
CREATE TABLE IF NOT EXISTS type_of_areas (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL UNIQUE
);

-- Lookup table for property types
CREATE TABLE IF NOT EXISTS property_types (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL UNIQUE
);

-- Main sales table
CREATE TABLE IF NOT EXISTS sales (
    id INTEGER PRIMARY KEY,
    projectId INTEGER,
    districtId INTEGER,
    streetId INTEGER,
    marketSegmentId INTEGER,
    contractDate INTEGER,
    startFloor INTEGER,
    endFloor INTEGER,
    typeOfSale INTEGER,
    areaSqm INTEGER,
    typeOfAreaId INTEGER,
    price INTEGER,
    propertyTypeId INTEGER,
    districtNum INTEGER,
    totalYear INTEGER, -- Total years of the contract
    leaseYear INTEGER -- year of the lease commencement
);
*/

const escapeSQLiteValue = (value: any) => {
  if (value === null || value === undefined) return 'NULL';
  if (typeof value === 'number') return value.toString();
  if (typeof value === 'boolean') return (value ? 1 : 0).toString();
  if (typeof value === 'string') return `'${value.replace(/'/g, "''")}'`;
  if (value instanceof Date) return `'${value.toISOString()}'`;
  if (Array.isArray(value)) return `'${JSON.stringify(value).replace(/'/g, "''")}'`;
  if (typeof value === 'object') return `'${JSON.stringify(value).replace(/'/g, "''")}'`;
  throw new Error('Unsupported data type');
};
@Injectable({ providedIn: 'root' })
export class CondoSalesService {
  salesDb: any;
  readonly #sqlWebService = inject(SQLWebService);

  loadDB() {
    this.salesDb ??= this.#sqlWebService.loadDB('/assets/condo-sales.sqlite');
    return this.salesDb;
  }

  getAllDistricts(db: any) {
    return db.exec('select * from districts')[0].values;
  }

  getAllMarketSegments(db: any) {
    return db.exec('select * from market_segments')[0].values;
  }

  getAllPropertyTypes(db: any) {
    return db.exec('select * from property_types')[0].values;
  }

  getAllTypeOfAreas(db: any) {
    return db.exec('select * from type_of_areas')[0].values;
  }

  public getConfigs(): JDataConfig {
    return { page: 1, limit: 50, sort: 'resaleAt', order: 'DESC' };
  }

  generateSqlQuery(db: any, filters, configs) {
    let query = /* SQL */ `SELECT
      sales.id as id,
      projects.name AS project_name,
      districts.name AS district_name,
      streets.name AS street_name,
      market_segments.name AS market_segment_name,
      strftime('%m-%Y', sales.contractDate, 'unixepoch') AS contract_date,
      sales.startFloor as start_floor,
      sales.endFloor as end_floor,
      sales.typeOfSale as type_of_sale,
      sales.areaSqm as area_sqm,
      type_of_areas.name AS type_of_area_name,
      sales.price as price,
      property_types.name AS property_type_name,
      sales.districtNum as district_num,
      sales.totalYear as total_year,
      sales.leaseYear as lease_year
    FROM sales
    JOIN projects ON sales.projectId = projects.id
    JOIN districts ON sales.districtId = districts.id
    JOIN streets ON sales.streetId = streets.id
    JOIN market_segments ON sales.marketSegmentId = market_segments.id
    JOIN type_of_areas ON sales.typeOfAreaId = type_of_areas.id
    JOIN property_types ON sales.propertyTypeId = property_types.id
    `;

    let condition = '';

    if (filters.projectId?.length)
      condition += ` AND projectId IN (${filters.projectId.map(escapeSQLiteValue).join(', ')})`;

    if (filters.districtId?.length)
      condition += ` AND districtId IN (${filters.districtId.map(escapeSQLiteValue).join(', ')})`;

    if (filters.streetId?.length)
      condition += ` AND streetId IN (${filters.streetId.map(escapeSQLiteValue).join(', ')})`;

    if (filters.marketSegmentId?.length)
      condition += ` AND marketSegmentId IN (${filters.marketSegmentId.map(escapeSQLiteValue).join(', ')})`;

    if (filters.propertyTypeId?.length)
      condition += ` AND propertyTypeId IN (${filters.propertyTypeId.map(escapeSQLiteValue).join(', ')})`;

    if (filters.typeOfAreaId?.length)
      condition += ` AND typeOfAreaId IN (${filters.typeOfAreaId.map(escapeSQLiteValue).join(', ')})`;

    if (filters.timePeriod) {
      if (filters.timePeriod.from)
        condition += ` AND contractDate >= ${escapeSQLiteValue(
          new Date(filters.timePeriod.from).getTime(),
        )}`;

      if (filters.timePeriod.to)
        condition += ` AND contractDate <= ${escapeSQLiteValue(
          new Date(filters.timePeriod.to).getTime(),
        )}`;
    }

    if (!isNil(filters.minimumPrice))
      condition += ` AND price >= ${escapeSQLiteValue(filters.minimumPrice)}`;

    if (!isNil(filters.maximumPrice))
      condition += ` AND price <= ${escapeSQLiteValue(filters.maximumPrice)}`;

    if (!isNil(filters.minimumLeaseYear))
      condition += ` AND leaseYear >= ${escapeSQLiteValue(filters.minimumLeaseYear)}`;

    if (!isNil(filters.maximumLeaseYear))
      condition += ` AND leaseYear <= ${escapeSQLiteValue(filters.maximumLeaseYear)}`;

    if (!isNil(filters.minimumStorey))
      condition += ` AND startFloor >= ${escapeSQLiteValue(filters.minimumStorey)}`;

    if (!isNil(filters.maximumStorey))
      condition += ` AND endFloor <= ${escapeSQLiteValue(filters.maximumStorey)}`;

    if (!isNil(filters.areaSqm))
      condition += ` AND areaSqm = ${escapeSQLiteValue(filters.areaSqm)}`;

    const total = db.exec(`SELECT COUNT(*) FROM sales WHERE 1=1 ${condition}`)[0].values[0][0];

    query += ` WHERE ${configs.lastId ? `sales.id < ${escapeSQLiteValue(configs.lastId)}` : '1 = 1'} `;
    query += ` ${condition}`;
    query += ` ORDER BY ${configs.sort} ${configs.order}`;
    query += ` LIMIT ${configs.limit}`;

    console.time('query time');
    const res = db.exec(query);
    console.timeEnd('query time');
    return {
      headers: res[0]?.columns ?? [],
      data: res[0]?.values ?? [],
      meta: {
        total,
        start: (configs.page - 1) * configs.limit,
        end: Math.min(total, configs.page * configs.limit),
      },
    };
  }

  getNumberOfUnitSoldByMonth(db: any) {
    console.time('query time');
    const rs = db.exec(/* SQL */ `
      SELECT
        strftime('%Y-%m', contractDate, 'unixepoch') AS month,
        COUNT(*) AS total
      FROM sales
      GROUP BY month
      ORDER BY month
    `)[0].values;
    console.timeEnd('query time');
    console.log(rs);
  }

  getNumberOfUnitSoldByProject(db: any) {
    console.time('query time');
    const rs = db.exec(/* SQL */ `
      SELECT
        projects.name AS project_name,
        COUNT(*) AS total
      FROM sales
      JOIN projects ON sales.projectId = projects.id
      GROUP BY projects.name
      ORDER BY total DESC
    `)[0].values;
    console.timeEnd('query time');
    console.log(rs);
  }
}
