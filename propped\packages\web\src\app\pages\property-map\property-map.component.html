<div class="w-full min-h-screen">
  <!-- Main Container -->
  <div class="px-6 md:px-12 py-6 space-y-6">
    <!-- SECTION 1: Interactive Map -->
    <div class="rounded-lg shadow-lg p-4 md:p-6">
      <div class="mb-4">
        <div>
          <span class="font-medium">{{ propertyAddress }}</span>
        </div>
      </div>

      <!-- Map Container -->
      <div
        class="w-full h-full bg-gray-200 rounded-lg relative md:h-[500px]"
        [ngClass]="
          showPropertyDetails
            ? 'flex flex-col relative md:grid md:grid-cols-2 gap-4'
            : 'relative flex-grow'
        "
      >
        <!-- Map will be rendered here -->
        <div class="relative">
          @if (amenitiesSource.features.length > 0 || hdbSourceData.features.length > 0) {
            <div class="absolute top-16 right-6 md:top-1 md:right-1 z-[999999999]">
              <button
                class="bg-white dark:bg-gray-800 text-red-600 dark:text-red-400 p-2 rounded-full shadow-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center justify-center"
                (click)="clearAmenities()"
                title="Clear all markers"
              >
                <mat-icon>cancel</mat-icon>
              </button>
            </div>
          }
          <mgl-map
            [center]="[this.lng, this.lat]"
            (mapLoad)="onMapLoad($event.target)"
            [maxBounds]="[
              [103.6, 1.16],
              [104.1, 1.47],
            ]"
            [style]="style"
            [zoom]="[zoom]"
            [bearing]="-17.6"
            [pitch]="45"
            (styleData)="updateMapStyle($event.target)"
            class="w-full h-[500px] bg-gray-200 rounded-lg relative"
            [ngClass]="
              showPropertyDetails
                ? 'h-[calc(100vh-12rem)] relative md:h-full w-full order-1'
                : 'absolute inset-0 w-screen h-full md:w-full md:h-[500px]'
            "
          >
            <mgl-marker [feature]="propertyLocation" [anchor]="'center'" [draggable]="true">
              <div
                (click)="showPropertyPopup = true"
                class="w-6 h-6 bg-blue-600 rounded-full p-2 cursor-pointer"
              ></div>
            </mgl-marker>
            @if (showPropertyPopup && propertyLocation) {
              <mgl-popup
                class="mapbox-popup-modern"
                [feature]="propertyLocation"
                [closeOnClick]="false"
                [maxWidth]="'300px'"
              >
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
                  <!-- Header -->
                  <div
                    class="bg-gradient-to-r from-blue-600 to-indigo-700 dark:from-indigo-800 dark:to-blue-900 py-2 px-3"
                  >
                    <div class="flex items-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-5 w-5 text-white mr-2"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                        />
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                        />
                      </svg>
                      <h3 class="text-base font-bold text-white">Current Location</h3>
                    </div>
                  </div>

                  <!-- Content -->
                  <div class="p-3 dark:text-white">
                    <!-- Radius Selector -->
                    <div class="mb-3">
                      <div class="flex items-center justify-between mb-2">
                        <div class="flex items-center">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-4 w-4 text-blue-500 dark:text-blue-400 mr-1.5"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4"
                            />
                          </svg>
                          <span class="text-xs font-medium">Show Nearby Within</span>
                        </div>
                        <select
                          #radiusDropdown
                          class="px-2 py-1 bg-gray-50 border border-gray-300 text-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white text-xs"
                          [value]="selectedRadius"
                          (change)="updateLocationRadius(+radiusDropdown.value, 'userLocation')"
                        >
                          <option value="1">1 km</option>
                          <option value="2">2 km</option>
                          <option value="3">3 km</option>
                          <option value="4">4 km</option>
                        </select>
                      </div>
                    </div>

                    <!-- Divider -->
                    <div class="border-t border-gray-200 dark:border-gray-700 mb-3"></div>

                    <!-- Categories -->
                    <h4
                      class="text-xs font-semibold mb-2 text-gray-600 dark:text-gray-300 flex items-center"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-3.5 w-3.5 mr-1"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M4 6h16M4 12h16M4 18h16"
                        />
                      </svg>
                      NEARBY CATEGORIES
                    </h4>

                    <div class="grid grid-cols-2 gap-1.5">
                      <!-- Schools -->
                      <div
                        class="flex items-center p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                      >
                        <input
                          type="checkbox"
                          id="schools"
                          class="w-3.5 h-3.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-1 dark:bg-gray-700 dark:border-gray-600"
                          [checked]="nearbyOptions.schools"
                          (change)="
                            handleNearbyOptionChange(
                              'schools',
                              $event.target.checked,
                              'propertyLocation'
                            )
                          "
                        />
                        <label for="schools" class="ml-1.5 text-xs font-medium flex items-center">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-3.5 w-3.5 mr-1 text-blue-500 dark:text-blue-400"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path d="M12 14l9-5-9-5-9 5 9 5z" />
                            <path
                              d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"
                            />
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222"
                            />
                          </svg>
                          Schools
                        </label>
                      </div>

                      <!-- Hawker Centers -->
                      <div
                        class="flex items-center p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                      >
                        <input
                          type="checkbox"
                          id="hawker"
                          class="w-3.5 h-3.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-1 dark:bg-gray-700 dark:border-gray-600"
                          [checked]="nearbyOptions.hawker"
                          (change)="
                            handleNearbyOptionChange(
                              'hawker',
                              $event.target.checked,
                              'propertyLocation'
                            )
                          "
                        />
                        <label for="hawker" class="ml-1.5 text-xs font-medium flex items-center">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-3.5 w-3.5 mr-1 text-orange-500 dark:text-orange-400"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                            />
                          </svg>
                          Hawker Centres
                        </label>
                      </div>

                      <!-- Bus Stops -->
                      <div
                        class="flex items-center p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                      >
                        <input
                          type="checkbox"
                          id="busStops"
                          class="w-3.5 h-3.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-1 dark:bg-gray-700 dark:border-gray-600"
                          [checked]="nearbyOptions.busStops"
                          (change)="
                            handleNearbyOptionChange(
                              'busStops',
                              $event.target.checked,
                              'propertyLocation'
                            )
                          "
                        />
                        <label for="busStops" class="ml-1.5 text-xs font-medium flex items-center">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-3.5 w-3.5 mr-1 text-red-500 dark:text-red-400"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"
                            />
                          </svg>
                          Bus Stops
                        </label>
                      </div>

                      <!-- MRT/LRT Stations -->
                      <div
                        class="flex items-center p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                      >
                        <input
                          type="checkbox"
                          id="mrt"
                          class="w-3.5 h-3.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-1 dark:bg-gray-700 dark:border-gray-600"
                          [checked]="nearbyOptions.mrt"
                          (change)="
                            handleNearbyOptionChange(
                              'mrt',
                              $event.target.checked,
                              'propertyLocation'
                            )
                          "
                        />
                        <label for="mrt" class="ml-1.5 text-xs font-medium flex items-center">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-3.5 w-3.5 mr-1 text-green-500 dark:text-green-400"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M13 10V3L4 14h7v7l9-11h-7z"
                            />
                          </svg>
                          MRT/LRT Stations
                        </label>
                      </div>

                      <!-- Sales Listings
                    <div
                      class="flex items-center p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    >
                      <input
                        type="checkbox"
                        id="salesListings"
                        class="w-3.5 h-3.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-1 dark:bg-gray-700 dark:border-gray-600"
                        [checked]="nearbyOptions.salesListings"
                        (change)="
                          handleNearbyOptionChange(
                            'salesListings',
                            $event.target.checked,
                            'userLocation'
                          )
                        "
                      />
                      <label
                        for="salesListings"
                        class="ml-1.5 text-xs font-medium flex items-center"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-3.5 w-3.5 mr-1 text-purple-500 dark:text-purple-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                          />
                        </svg>
                        Sales Listings
                      </label>
                    </div> -->

                      <!-- HDB Data -->
                      <div
                        class="flex items-center p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                      >
                        <input
                          type="checkbox"
                          id="hdbData"
                          class="w-3.5 h-3.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-1 dark:bg-gray-700 dark:border-gray-600"
                          [checked]="nearbyOptions.hdbData"
                          (change)="
                            handleNearbyOptionChange(
                              'hdbData',
                              $event.target.checked,
                              'propertyLocation'
                            )
                          "
                        />
                        <label for="hdbData" class="ml-1.5 text-xs font-medium flex items-center">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-3.5 w-3.5 mr-1 text-yellow-500 dark:text-yellow-400"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                            />
                          </svg>
                          HDB Data
                        </label>
                      </div>

                      <!-- Healthcare -->
                      <div
                        class="flex items-center p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                      >
                        <input
                          type="checkbox"
                          id="healthcare"
                          class="w-3.5 h-3.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-1 dark:bg-gray-700 dark:border-gray-600"
                          [checked]="nearbyOptions.healthcare"
                          (change)="
                            handleNearbyOptionChange(
                              'healthcare',
                              $event.target.checked,
                              'propertyLocation'
                            )
                          "
                        />
                        <label
                          for="healthcare"
                          class="ml-1.5 text-xs font-medium flex items-center"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-3.5 w-3.5 mr-1 text-red-500 dark:text-red-400"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                            />
                          </svg>
                          Healthcare
                        </label>
                      </div>
                    </div>

                    <!-- Footer -->
                    <div class="flex justify-end mt-3">
                      <button
                        class="px-3 py-1 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white text-xs font-medium rounded-md shadow-sm transition-all duration-200 focus:outline-none focus:ring-1 focus:ring-offset-1 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
                        (click)="showPropertyPopup = false"
                      >
                        <div class="flex items-center">
                          <span>Close</span>
                        </div>
                      </button>
                    </div>
                  </div>
                </div>
              </mgl-popup>
            }
            <mgl-geojson-source id="radius-circle" [data]="radiusCircleSource">
              <mgl-layer
                id="radius-circle-fill"
                type="fill"
                source="radius-circle"
                [paint]="{
                  'fill-color': '#FF9800',
                  'fill-opacity': 0.1,
                }"
              />
              <mgl-layer
                id="radius-circle-line"
                type="line"
                source="radius-circle"
                [paint]="{
                  'line-color': '#FF9800',
                  'line-width': 2,
                  'line-opacity': 0.8,
                }"
              />
            </mgl-geojson-source>

            <!-- School markers -->
            <mgl-geojson-source id="amenities" [data]="amenitiesSource">
              <mgl-layer
                id="amenities-layer"
                type="circle"
                source="amenities"
                [paint]="{
                  'circle-radius': 8,
                  'circle-color': '#FF9800',
                  'circle-stroke-width': 1,
                  'circle-stroke-color': '#000000',
                }"
                (layerClick)="getAmenitiesPopUp($event)"
              />
              @if (showAmenitiesPopup && amenitiesFeature) {
                <mgl-popup
                  class="mapbox-popup-modern"
                  [feature]="amenitiesFeature"
                  [closeOnClick]="false"
                  [closeButton]="false"
                  [maxWidth]="'300px'"
                >
                  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
                    <!-- Header with dynamic color based on amenity type -->
                    <div class="w-full flex justify-end">
                      <mat-icon (click)="showAmenitiesPopup = false">close</mat-icon>
                    </div>
                    <div
                      [ngClass]="{
                        'bg-blue-600': amenitiesPopUpContent.type === 'school',
                        'bg-green-600': amenitiesPopUpContent.type === 'mrt',
                        'bg-red-600': amenitiesPopUpContent.type === 'healthcare',
                        'bg-red-500': amenitiesPopUpContent.type === 'busStop',
                        'bg-orange-500': amenitiesPopUpContent.type === 'hawker',
                        'bg-purple-600': ![
                          'school',
                          'mrt',
                          'healthcare',
                          'busStop',
                          'hawker',
                        ].includes(amenitiesPopUpContent.type),
                      }"
                      class="py-2 px-3 text-white"
                    >
                      <div class="flex items-center">
                        <!-- Dynamic icon based on amenity type -->
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-5 w-5 text-white mr-2"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <!-- School icon -->
                          @if (amenitiesPopUpContent.type === 'school') {
                            <path
                              d="M12 14l9-5-9-5-9 5 9 5z"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                            />
                            <path
                              d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                            />
                          }
                          <!-- MRT icon -->
                          @if (amenitiesPopUpContent.type === 'mrt') {
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M13 10V3L4 14h7v7l9-11h-7z"
                            />
                          }
                          <!-- Healthcare icon -->
                          @if (amenitiesPopUpContent.type === 'healthcare') {
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                            />
                          }
                          <!-- Bus Stop icon -->
                          @if (amenitiesPopUpContent.type === 'busStop') {
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"
                            />
                          }
                          <!-- Hawker icon -->
                          @if (amenitiesPopUpContent.type === 'hawker') {
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                            />
                          }
                          <!-- Default icon for other types -->
                          @if (
                            !['school', 'mrt', 'healthcare', 'busStop', 'hawker'].includes(
                              amenitiesPopUpContent.type
                            )
                          ) {
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                            />
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                            />
                          }
                        </svg>
                        <h3 class="text-base font-bold text-white">
                          {{ amenitiesPopUpContent.name }}
                        </h3>
                      </div>
                      <!-- Subtitle if category exists -->
                      @if (amenitiesPopUpContent.category) {
                        <div class="text-xs text-white opacity-80 mt-1 ml-7">
                          {{ amenitiesPopUpContent.category }}
                        </div>
                      }
                    </div>

                    <!-- Content -->
                    <div class="p-3 dark:text-white text-sm">
                      <!-- School specific content -->
                      @if (amenitiesPopUpContent.type === 'school') {
                        <div class="space-y-1 text-gray-800 dark:text-gray-200">
                          @if (amenitiesPopUpContent.address) {
                            <p>
                              <span class="font-semibold">Address:</span>
                              {{ amenitiesPopUpContent.address }}
                            </p>
                          }
                          @if (amenitiesPopUpContent.postal) {
                            <p>
                              <span class="font-semibold">Postal Code:</span>
                              {{ amenitiesPopUpContent.postal }}
                            </p>
                          }
                          @if (amenitiesPopUpContent.telephoneNumber) {
                            <p>
                              <span class="font-semibold">Telephone:</span>
                              {{ amenitiesPopUpContent.telephoneNumber }}
                            </p>
                          }
                          @if (amenitiesPopUpContent.email) {
                            <p>
                              <span class="font-semibold">Email:</span>
                              <a
                                href="mailto:{{ amenitiesPopUpContent.email }}"
                                class="text-blue-600 dark:text-blue-400 underline"
                              >
                                {{ amenitiesPopUpContent.email }}
                              </a>
                            </p>
                          }
                          @if (amenitiesPopUpContent.town) {
                            <p>
                              <span class="font-semibold">Town:</span>
                              {{ amenitiesPopUpContent.town }}
                            </p>
                          }
                          @if (amenitiesPopUpContent.schoolLevel) {
                            <p>
                              <span class="font-semibold">Level:</span>
                              {{ amenitiesPopUpContent.schoolLevel }}
                            </p>
                          }
                          @if (amenitiesPopUpContent.schoolType) {
                            <p>
                              <span class="font-semibold">Type:</span>
                              {{ amenitiesPopUpContent.schoolType }}
                            </p>
                          }
                        </div>
                      }

                      <!-- MRT specific content -->
                      @if (amenitiesPopUpContent.type === 'mrt') {
                        <div class="space-y-1 text-gray-800 dark:text-gray-200">
                          @if (amenitiesPopUpContent.buildingName) {
                            <p>
                              <span class="font-semibold">Building:</span>
                              {{ amenitiesPopUpContent.buildingName }}
                            </p>
                          }
                          @if (amenitiesPopUpContent.alphaNumericCode) {
                            <p>
                              <span class="font-semibold">Code:</span>
                              {{ amenitiesPopUpContent.alphaNumericCode }}
                            </p>
                          }
                          @if (amenitiesPopUpContent.address) {
                            <p>
                              <span class="font-semibold">Address:</span>
                              {{ amenitiesPopUpContent.address }}
                            </p>
                          }
                          <a
                            href="https://www.google.com/maps/search/?api=1&query={{
                              amenitiesPopUpContent.coordinates[1]
                            }},{{ amenitiesPopUpContent.coordinates[0] }}"
                            target="_blank"
                            class="mt-2 inline-flex items-center gap-1 text-blue-600 dark:text-blue-400 underline text-xs"
                          >
                            <mat-icon>location_on</mat-icon>
                            Get Directions
                          </a>
                        </div>
                      }

                      <!-- Healthcare specific content -->
                      @if (amenitiesPopUpContent.type === 'healthcare') {
                        <div class="space-y-1 text-gray-800 dark:text-gray-200">
                          @if (amenitiesPopUpContent.buildingName) {
                            <p>
                              <span class="font-semibold">Building:</span>
                              {{ amenitiesPopUpContent.buildingName }}
                            </p>
                          }
                          @if (amenitiesPopUpContent.address) {
                            <p>
                              <span class="font-semibold">Address:</span>
                              {{ amenitiesPopUpContent.address }}
                            </p>
                          }
                          @if (amenitiesPopUpContent.postal) {
                            <p>
                              <span class="font-semibold">Postal Code:</span>
                              {{ amenitiesPopUpContent.postal }}
                            </p>
                          }
                          @if (amenitiesPopUpContent.facilityType) {
                            <p>
                              <span class="font-semibold">Facility Type:</span>
                              {{ amenitiesPopUpContent.facilityType }}
                            </p>
                          }
                          <a
                            href="https://www.google.com/maps/search/?api=1&query={{
                              amenitiesPopUpContent.coordinates[1]
                            }},{{ amenitiesPopUpContent.coordinates[0] }}"
                            target="_blank"
                            class="mt-2 inline-flex items-center text-blue-600 dark:text-blue-400 underline text-xs"
                          >
                            <mat-icon>directions</mat-icon>
                            Get Directions
                          </a>
                        </div>
                      }

                      <!-- Bus Stop specific content -->
                      @if (amenitiesPopUpContent.type === 'busStop') {
                        <div class="space-y-1 text-gray-800 dark:text-gray-200">
                          @if (amenitiesPopUpContent.details) {
                            <p>
                              <span class="font-semibold">Landmark:</span>
                              {{ amenitiesPopUpContent.details }}
                            </p>
                          }
                        </div>
                      }

                      <!-- Hawker specific content -->
                      @if (amenitiesPopUpContent.type === 'hawker') {
                        <div class="space-y-1 text-gray-800 dark:text-gray-200">
                          @if (amenitiesPopUpContent.photoUrl) {
                            <img
                              src="{{ amenitiesPopUpContent.photoUrl }}"
                              alt="Hawker Center"
                              class="w-full mx-auto h-32 object-cover rounded mb-2"
                            />
                          }
                          @if (amenitiesPopUpContent.description) {
                            <p class="mb-1 text-gray-600 dark:text-gray-400 italic text-xs">
                              {{ amenitiesPopUpContent.description }}
                            </p>
                          }
                          @if (amenitiesPopUpContent.address) {
                            <p>
                              <span class="font-semibold">Address:</span>
                              {{ amenitiesPopUpContent.address }}
                            </p>
                          }
                          @if (amenitiesPopUpContent.postalCode) {
                            <p>
                              <span class="font-semibold">Postal Code:</span>
                              {{ amenitiesPopUpContent.postalCode }}
                            </p>
                          }
                          @if (amenitiesPopUpContent.stallCount) {
                            <p>
                              <span class="font-semibold">Food Stalls:</span>
                              {{ amenitiesPopUpContent.stallCount }}
                            </p>
                          }
                        </div>
                      }

                      <!-- Default content for other types -->
                      @if (
                        !['school', 'mrt', 'healthcare', 'busStop', 'hawker'].includes(
                          amenitiesPopUpContent.type
                        )
                      ) {
                        <div class="space-y-1 text-gray-800 dark:text-gray-200">
                          @if (amenitiesPopUpContent.address) {
                            <p>
                              <span class="font-semibold">Address:</span>
                              {{ amenitiesPopUpContent.address }}
                            </p>
                          }
                          @if (amenitiesPopUpContent.description) {
                            <p>{{ amenitiesPopUpContent.description }}</p>
                          }
                        </div>
                      }
                    </div>
                  </div>
                </mgl-popup>
              }
            </mgl-geojson-source>

            <mgl-geojson-source id="hdb-markers" [data]="hdbSourceData">
              @if (hdbSourceData?.features?.length > 0) {
                @for (feature of hdbSourceData.features; track feature.properties.blockNumber) {
                  <mgl-marker
                    (click)="getPropertyDetails(feature)"
                    [lngLat]="feature.geometry.coordinates"
                    [offset]="[0, -20]"
                  >
                    <div
                      (click)="getPropertyDetails(feature)"
                      class="w-6 h-6 rounded-full text-white text-xs flex items-center justify-center shadow-md"
                      [ngStyle]="{
                        'background-color': townColors[feature.properties.town] || '#999999',
                      }"
                      title="Block {{ feature.properties.blockNumber }}"
                    >
                      {{ feature.properties.blockNumber.substring(3) }}
                    </div>
                  </mgl-marker>
                }
              }
            </mgl-geojson-source>
          </mgl-map>
        </div>

        @if (showPropertyDetails && selectedProperty) {
          <div
            class="w-full overflow-hidden h-full bg-white dark:bg-gray-800 shadow-lg rounded-lg order-2 md:order-2 md:overflow-auto"
            [ngClass]="{
              'md:h-full': showPropertyDetails,
              'h-[calc(100vh-20rem)]': !showHDBListings && !showCondoListings,
            }"
          >
            <!-- Header with close button -->
            <div
              class="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700 sticky top-0 bg-white dark:bg-gray-800 z-10"
            >
              <h2 class="text-xl font-bold text-gray-800 dark:text-white">Property Details</h2>
              <button
                class="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700"
                (click)="closePropertyDetails()"
                title="Close"
              >
                <mat-icon>close</mat-icon>
              </button>
            </div>

            <!-- Property Information -->
            <div class="p-4 space-y-4">
              <!-- HDB Block Details -->
              @if (selectedProperty.blockNumber) {
                <div>
                  <h3 class="text-lg font-semibold">Block {{ selectedProperty.blockNumber }}</h3>
                  <p class="text-sm font-medium">{{ selectedProperty.hdbTown }}</p>
                  <p class="text-sm italic">{{ selectedProperty?.projectName || 'N/A' }}</p>

                  <div class="border-t border-gray-300 my-2"></div>

                  <p class="text-sm">
                    <strong>Street:</strong> {{ selectedProperty.streetAddress }}
                  </p>
                  <p class="text-sm">
                    <strong>Postal Code:</strong> {{ selectedProperty.postalCode }}
                  </p>
                  <p class="text-sm">
                    <strong>Lease Start:</strong> {{ selectedProperty?.leaseStart || 'N/A' }}
                  </p>

                  <div class="border-t border-gray-300 my-2"></div>

                  <p class="text-sm">
                    <strong>{{ selectedProperty.totalFloors }}</strong> Floors
                  </p>
                  <p class="text-sm">
                    <strong>{{ selectedProperty.unitsPerFloor }}</strong> Units Per Floor
                  </p>
                  <p class="text-sm font-semibold">
                    Total <strong>{{ selectedProperty.totalUnits }}</strong> Units
                  </p>
                </div>
              }

              <!-- Condo Details -->
              @if (selectedProperty.projectName && !selectedProperty.blockNumber) {
                <div>
                  <h3 class="text-lg font-semibold">{{ selectedProperty.projectName }}</h3>
                  <p class="text-sm">{{ selectedProperty.address }}</p>

                  <div class="border-t border-gray-300 my-2"></div>

                  <p class="text-sm"><strong>District:</strong> {{ selectedProperty.district }}</p>
                  <p class="text-sm">
                    <strong>Year Completed:</strong> {{ selectedProperty.yearOfCompletion }}
                  </p>
                  <p class="text-sm"><strong>Tenure:</strong> {{ selectedProperty.tenure }}</p>
                  <p class="text-sm">
                    <strong>Total Units:</strong> {{ selectedProperty.totalUnits }}
                  </p>
                </div>
              }

              <!-- Floor Plans if available -->
              @if (selectedProperty.floorPlans?.length) {
                <div class="mt-4">
                  <p class="font-semibold text-sm">Floor Plans:</p>
                  <img
                    src="{{ selectedProperty.floorPlans[0] }}"
                    class="mt-2 border rounded-lg w-40 shadow-md"
                    alt="Floor Plan"
                  />
                </div>
              }

              <!-- Ethnic Query for HDB -->
              @if (selectedProperty.blockNumber) {
                <div class="mt-2 p-4 border rounded-lg shadow-sm">
                  <p class="font-semibold text-sm mb-2">
                    Ethnic Query
                    <span class="italic text-gray-500">(Source: <strong>HDB</strong>)</span>:
                  </p>

                  <div class="text-xs space-y-4">
                    <!-- Singaporean Citizen / Malaysian SPR -->
                    <div>
                      <p class="font-semibold">Singaporean Citizen / Malaysian SPR</p>
                      <ul class="ml-4 mt-1 space-y-1">
                        <li>
                          <span class="italic font-medium">Chinese:</span>
                          <br />
                          <span class="italic">
                            You can only buy from Chinese flat sellers, regardless of their
                            citizenship.
                          </span>
                        </li>
                        <li>
                          <span class="italic font-medium">Malay:</span>
                          <br />
                          <span class="italic">
                            You can buy from any flat seller, regardless of their ethnic group and
                            citizenship.
                          </span>
                        </li>
                        <li>
                          <span class="italic font-medium">Indian and Other Ethnic Groups:</span>
                          <br />
                          <span class="italic">
                            You can buy from any flat seller regardless of their ethnic group and
                            citizenship.
                          </span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              }
            </div>

            <!-- Transaction Data Section -->
            @if (propertyTransactionData && propertyTransactionData.data?.length > 0) {
              <div class="p-4 border-t border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold mb-4">{{ propertyTransactionData.heading }}</h3>

                <div class="overflow-x-auto">
                  <table class="w-full text-left border-collapse text-sm">
                    <thead>
                      <tr class="bg-gray-100 dark:bg-gray-700">
                        @for (header of propertyTransactionData.header; track header) {
                          <th class="px-3 py-2 font-semibold border dark:border-gray-600">
                            {{ header }}
                          </th>
                        }
                      </tr>
                    </thead>
                    <tbody>
                      @for (row of propertyTransactionData.data; track row) {
                        <tr class="border hover:bg-gray-50 dark:hover:bg-gray-700 transition-all">
                          @for (cell of row; track cell) {
                            <td class="px-3 py-2 border dark:border-gray-600">{{ cell }}</td>
                          }
                        </tr>
                      }
                    </tbody>
                  </table>
                </div>

                <!-- Toggle Chart Button -->
                <div class="mt-4 flex justify-end">
                  <button
                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-all"
                    (click)="toggleTransactionChart()"
                  >
                    {{ showTransactionChart ? 'Hide Charts' : 'Show Charts' }}
                  </button>
                </div>
              </div>
            } @else {
              <div class="p-4 border-t border-gray-200 dark:border-gray-700">
                <div class="flex flex-col items-center justify-center py-6 text-center">
                  <mat-icon class="text-gray-400 text-4xl mb-2">info</mat-icon>
                  <p class="text-gray-500 dark:text-gray-400">
                    No transaction data available for this property.
                  </p>
                </div>
              </div>
            }

            <!-- Charts Section -->
            <!-- Charts Section -->
            @if (showTransactionChart) {
              @if (transactionMedianPrices.length > 0) {
                <div class="p-4 border-t border-gray-200 dark:border-gray-700">
                  <h3 class="text-lg font-semibold mb-4">Price & PSF Trends</h3>

                  <!-- Median Price Chart -->
                  <div class="mb-6 border rounded-lg p-4 shadow-sm">
                    <h4 class="text-sm font-bold text-center mb-2">Monthly Median Price</h4>
                    <canvas #medianPriceChart></canvas>
                  </div>

                  <!-- Average PSF Chart -->
                  <div class="border rounded-lg p-4 shadow-sm">
                    <h4 class="text-sm font-bold text-center mb-2">Monthly Average PSF</h4>
                    <canvas #avgPsfChart></canvas>
                  </div>
                </div>
              } @else {
                <div class="p-4 border-t border-gray-200 dark:border-gray-700">
                  <div class="flex flex-col items-center justify-center py-6 text-center">
                    <mat-icon class="text-gray-400 text-4xl mb-2">bar_chart</mat-icon>
                    <p class="text-gray-500 dark:text-gray-400">
                      No chart data available for this property.
                    </p>
                  </div>
                </div>
              }
            }
          </div>
        }
        <!-- Loading Overlay -->
        @if (isMapLoading) {
          <div
            class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-70 rounded-lg"
          >
            <div class="text-center">
              <div
                class="inline-block animate-spin rounded-full h-8 w-8 border-4 border-blue-500 border-t-transparent"
              ></div>
              <p class="mt-2 text-gray-600">Loading map...</p>
            </div>
          </div>
        }
      </div>
    </div>

    <!-- SECTION 2: HDB Ethnic Query Info -->
    <div class="rounded-lg shadow-lg p-4 md:p-6">
      <h2 class="text-xl text-center font-semibold mb-4">HDB Ethnic Query</h2>
      <div
        class="w-full border border-gray-200 bg-gray-100 p-2 flex justify-between items-center dark:bg-gray-800"
      >
        <h3 class="text-sm font-semibold md:text-lg">
          Ethnic Quota <span class="text-red-500 font-bold">Seller</span> Query (Source: HDB)
        </h3>
        <mat-icon (click)="showEthnicSellerQuery = !showEthnicSellerQuery">{{
          showEthnicSellerQuery ? 'remove' : 'add'
        }}</mat-icon>
      </div>

      @if (showEthnicSellerQuery) {
        <!-- Ethnic Query Table -->
        <div class="space-y-4">
          <!-- Singapore Citizen / Malaysian SPR -->
          <div class="p-2">
            <h3 class="font-medium mb-2">Singapore Citizen / Malaysian SPR</h3>
            <div class="bg-gray-50 border rounded-lg overflow-hidden dark:bg-gray-800">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-100 dark:bg-gray-700">
                  <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-bold uppercase">
                      Ethnic Group
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-bold uppercase" s>
                      Policy
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200">
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold">Chinese</td>
                    <td class="px-6 py-4 text-sm">
                      You can only buy from Chinese flat sellers, regardless of their citizenship.
                    </td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold">Malay</td>
                    <td class="px-6 py-4 text-sm">
                      You can buy from any flat seller, regardless of ethnic group or citizenship.
                    </td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold">
                      Indian and Other Ethnic Groups
                    </td>
                    <td class="px-6 py-4 text-sm">
                      You can buy from any flat seller, regardless of ethnic group or citizenship.
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- Non-Malaysian SPR -->
          <div class="p-2">
            <h3 class="font-medium my-2">Non-Malaysian SPR</h3>
            <div
              class="bg-gray-50 border border-gray-200 rounded-lg overflow-hidden dark:bg-gray-800"
            >
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-100 dark:bg-gray-700">
                  <tr>
                    <th
                      scope="col"
                      class="px-6 py-3 text-left text-xs font-bold uppercase tracking-wider"
                    >
                      Ethnic Group
                    </th>
                    <th
                      scope="col"
                      class="px-6 py-3 text-left text-xs font-bold uppercase tracking-wider"
                    >
                      Policy
                    </th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold">Chinese</td>
                    <td class="px-6 py-4 text-sm">
                      You can only buy from Chinese flat sellers, regardless of their citizenship.
                    </td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold">Malay</td>
                    <td class="px-6 py-4 text-sm">
                      You can buy from any flat seller, regardless of ethnic group or citizenship.
                    </td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold">
                      Indian and Other Ethnic Groups
                    </td>
                    <td class="px-6 py-4 text-sm">
                      You can buy from any flat seller, regardless of ethnic group or citizenship.
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      }

      <div
        class="w-full mt-4 bg-gray-100 border border-gray-200 p-2 flex justify-between items-center dark:bg-gray-800"
      >
        <h3 class="text-sm font-semibold md:text-lg">
          Ethnic Quota <span class="text-red-500 font-bold">Buyer</span> Query (Source: HDB)
        </h3>
        <mat-icon (click)="showEthnicBuyerQuery = !showEthnicBuyerQuery">{{
          showEthnicBuyerQuery ? 'remove' : 'add'
        }}</mat-icon>
      </div>

      @if (showEthnicBuyerQuery) {
        <!-- Ethnic Query Table -->
        <div class="space-y-6">
          <!-- Singapore Citizen / Malaysian SPR -->
          <div>
            <h3 class="font-medium my-2">Singapore Citizen / Malaysian SPR</h3>
            <div class="border rounded-lg overflow-hidden">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-100 dark:bg-gray-700">
                  <tr>
                    <th
                      scope="col"
                      class="px-6 py-3 text-left text-xs font-bold uppercase tracking-wider"
                    >
                      Ethnic Group
                    </th>
                    <th
                      scope="col"
                      class="px-6 py-3 text-left text-xs font-bold uppercase tracking-wider"
                    >
                      Policy
                    </th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold">Chinese</td>
                    <td class="px-6 py-4 text-sm">
                      You can only buy from Chinese flat sellers, regardless of their citizenship.
                    </td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold">Malay</td>
                    <td class="px-6 py-4 text-sm">
                      You can buy from any flat seller, regardless of ethnic group or citizenship.
                    </td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold">
                      Indian and Other Ethnic Groups
                    </td>
                    <td class="px-6 py-4 text-sm">
                      You can buy from any flat seller, regardless of ethnic group or citizenship.
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- Non-Malaysian SPR -->
          <div>
            <h3 class="font-medium my-2">Non-Malaysian SPR</h3>
            <div class="border rounded-lg overflow-hidden">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-100 dark:bg-gray-700">
                  <tr>
                    <th
                      scope="col"
                      class="px-6 py-3 text-left text-xs font-bold uppercase tracking-wider"
                    >
                      Ethnic Group
                    </th>
                    <th
                      scope="col"
                      class="px-6 py-3 text-left text-xs font-bold uppercase tracking-wider"
                    >
                      Policy
                    </th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold">Chinese</td>
                    <td class="px-6 py-4 text-sm">
                      You can only buy from Chinese flat sellers, regardless of their citizenship.
                    </td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold">Malay</td>
                    <td class="px-6 py-4 text-sm">
                      You can buy from any flat seller, regardless of ethnic group or citizenship.
                    </td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold">
                      Indian and Other Ethnic Groups
                    </td>
                    <td class="px-6 py-4 text-sm">
                      You can buy from any flat seller, regardless of ethnic group or citizenship.
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      }
    </div>
  </div>
</div>
