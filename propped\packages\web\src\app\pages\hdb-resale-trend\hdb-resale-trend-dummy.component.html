<!-- Modern HDB Resale Trends & Top Performers Dashboard -->
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20">
  <!-- Modern Header with Enhanced Visual Hierarchy -->
  <div class="bg-white/80 backdrop-blur-sm border-b border-slate-200/60 sticky top-0 z-10">
    <div class="max-w-7xl mx-auto px-6 py-6">
      <div class="flex items-center gap-4">
        <!-- Enhanced Icon with Gradient -->
        <div class="relative">
          <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl shadow-lg shadow-red-500/25 flex items-center justify-center transform hover:scale-105 transition-transform duration-200">
            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
            </svg>
          </div>
          <div class="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
        </div>

        <!-- Enhanced Typography -->
        <div class="flex-1">
          <h1 class="text-3xl font-bold bg-gradient-to-r from-slate-900 via-slate-800 to-slate-700 bg-clip-text text-transparent">
            HDB Resale Trends & Top Performers
          </h1>
          <p class="text-slate-600 mt-1 font-medium">
            Analyze property resale trends and identify top performing locations with real-time insights
          </p>
        </div>

        <!-- Status Indicator -->
        <div class="hidden md:flex items-center gap-2 px-4 py-2 bg-green-50 border border-green-200 rounded-full">
          <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span class="text-sm font-medium text-green-700">Live Data</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content Container -->
  <div class="max-w-7xl mx-auto px-6 py-8 space-y-8">

    <!-- Enhanced Filters Section -->
    <div class="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl shadow-slate-200/50 border border-slate-200/60 p-8">
      <!-- Filter Header -->
      <div class="flex items-center justify-between mb-8">
        <div>
          <h2 class="text-xl font-bold text-slate-900 mb-1">Filter & Analyze</h2>
          <p class="text-slate-600 text-sm">Customize your data view with advanced filtering options</p>
        </div>
        <button class="px-4 py-2 text-sm font-medium text-slate-600 hover:text-slate-900 border border-slate-300 rounded-lg hover:bg-slate-50 transition-all duration-200">
          Reset Filters
        </button>
      </div>

      <!-- Row 1: Location & Time Period -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <!-- Town Selection -->
        <div class="space-y-2">
          <label class="block text-sm font-semibold text-slate-700">
            <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
            </svg>
            Town
          </label>
          <select class="w-full bg-white border border-slate-300 rounded-xl px-4 py-3 text-sm font-medium text-slate-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-sm hover:border-slate-400 transition-all duration-200">
            <option>All Towns</option>
            <option>Ang Mo Kio</option>
            <option>Bedok</option>
            <option>Bishan</option>
            <option>Tampines</option>
          </select>
        </div>

        <!-- Street Selection -->
        <div class="space-y-2">
          <label class="block text-sm font-semibold text-slate-700">
            <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2h8.5v7h3v-7H21z"/>
            </svg>
            Street
          </label>
          <select class="w-full bg-white border border-slate-300 rounded-xl px-4 py-3 text-sm font-medium text-slate-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-sm hover:border-slate-400 transition-all duration-200">
            <option>All Streets</option>
            <option>Ang Mo Kio Ave 1</option>
            <option>Ang Mo Kio Ave 2</option>
          </select>
        </div>

        <!-- Time Period -->
        <div class="space-y-2">
          <label class="block text-sm font-semibold text-slate-700">
            <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
            </svg>
            Time Period
          </label>
          <div class="flex items-center gap-3">
            <input type="date" value="2024-12-17" class="flex-1 bg-white border border-slate-300 rounded-xl px-4 py-3 text-sm font-medium text-slate-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-sm hover:border-slate-400 transition-all duration-200">
            <div class="px-2 py-1 bg-slate-100 rounded-lg">
              <svg class="w-4 h-4 text-slate-500" fill="currentColor" viewBox="0 0 24 24">
                <path d="M8.59 16.59L10 18l6-6-6-6-1.41 1.41L13.17 12z"/>
              </svg>
            </div>
            <input type="date" value="2025-06-17" class="flex-1 bg-white border border-slate-300 rounded-xl px-4 py-3 text-sm font-medium text-slate-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-sm hover:border-slate-400 transition-all duration-200">
          </div>
        </div>
      </div>

      <!-- Row 2: Flat Type Selection -->
      <div class="space-y-4 mb-8">
        <label class="block text-sm font-semibold text-slate-700">
          <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 24 24">
            <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
          </svg>
          Flat Type
        </label>
        <div class="flex flex-wrap gap-3">
          <button class="group flex items-center gap-3 px-5 py-3 bg-slate-100 hover:bg-blue-50 border border-slate-200 hover:border-blue-300 rounded-xl text-sm font-medium text-slate-700 hover:text-blue-700 transition-all duration-200 transform hover:scale-105">
            <div class="w-8 h-8 bg-white group-hover:bg-blue-100 rounded-lg flex items-center justify-center shadow-sm">
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
              </svg>
            </div>
            1 Room
          </button>
          <button class="group flex items-center gap-3 px-5 py-3 bg-slate-100 hover:bg-blue-50 border border-slate-200 hover:border-blue-300 rounded-xl text-sm font-medium text-slate-700 hover:text-blue-700 transition-all duration-200 transform hover:scale-105">
            <div class="w-8 h-8 bg-white group-hover:bg-blue-100 rounded-lg flex items-center justify-center shadow-sm">
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
              </svg>
            </div>
            2 Room
          </button>
          <button class="group flex items-center gap-3 px-5 py-3 bg-slate-100 hover:bg-blue-50 border border-slate-200 hover:border-blue-300 rounded-xl text-sm font-medium text-slate-700 hover:text-blue-700 transition-all duration-200 transform hover:scale-105">
            <div class="w-8 h-8 bg-white group-hover:bg-blue-100 rounded-lg flex items-center justify-center shadow-sm">
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
              </svg>
            </div>
            3 Room
          </button>
          <button class="group flex items-center gap-3 px-5 py-3 bg-blue-600 hover:bg-blue-700 border border-blue-600 rounded-xl text-sm font-medium text-white transition-all duration-200 transform hover:scale-105 shadow-lg shadow-blue-600/25">
            <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center shadow-sm">
              <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
              </svg>
            </div>
            4 Room
          </button>
          <button class="group flex items-center gap-3 px-5 py-3 bg-slate-100 hover:bg-blue-50 border border-slate-200 hover:border-blue-300 rounded-xl text-sm font-medium text-slate-700 hover:text-blue-700 transition-all duration-200 transform hover:scale-105">
            <div class="w-8 h-8 bg-white group-hover:bg-blue-100 rounded-lg flex items-center justify-center shadow-sm">
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
              </svg>
            </div>
            5 Room
          </button>
        </div>
      </div>

      <!-- Row 3: Sort By & Chart Type (Single Row Layout) -->
      <!-- <div class="grid grid-cols-1 xl:grid-cols-2 gap-8"> -->
        <div class="flex w-full gap-6 ">
        <!-- Sort By -->
        <div class="w-1/2 space-y-4">
          <label class="block text-sm font-semibold text-slate-700">
            <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M3 18h6v-2H3v2zM3 6v2h18V6H3zm0 7h12v-2H3v2z"/>
            </svg>
            Sort By
          </label>
          <div class="grid grid-cols-3 gap-2">
            <button class="px-3 py-3 w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl text-sm font-semibold hover:from-blue-700 hover:to-blue-800 transition-all duration-200 shadow-lg shadow-blue-600/25 transform hover:scale-105">
              <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 24 24">
                <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
              </svg>
              Month
            </button>
            <button class="px-3 py-3 w-full bg-slate-100 hover:bg-slate-200 border border-slate-200 hover:border-slate-300 rounded-xl text-sm font-semibold text-slate-700 hover:text-slate-900 transition-all duration-200 transform hover:scale-105">
              <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 24 24">
                <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
              </svg>
              Quarter
            </button>
            <button class="px-3 py-3 w-full bg-slate-100 hover:bg-slate-200 border border-slate-200 hover:border-slate-300 rounded-xl text-sm font-semibold text-slate-700 hover:text-slate-900 transition-all duration-200 transform hover:scale-105">
              <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 24 24">
                <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
              </svg>
              Year
            </button>
          </div>
        </div>

        <!-- Chart Type -->
        <div class="w-1/2 space-y-4">
          <label class="block text-sm font-semibold text-slate-700">
            <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M3.5 18.49l6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L3.5 15.9z"/>
            </svg>
            Chart Type
          </label>
          <div class="grid grid-cols-4 gap-2">
            <button class="px-3 py-3 w-full bg-slate-100 hover:bg-slate-200 border border-slate-200 hover:border-slate-300 rounded-xl text-xs font-semibold text-slate-700 hover:text-slate-900 transition-all duration-200 flex items-center justify-center gap-1">
              <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z"/>
              </svg>
              Median PSF
            </button>
            <button class="px-3 py-3 w-full bg-slate-100 hover:bg-slate-200 border border-slate-200 hover:border-slate-300 rounded-xl text-xs font-semibold text-slate-700 hover:text-slate-900 transition-all duration-200 flex items-center justify-center gap-1">
              <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                <path d="M3.5 18.49l6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L3.5 15.9z"/>
              </svg>
              Average Price
            </button>
            <button class="px-3 py-3 w-full bg-gradient-to-r from-emerald-600 to-emerald-700 text-white rounded-xl text-xs font-semibold hover:from-emerald-700 hover:to-emerald-800 transition-all duration-200 shadow-lg shadow-emerald-600/25 flex items-center justify-center gap-1">
              <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                <path d="M3.5 18.49l6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L3.5 15.9z"/>
              </svg>
              Median Price
            </button>
            <button class="px-3 py-3 w-full bg-slate-100 hover:bg-slate-200 border border-slate-200 hover:border-slate-300 rounded-xl text-xs font-semibold text-slate-700 hover:text-slate-900 transition-all duration-200 flex items-center justify-center gap-1">
              <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z"/>
              </svg>
              Capital Gain(PSF)
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Enhanced Main Content: Chart and Table -->
    <div class="grid grid-cols-1 xl:grid-cols-5 gap-8">
      <!-- Left Column: Enhanced Resale Trend Chart -->
      <div class="xl:col-span-3 bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl shadow-slate-200/50 border border-slate-200/60 p-8">
        <!-- Chart Header -->
        <div class="flex items-center justify-between mb-6">
          <div>
            <h2 class="text-2xl font-bold text-slate-900 mb-2">Resale Trend Analysis</h2>
            <div class="flex flex-wrap gap-2 text-xs">
              <span class="px-3 py-1.5 bg-blue-50 border border-blue-200 rounded-full text-blue-700 font-medium">Period: Dec 2024 - Jun 2025</span>
              <span class="px-3 py-1.5 bg-emerald-50 border border-emerald-200 rounded-full text-emerald-700 font-medium">Sort By: Year</span>
              <span class="px-3 py-1.5 bg-purple-50 border border-purple-200 rounded-full text-purple-700 font-medium">Chart Type: Average PSF</span>
            </div>
          </div>
          <!-- Chart Actions -->
          <div class="flex gap-2">
            <button class="p-2 text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-lg transition-all duration-200">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </button>
            <button class="p-2 text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-lg transition-all duration-200">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M19 12v7H5v-7M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
            </button>
          </div>
        </div>

        <!-- Enhanced Chart Container -->
        <div class="relative h-96 bg-gradient-to-br from-slate-50 to-blue-50/30 rounded-xl border border-slate-200/60 overflow-hidden">
          <!-- Chart Background Pattern -->
          <div class="absolute inset-0 opacity-5">
            <div class="w-full h-full" style="background-image: radial-gradient(circle at 1px 1px, rgba(0,0,0,0.15) 1px, transparent 0); background-size: 20px 20px;"></div>
          </div>

          <!-- Enhanced Chart -->
          <div class="w-full h-full p-6 relative">
            <!-- Y-axis labels with enhanced styling -->
            <div class="absolute left-2 top-8 bottom-16 flex flex-col justify-between text-xs font-medium text-slate-500">
              <div class="flex items-center gap-2">
                <span class="bg-white px-2 py-1 rounded shadow-sm">650</span>
                <div class="w-2 h-px bg-slate-300"></div>
              </div>
              <div class="flex items-center gap-2">
                <span class="bg-white px-2 py-1 rounded shadow-sm">600</span>
                <div class="w-2 h-px bg-slate-300"></div>
              </div>
              <div class="flex items-center gap-2">
                <span class="bg-white px-2 py-1 rounded shadow-sm">550</span>
                <div class="w-2 h-px bg-slate-300"></div>
              </div>
              <div class="flex items-center gap-2">
                <span class="bg-white px-2 py-1 rounded shadow-sm">500</span>
                <div class="w-2 h-px bg-slate-300"></div>
              </div>
              <div class="flex items-center gap-2">
                <span class="bg-white px-2 py-1 rounded shadow-sm">450</span>
                <div class="w-2 h-px bg-slate-300"></div>
              </div>
            </div>

            <!-- Chart area with enhanced grid -->
            <div class="ml-16 mr-6 h-full relative">
              <!-- Enhanced grid lines -->
              <div class="absolute inset-0 flex flex-col justify-between">
                <div class="border-t border-slate-200/80"></div>
                <div class="border-t border-slate-200/80"></div>
                <div class="border-t border-slate-200/80"></div>
                <div class="border-t border-slate-200/80"></div>
                <div class="border-t border-slate-200/80"></div>
              </div>

              <!-- Enhanced line chart with gradient -->
              <svg class="w-full h-full" viewBox="0 0 400 200">
                <!-- Gradient definition -->
                <defs>
                  <linearGradient id="chartGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                    <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.3" />
                    <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:0.05" />
                  </linearGradient>
                  <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
                    <feDropShadow dx="0" dy="2" stdDeviation="3" flood-color="#3b82f6" flood-opacity="0.3"/>
                  </filter>
                </defs>

                <!-- Area under the curve -->
                <polygon
                  fill="url(#chartGradient)"
                  points="20,200 20,120 70,100 120,140 170,110 220,90 270,80 320,90 370,80 370,200"
                />

                <!-- Main trend line -->
                <polyline
                  fill="none"
                  stroke="#3b82f6"
                  stroke-width="3"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  points="20,120 70,100 120,140 170,110 220,90 270,80 320,90 370,80"
                  filter="url(#shadow)"
                />

                <!-- Enhanced data points -->
                <circle cx="20" cy="120" r="5" fill="#ffffff" stroke="#3b82f6" stroke-width="3"/>
                <circle cx="70" cy="100" r="5" fill="#ffffff" stroke="#3b82f6" stroke-width="3"/>
                <circle cx="120" cy="140" r="5" fill="#ffffff" stroke="#3b82f6" stroke-width="3"/>
                <circle cx="170" cy="110" r="5" fill="#ffffff" stroke="#3b82f6" stroke-width="3"/>
                <circle cx="220" cy="90" r="5" fill="#ffffff" stroke="#3b82f6" stroke-width="3"/>
                <circle cx="270" cy="80" r="5" fill="#ffffff" stroke="#3b82f6" stroke-width="3"/>
                <circle cx="320" cy="90" r="5" fill="#ffffff" stroke="#3b82f6" stroke-width="3"/>
                <circle cx="370" cy="80" r="5" fill="#ffffff" stroke="#3b82f6" stroke-width="3"/>
              </svg>
            </div>

            <!-- Enhanced X-axis labels -->
            <div class="absolute bottom-2 left-16 right-6 flex justify-between text-xs font-medium text-slate-500">
              <span class="bg-white px-2 py-1 rounded shadow-sm">Aug 2024</span>
              <span class="bg-white px-2 py-1 rounded shadow-sm">Sep 2024</span>
              <span class="bg-white px-2 py-1 rounded shadow-sm">Oct 2024</span>
              <span class="bg-white px-2 py-1 rounded shadow-sm">Nov 2024</span>
              <span class="bg-white px-2 py-1 rounded shadow-sm">Dec 2024</span>
              <span class="bg-white px-2 py-1 rounded shadow-sm">Jan 2025</span>
              <span class="bg-white px-2 py-1 rounded shadow-sm">Feb 2025</span>
            </div>
          </div>

          <!-- Chart Insights -->
          <div class="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-lg border border-slate-200/60">
            <div class="flex items-center gap-2 text-sm">
              <div class="w-2 h-2 bg-green-500 rounded-full"></div>
              <span class="text-slate-600 font-medium">+12.5% Growth</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Column: Enhanced Top Performers Table -->
      <div class="xl:col-span-2 bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl shadow-slate-200/50 border border-slate-200/60 p-8">
        <!-- Table Header -->
        <div class="flex items-center justify-between mb-6">
          <div>
            <h2 class="text-2xl font-bold text-slate-900 mb-2">Top Performers</h2>
            <div class="flex flex-wrap gap-2 text-xs">
              <span class="px-3 py-1.5 bg-orange-50 border border-orange-200 rounded-full text-orange-700 font-medium">Period: Dec 2024 - Jun 2025</span>
              <span class="px-3 py-1.5 bg-indigo-50 border border-indigo-200 rounded-full text-indigo-700 font-medium">Sort By: Year</span>
              <span class="px-3 py-1.5 bg-pink-50 border border-pink-200 rounded-full text-pink-700 font-medium">Chart Type: Average PSF</span>
            </div>
          </div>
          <!-- Table Actions -->
          <div class="flex gap-2">
            <button class="p-2 text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-lg transition-all duration-200">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
              </svg>
            </button>
          </div>
        </div>

        <!-- Enhanced Table Header -->
        <div class="grid grid-cols-12 gap-4 mb-4 pb-3 border-b border-slate-200">
          <div class="col-span-2 text-sm font-bold text-slate-600 text-center">Rank</div>
          <div class="col-span-7 text-sm font-bold text-slate-600">All Towns (street name)</div>
          <div class="col-span-3 text-sm font-bold text-slate-600 text-right">All Types Average PSF</div>
        </div>

        <!-- Enhanced Table Content -->
        <div class="space-y-2">
          <!-- Row 1 - Winner -->
          <div class="grid grid-cols-12 gap-4 items-center py-4 px-4 bg-gradient-to-r from-yellow-50 to-amber-50 border border-yellow-200 rounded-xl hover:shadow-md transition-all duration-200 group">
            <div class="col-span-2 flex items-center justify-center">
              <div class="w-8 h-8 bg-gradient-to-r from-yellow-400 to-amber-500 rounded-full flex items-center justify-center text-white font-bold text-sm shadow-lg">
                1
              </div>
            </div>
            <div class="col-span-7">
              <div class="flex items-center gap-3">
                <div class="w-2 h-2 bg-yellow-500 rounded-full"></div>
                <span class="text-blue-600 hover:text-blue-800 cursor-pointer font-semibold group-hover:underline transition-all duration-200">Sengkang Ave 4</span>
              </div>
            </div>
            <div class="col-span-3 text-right">
              <span class="text-lg font-bold text-slate-900">56</span>
              <div class="text-xs text-green-600 font-medium">+2.1%</div>
            </div>
          </div>

          <!-- Row 2 -->
          <div class="grid grid-cols-12 gap-4 items-center py-3 px-4 hover:bg-slate-50 rounded-xl transition-all duration-200 group">
            <div class="col-span-2 flex items-center justify-center">
              <div class="w-7 h-7 bg-slate-200 rounded-full flex items-center justify-center text-slate-700 font-semibold text-sm">
                2
              </div>
            </div>
            <div class="col-span-7">
              <div class="flex items-center gap-3">
                <div class="w-2 h-2 bg-slate-400 rounded-full"></div>
                <span class="text-blue-600 hover:text-blue-800 cursor-pointer font-medium group-hover:underline transition-all duration-200">Sengkang Ave 2</span>
              </div>
            </div>
            <div class="col-span-3 text-right">
              <span class="text-base font-semibold text-slate-900">55</span>
              <div class="text-xs text-green-600 font-medium">+1.8%</div>
            </div>
          </div>

          <!-- Row 3 -->
          <div class="grid grid-cols-12 gap-4 items-center py-3 px-4 hover:bg-slate-50 rounded-xl transition-all duration-200 group">
            <div class="col-span-2 flex items-center justify-center">
              <div class="w-7 h-7 bg-amber-200 rounded-full flex items-center justify-center text-amber-800 font-semibold text-sm">
                3
              </div>
            </div>
            <div class="col-span-7">
              <div class="flex items-center gap-3">
                <div class="w-2 h-2 bg-amber-500 rounded-full"></div>
                <span class="text-blue-600 hover:text-blue-800 cursor-pointer font-medium group-hover:underline transition-all duration-200">Sengkang Ave 5</span>
              </div>
            </div>
            <div class="col-span-3 text-right">
              <span class="text-base font-semibold text-slate-900">53</span>
              <div class="text-xs text-green-600 font-medium">+1.2%</div>
            </div>
          </div>

          <!-- Rows 4-10 -->
          <div class="grid grid-cols-12 gap-4 items-center py-3 px-4 hover:bg-slate-50 rounded-xl transition-all duration-200 group">
            <div class="col-span-2 flex items-center justify-center">
              <span class="text-sm font-semibold text-slate-600">4</span>
            </div>
            <div class="col-span-7">
              <span class="text-blue-600 hover:text-blue-800 cursor-pointer font-medium group-hover:underline transition-all duration-200">Sengkang Ave 9</span>
            </div>
            <div class="col-span-3 text-right">
              <span class="text-base font-semibold text-slate-900">53</span>
            </div>
          </div>

          <div class="grid grid-cols-12 gap-4 items-center py-3 px-4 hover:bg-slate-50 rounded-xl transition-all duration-200 group">
            <div class="col-span-2 flex items-center justify-center">
              <span class="text-sm font-semibold text-slate-600">5</span>
            </div>
            <div class="col-span-7">
              <span class="text-blue-600 hover:text-blue-800 cursor-pointer font-medium group-hover:underline transition-all duration-200">Sengkang Ave 3</span>
            </div>
            <div class="col-span-3 text-right">
              <span class="text-base font-semibold text-slate-900">49</span>
            </div>
          </div>

          <div class="grid grid-cols-12 gap-4 items-center py-3 px-4 hover:bg-slate-50 rounded-xl transition-all duration-200 group">
            <div class="col-span-2 flex items-center justify-center">
              <span class="text-sm font-semibold text-slate-600">6</span>
            </div>
            <div class="col-span-7">
              <span class="text-blue-600 hover:text-blue-800 cursor-pointer font-medium group-hover:underline transition-all duration-200">Sengkang Ave 10</span>
            </div>
            <div class="col-span-3 text-right">
              <span class="text-base font-semibold text-slate-900">46</span>
            </div>
          </div>

          <div class="grid grid-cols-12 gap-4 items-center py-3 px-4 hover:bg-slate-50 rounded-xl transition-all duration-200 group">
            <div class="col-span-2 flex items-center justify-center">
              <span class="text-sm font-semibold text-slate-600">7</span>
            </div>
            <div class="col-span-7">
              <span class="text-blue-600 hover:text-blue-800 cursor-pointer font-medium group-hover:underline transition-all duration-200">Sengkang Ave 3</span>
            </div>
            <div class="col-span-3 text-right">
              <span class="text-base font-semibold text-slate-900">46</span>
            </div>
          </div>

          <div class="grid grid-cols-12 gap-4 items-center py-3 px-4 hover:bg-slate-50 rounded-xl transition-all duration-200 group">
            <div class="col-span-2 flex items-center justify-center">
              <span class="text-sm font-semibold text-slate-600">8</span>
            </div>
            <div class="col-span-7">
              <span class="text-blue-600 hover:text-blue-800 cursor-pointer font-medium group-hover:underline transition-all duration-200">Sengkang Ave 5</span>
            </div>
            <div class="col-span-3 text-right">
              <span class="text-base font-semibold text-slate-900">44</span>
            </div>
          </div>

          <div class="grid grid-cols-12 gap-4 items-center py-3 px-4 hover:bg-slate-50 rounded-xl transition-all duration-200 group">
            <div class="col-span-2 flex items-center justify-center">
              <span class="text-sm font-semibold text-slate-600">9</span>
            </div>
            <div class="col-span-7">
              <span class="text-blue-600 hover:text-blue-800 cursor-pointer font-medium group-hover:underline transition-all duration-200">Sengkang Ave 5</span>
            </div>
            <div class="col-span-3 text-right">
              <span class="text-base font-semibold text-slate-900">43</span>
            </div>
          </div>

          <div class="grid grid-cols-12 gap-4 items-center py-3 px-4 hover:bg-slate-50 rounded-xl transition-all duration-200 group">
            <div class="col-span-2 flex items-center justify-center">
              <span class="text-sm font-semibold text-slate-600">10</span>
            </div>
            <div class="col-span-7">
              <span class="text-blue-600 hover:text-blue-800 cursor-pointer font-medium group-hover:underline transition-all duration-200">Sengkang Ave 3</span>
            </div>
            <div class="col-span-3 text-right">
              <span class="text-base font-semibold text-slate-900">43</span>
            </div>
          </div>
        </div>

        <!-- Enhanced Pagination -->
        <div class="flex items-center justify-between mt-8 pt-6 border-t border-slate-200">
          <div class="text-sm text-slate-600">
            Showing <span class="font-semibold text-slate-900">1-10</span> of <span class="font-semibold text-slate-900">247</span> results
          </div>

          <nav class="flex items-center gap-2">
            <!-- Previous button -->
            <button class="flex items-center gap-2 px-4 py-2 text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-lg transition-all duration-200 disabled:opacity-50" disabled>
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
              </svg>
              <span class="hidden sm:inline">Previous</span>
            </button>

            <!-- Page numbers -->
            <div class="flex items-center gap-1">
              <button class="w-10 h-10 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg text-sm font-semibold shadow-lg shadow-blue-600/25 transform hover:scale-105 transition-all duration-200">1</button>
              <button class="w-10 h-10 text-slate-700 hover:bg-slate-100 hover:text-slate-900 rounded-lg text-sm font-medium transition-all duration-200 transform hover:scale-105">2</button>
              <button class="w-10 h-10 text-slate-700 hover:bg-slate-100 hover:text-slate-900 rounded-lg text-sm font-medium transition-all duration-200 transform hover:scale-105">3</button>
              <span class="px-2 text-slate-400">...</span>
              <button class="w-10 h-10 text-slate-700 hover:bg-slate-100 hover:text-slate-900 rounded-lg text-sm font-medium transition-all duration-200 transform hover:scale-105">25</button>
            </div>

            <!-- Next button -->
            <button class="flex items-center gap-2 px-4 py-2 text-slate-700 hover:text-slate-900 hover:bg-slate-100 rounded-lg transition-all duration-200">
              <span class="hidden sm:inline">Next</span>
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M8.59 16.59L10 18l6-6-6-6-1.41 1.41L13.17 12z"/>
              </svg>
            </button>
          </nav>
        </div>
      </div>
    </div>

    <!-- Additional Analytics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
      <!-- Quick Stats Card 1 -->
      <div class="bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg shadow-slate-200/50 border border-slate-200/60 p-6">
        <div class="flex items-center justify-between mb-4">
          <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M3.5 18.49l6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L3.5 15.9z"/>
            </svg>
          </div>
          <div class="text-right">
            <div class="text-2xl font-bold text-slate-900">$542K</div>
            <div class="text-sm text-green-600 font-medium">+8.2%</div>
          </div>
        </div>
        <div class="text-slate-600 font-medium">Average Price</div>
        <div class="text-xs text-slate-500 mt-1">Across all property types</div>
      </div>

      <!-- Quick Stats Card 2 -->
      <div class="bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg shadow-slate-200/50 border border-slate-200/60 p-6">
        <div class="flex items-center justify-between mb-4">
          <div class="w-12 h-12 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
          </div>
          <div class="text-right">
            <div class="text-2xl font-bold text-slate-900">1,247</div>
            <div class="text-sm text-blue-600 font-medium">+12.5%</div>
          </div>
        </div>
        <div class="text-slate-600 font-medium">Total Transactions</div>
        <div class="text-xs text-slate-500 mt-1">This quarter</div>
      </div>

      <!-- Quick Stats Card 3 -->
      <div class="bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg shadow-slate-200/50 border border-slate-200/60 p-6">
        <div class="flex items-center justify-between mb-4">
          <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
            </svg>
          </div>
          <div class="text-right">
            <div class="text-2xl font-bold text-slate-900">23</div>
            <div class="text-sm text-orange-600 font-medium">Active</div>
          </div>
        </div>
        <div class="text-slate-600 font-medium">Hot Locations</div>
        <div class="text-xs text-slate-500 mt-1">High demand areas</div>
      </div>
    </div>
  </div>
</div>