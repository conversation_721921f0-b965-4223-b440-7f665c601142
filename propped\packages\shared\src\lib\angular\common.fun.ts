import { randomItem } from '../common/fun';
import { MatColors } from '../common/material';

// biome-ignore lint/complexity/noStaticOnlyClass: <explanation>
export class CommonFunctions {
  static colors: string[];
  public static getRandomColor = (shade = 500): [string, string] => {
    if (!this.colors) {
      const all = MatColors.getAll();
      const exclude = ['black', 'white'];
      this.colors = Object.keys(all).filter((v) => !exclude.includes(v));
    }
    return MatColors.getAll()[randomItem(this.colors)][shade];
  };
}
