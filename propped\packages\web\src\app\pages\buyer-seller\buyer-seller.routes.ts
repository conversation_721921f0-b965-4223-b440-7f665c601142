import type { Route } from '@angular/router';

export const routes: Route[] = [
  {
    path: '',
    loadComponent: () => import('./buyer-seller.component').then((m) => m.BuyerSellerComponent),
  },
  {
    path: 'step1',
    loadComponent: () =>
      import('./property-address/property-address.component').then(
        (m) => m.PropertyAddressComponent,
      ),
  },
  {
    path: 'step2',
    loadComponent: () =>
      import('./property-photos/property-photos.component').then((m) => m.PropertyPhotosComponent),
  },
  {
    path: 'step3',
    loadComponent: () =>
      import('./property-details/property-details.component').then(
        (m) => m.PropertyDetailsComponent,
      ),
  },
  {
    path: 'step4',
    loadComponent: () =>
      import('./selling-price/selling-price.component').then((m) => m.SellingPriceComponent),
  },
  {
    path: 'step5',
    loadComponent: () =>
      import('./sale-conditions/sale-conditions.component').then((m) => m.SaleConditionsComponent),
  },
  {
    path: 'preview',
    loadComponent: () =>
      import('./preview-listing/preview-listing.component').then((m) => m.PreviewListingComponent),
  },
  {
    path: 'property-map',
    loadComponent: () =>
      import('../property-map/property-map.component').then((m) => m.PropertyMapComponent),
  },
  {
    path: 'offer-price-estimator',
    loadComponent: () =>
      import('./offer-price-estimator/offer-price-estimator.component').then(
        (m) => m.OfferPriceEstimatorComponent,
      ),
  },
  {
    path: 'seller-preview',
    loadComponent: () =>
      import('./seller-preview/seller-preview.component').then((m) => m.SellerPreviewComponent),
  },
  {
    path: 'make-offer-buyer',
    loadComponent: () =>
      import('./make-offer-buyer/make-offer-buyer.component').then(
        (m) => m.MakeOfferBuyerComponent,
      ),
  },
  {
    path: 'sales-listing',
    loadComponent: () =>
     import('./buyer-sales-listing/buyer-sales-listing.component').then(
      (m) => m.BuyerSalesListingComponent
    ),
  },
  {
    path: 'my-sales-listing',
    loadComponent: () =>
     import('./seller-sales-listing/my-sales-listing.component').then(
      (m) => m.MySalesListingComponent
    ),
  }

];
