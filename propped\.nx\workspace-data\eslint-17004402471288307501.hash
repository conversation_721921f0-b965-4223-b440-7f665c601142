{"17008463350112551562": {}, "6444247928772239518": {"packages/api": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/api"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "2572603065811561279": {"packages/shared": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/shared"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "4332257591430414687": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "5773173857821286202": {}, "2755683675738496296": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "2655835136373438864": {}, "17366609181925345187": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "5324065037646741772": {}, "5498213217945831195": {"packages/api": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/api"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "854960638363083298": {}, "16158546096388685383": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "5773000202703881929": {}, "3308959374626987343": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "10583980768562959259": {}, "753965083235902776": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "9073650822032317725": {}, "2978193665196714276": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "8738463287267225173": {}, "11917321632848416601": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "12024817542675237260": {}, "1249676841367159873": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "7480730617086416216": {}, "14745847963054789813": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "700565143110731636": {}, "2944097395463558113": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "11309401390628285305": {}, "1090088275472792820": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "1396319756465653862": {}, "2820586052257127572": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "5521145985296083794": {}, "10473558545627135585": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "350124349183596175": {}, "8115432161194438234": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "2772918003789964502": {}, "5104628437088734608": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "6752214891824441748": {}, "1915590369186937944": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "5443395203152510052": {}, "3055436286847990362": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "9169240483134633206": {}, "5457048453198737849": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "14899721966561197875": {}, "11331615261904903528": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "3615877529457459831": {}, "13809088676720719689": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "15493360725051758819": {}, "1408927120199110251": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "3859179830099221892": {}, "11195664332722428267": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "15223212758348921728": {}, "15863336276702172729": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "15673011214000896981": {}, "16023749941318307772": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "18325656779718496567": {}, "14651239284625183660": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "10363013180725952080": {}, "8507776217553041259": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "9635000964924948100": {}, "12216428047145035395": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "5957305881784712473": {}, "5452622676730735493": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "3572229169229914340": {}, "18116811251835571808": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "13972481769412661695": {}, "371278408213333388": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "1076467781553298217": {}, "15827825716536076973": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "4814103816575441546": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "3742892845269256458": {}, "5063068744339655017": {}, "4115142733992455418": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "9587222685424254684": {}, "16597821419611944295": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "12942992011935982987": {}, "8734882071960149242": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "8277396035272799842": {}, "10688808222356752320": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "17934072301726938336": {}, "12851155079476489410": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "3227882703138428241": {}, "5825665258134339558": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "7761134731925816657": {}, "8903274169005459003": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "6482243234690733041": {}, "18242963683693436033": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "10350103117763706869": {}, "4624969573389516513": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "2708339592980285816": {}, "3325464736372922062": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "12181705097919194468": {}, "17393202761605264605": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "18214461449404528299": {}, "1237026874503639555": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "16848581359791724235": {}, "827873637850563290": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "17245682387438875258": {}, "15498723079540172662": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "9992834409380334862": {}, "5927953320284006226": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "12212218335349993678": {}, "7458308731998890263": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "10521139818708464267": {}, "16251039546031990661": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "17068235011361398756": {}, "7422931354026550436": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "6478154689585616328": {}, "5865419499945882593": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "5125921001962563245": {}, "8525332003179120488": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "5429417478205634900": {}, "1136446237050661316": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "1930207921023290210": {}, "13800078181935563249": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "14471807048292059363": {}, "4645532150784708923": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "478849113839283644": {}, "5735557428649992159": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "16926809484838079587": {}, "13897554275843879225": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "14596204196715296174": {}, "13465943277064464855": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "16817375993977821099": {}, "8525376325905039233": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "1285208521379439052": {}, "2321891587529315389": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "14485123005724369891": {}, "4770992891746205671": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "17664839843243342648": {}, "1749337256299576158": {"packages/api": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/api"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "1486107148611520827": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "8157356276882694583": {}, "7919469665399095979": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "9872953734295192382": {}, "9795856168474901023": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "8294763291560636812": {}, "7277661132009227636": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "6030476007198068611": {}, "15460318386534831385": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "9193559967458960801": {}, "1985311175356987376": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "3104202598702152195": {}, "13267730183086006249": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "14310287568098469350": {}, "12674753322650515250": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "6429773627856348911": {}, "13181743941408552717": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "8372185980926878354": {}, "17479715694177239875": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "4770932430700979979": {}, "7546345455377221451": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "12381079027825047549": {}, "12372202807795302753": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "6039548437224676738": {}, "16135240357371057499": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "7724498248082112465": {}, "147162857915873139": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "16471990339771694097": {}, "1963427834022178522": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "2035395406756726507": {}, "1257629273971700940": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "5759861012145126058": {}, "6843657541497017490": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "4089331161900963638": {}, "14875335452648303344": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "495862140441861198": {}, "15366772742335020450": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "7497488715717594861": {}, "10848467380604158797": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "4086005263316651818": {}, "17980073642582332443": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "7269520734027956622": {}, "2145270457648693551": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "11182678204868841179": {}, "13761873323669252459": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "12561430091524933530": {}, "4749194867531791926": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "17251633522055926944": {}, "14564914959888045330": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "16737734194478661134": {}, "10991817108110332656": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "15724446121766270169": {}, "12403349689210169549": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "9040952209216447885": {}, "15548043511582543788": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "931674939341202128": {}, "10257308308285489359": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "14637982075130139523": {}, "3558923027186310333": {"packages/web": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "packages/web"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "pnpm exec eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}}