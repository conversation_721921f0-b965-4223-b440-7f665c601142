import { FocusMonitor } from '@angular/cdk/a11y';
import type { BooleanInput } from '@angular/cdk/coercion';
import { coerceBooleanProperty } from '@angular/cdk/coercion';
import type { OnDestroy } from '@angular/core';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  Input,
  ViewEncapsulation,
  inject,
} from '@angular/core';
import type { ControlValueAccessor } from '@angular/forms';
import { FormsModule, NgControl, ReactiveFormsModule } from '@angular/forms';
import type { MatFormField } from '@angular/material/form-field';
import { MAT_FORM_FIELD, MatFormFieldControl } from '@angular/material/form-field';
import { format } from 'date-fns';
import { Subject } from 'rxjs';
import { isNil } from '../../../common/fun';
import { SilverTimePickerModalService } from './time-picker.service';

@Component({
  selector: 'silver-time-picker',
  imports: [FormsModule, ReactiveFormsModule],
  providers: [{ provide: MatFormFieldControl, useExisting: SilverTimePickerComponent }],
  template: `
    <div
      [attr.aria-labelledby]="_formField?.getLabelId?.()"
      (focusin)="onFocusIn($event)"
      (focusout)="onFocusOut($event)"
      cdk-overlay-origin
    >
      <div>
        @if (!empty) {
          <span class="truncate" style="color:var(--mdc-filled-text-field-input-text-color)">{{
            getToShow()
          }}</span>
        }
      </div>
    </div>
  `,
  host: { '[id]': 'id' },
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SilverTimePickerComponent
  implements ControlValueAccessor, MatFormFieldControl<Date>, OnDestroy
{
  static nextId = 0;
  readonly #silverTimePickerModalService = inject(SilverTimePickerModalService);
  readonly #focusMonitor = inject(FocusMonitor);
  readonly #elementRef = inject(ElementRef);
  readonly ngControl = inject(NgControl, { self: true, optional: true });
  readonly #cdRef = inject(ChangeDetectorRef);
  readonly _formField: MatFormField | null = inject(MAT_FORM_FIELD, { optional: true });
  public stateChanges = new Subject<void>();
  public focused = false;
  public touched = false;
  public controlType = 'silver-time-picker';
  public id = `silver-time-picker-${SilverTimePickerComponent.nextId++}`;

  onChange = (_: any) => {
    //
  };
  onTouched = () => {
    //
  };

  _value: Date | null = null;

  get empty() {
    return isNil(this._value);
  }

  get shouldLabelFloat() {
    return this.focused || !this.empty;
  }

  // eslint-disable-next-line @angular-eslint/no-input-rename
  @Input('aria-describedby') userAriaDescribedBy!: string;

  @Input()
  get placeholder(): string {
    return this._placeholder;
  }
  set placeholder(value: string) {
    this._placeholder = value;
    this.stateChanges.next();
  }

  private _placeholder!: string;

  @Input()
  get required(): boolean {
    return this._required;
  }
  set required(value: BooleanInput) {
    this._required = coerceBooleanProperty(value);
    this.stateChanges.next();
  }
  private _required = false;

  @Input()
  get disabled(): boolean {
    return this._disabled;
  }
  set disabled(value: BooleanInput) {
    this._disabled = coerceBooleanProperty(value);
    this.stateChanges.next();
  }

  private _disabled = false;

  @Input()
  get value(): Date | null {
    const val = this._value;
    return isNil(val) ? null : val;
  }

  set value(val: Date | null) {
    this._value = isNil(val) ? null : val;
  }

  get errorState(): boolean {
    return this.touched;
  }

  constructor() {
    if (this.ngControl != null) {
      this.ngControl.valueAccessor = this;
    }
  }

  focus() {
    this.focused = true;
    this.stateChanges.next();
  }

  getToShow() {
    return this._value ? format(this._value, 'dd-MMM-yy hh:mm aa') : '';
  }

  focusOut() {
    this.focused = false;
    this.stateChanges.next();
  }

  onFocusIn(_event: FocusEvent) {
    if (!this.focused) {
      this.focused = true;
      this.stateChanges.next();
    }
  }

  onFocusOut(event: FocusEvent) {
    if (!this.#elementRef.nativeElement.contains(event.relatedTarget as Element)) {
      this.touched = true;
      this.focused = false;
      this.onTouched();
      this.stateChanges.next();
    }
  }

  setDescribedByIds(_ids: string[]) {
    //
  }

  async open() {
    const opt = { heading: 'Choose Date Time', value: this._value };
    const result = await this.#silverTimePickerModalService.open(opt);
    if (result?.action) {
      this._value = result.value;
      this.onChange(this.value);
      this.stateChanges.next();
    }
    if (this.empty) this.focusOut();
    this.#cdRef.detectChanges();
  }

  onContainerClick() {
    this.focus();
    this.open();
  }

  writeValue(input: Date | null): void {
    this.value = input;
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  ngOnDestroy() {
    this.stateChanges.complete();
    this.#focusMonitor.stopMonitoring(this.#elementRef);
  }
}
