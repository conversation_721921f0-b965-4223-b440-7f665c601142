import type { Context } from 'hono';
import { getOrInsertToMap } from '../common/fun';

export type GuardHandler = (context: APIContext) => any;
export type AnyMethod = any;

export const Guards = new Map<AnyMethod, Set<GuardHandler>>();

export const pushToGuards = (target: AnyMethod, guard: GuardHandler) => {
  const guards = getOrInsertToMap(Guards, target, new Set<GuardHandler>());
  guards.add(guard);
};

export const UseGuard = (guard: GuardHandler) => (target: any) => {
  pushToGuards(target, guard);
  return target;
};

export const MetaDataStore = new Map<AnyMethod, Map<any, any>>();

export const setMetaData = (target: AnyMethod, key: any, value: any) => {
  const store = getOrInsertToMap(MetaDataStore, target, new Map<any, any>());
  store.set(key, value);
};

export const getMetaData = (target: AnyMethod, key: any) => {
  const store = getOrInsertToMap(MetaDataStore, target, new Map<any, any>());
  return store.get(key);
};

export const SetMetaData = (key: any, value: any) => {
  return (target: any) => {
    setMetaData(target, key, value);
    return target;
  };
};

export const resolveMeta = (a: any[]) => {
  return (key: any) => {
    for (const target of a) {
      if (MetaDataStore.get(target)?.has(key)) return getMetaData(target, key);
    }
  };
};

export const applyDecorators = (fns: AnyMethod[]) => (target: AnyMethod) => {
  for (const fn of fns) fn(target);
};

export interface APIContext {
  c: Context;
  target: AnyMethod;
  handler: AnyMethod;
  getMeta: (key: any) => any;
}
