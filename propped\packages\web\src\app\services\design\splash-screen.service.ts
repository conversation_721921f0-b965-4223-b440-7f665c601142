import type { AnimationPlayer } from '@angular/animations';
import { AnimationBuilder, animate, style } from '@angular/animations';
import { DOCUMENT } from '@angular/common';
import { Injectable, inject } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { wait } from '@lib/common/fun';
import { EnsureSingleInstance } from '@lib/common/test-root-service';
import { delay, filter, of, take, tap } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class SplashScreenService {
  readonly #animationBuilder = inject(AnimationBuilder);
  readonly #document = inject(DOCUMENT);
  readonly #router = inject(Router);
  private splashScreenEl!: HTMLElement | null;
  public player!: AnimationPlayer;

  constructor() {
    EnsureSingleInstance(this);
  }

  public init() {
    this.splashScreenEl = this.#document.body.querySelector<HTMLElement>('#nk');
    if (this.splashScreenEl) {
      return this.#router.events.pipe(
        filter((event) => event instanceof NavigationEnd),
        take(1),
        delay(40),
        tap(() => this.hide()),
      );
    }
    return of(null);
  }

  public async show() {
    this.player = this.#animationBuilder
      .build([
        style({ opacity: '0', zIndex: '99999' }),
        animate('400ms ease', style({ opacity: '1' })),
      ])
      .create(this.splashScreenEl);
    await wait();
    this.player.play();
  }

  public async hide() {
    this.player = this.#animationBuilder
      .build([
        style({ opacity: '1' }),
        animate('400ms ease', style({ opacity: '0', zIndex: '-10' })),
      ])
      .create(this.splashScreenEl);
    await wait();
    this.player.play();
    // this.document.body.querySelector('app-root').removeAttribute('hidden');
  }
}
