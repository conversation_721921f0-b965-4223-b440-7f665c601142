import { Component, inject } from '@angular/core';
import { MatIcon } from '@angular/material/icon';
import { SideNavService } from '../../../services/design/sidenav.service';

@Component({
  selector: 'app-widgets',
  imports: [MatIcon],
  templateUrl: './widgets.component.html',
})
export class WidgetsComponent {
  readonly #sideNavService = inject(SideNavService);

  public closeWidgetsPanel() {
    this.#sideNavService.widgetPanel.next({ open: false });
  }
}
