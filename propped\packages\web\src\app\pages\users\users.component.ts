import { Component, type OnD<PERSON>roy, type OnInit, inject, viewChild } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { apiRP<PERSON>, injectController } from '@api/rpc';
import { ActionModalService } from '@lib/angular/action-modal.service';
import { AlertModalService } from '@lib/angular/alert-modal.service';
import { switchMapPromise } from '@lib/angular/controller.hook';
import { ProfileFormModalService } from '@lib/angular/dynamic-forms/profile-form-modal.service';
import {
  type SilverField,
  SilverFieldTypes,
} from '@lib/angular/dynamic-forms/silver-field.component';
import { FileUploaderService, type URLResolver } from '@lib/angular/file-uploader.service';
import { SilverSearchBarComponent } from '@lib/angular/search-bar.component';
import { SnackBarService } from '@lib/angular/snack-bar.service';
import { ROLES, ROLES_BY_ID } from '@lib/common/const/roles';
import { type JData, type JDataConfig, type JDataFilterGroup } from '@lib/common/j-data.interface';
import { getFileMeta } from '@lib/web/file.fun';
import { Subject, type Subscription, merge, startWith } from 'rxjs';
import { AppCommonModule } from '../../common/common.module';
import { AuthService } from '../../services/auth.service';

@Component({
  imports: [SilverSearchBarComponent, MatIconModule, MatPaginatorModule, AppCommonModule],
  selector: 'app-users',
  templateUrl: './users.component.html',
})
export class UsersComponent implements OnInit, OnDestroy {
  readonly paginator = viewChild.required(MatPaginator);

  readonly #actionModalService = inject(ActionModalService);
  readonly #alertModalService = inject(AlertModalService);
  readonly #authService = inject(AuthService);
  readonly #fileUploaderService = inject(FileUploaderService);
  readonly #profileFormModalService = inject(ProfileFormModalService);
  readonly #snackBarService = inject(SnackBarService);

  readonly #authController = injectController(apiRPC.AuthController);

  private jData: JData = {
    filter: { condition: 'AND', rules: [], valid: true },
    config: { sort: 'id', order: 'ASC', page: 1, limit: 50 },
  };
  public users: any;
  public meta: any = { total: 0 };
  public filterData = { search: '' };
  public updateForm: SilverField[];
  private refresh$ = new Subject<boolean>();
  public loginUser = this.#authService.getLoginUserSync();
  readonly #subscriptions: Record<string, Subscription> = {};

  ngOnInit(): void {
    this.getAllUsers();
  }

  rolesNamesById(ids: number[]): string[] {
    return ids.map((id) => ROLES_BY_ID[id]);
  }

  public getAllUsers() {
    this.#subscriptions.getAllUsers = merge(
      this.paginator().page,
      this.paginator().pageSizeOptions,
      this.refresh$,
    )
      .pipe(
        startWith({}),
        switchMapPromise((signal) => {
          this.jData.config = this.getConfigs();
          this.jData.filter.rules = this.getFilter();
          return this.#authController.with({ signal }).getAllUsers({ jData: this.jData });
        }),
      )
      .subscribe((data: any) => {
        this.users = data.data;
        this.meta = data.meta;
      });
  }

  openUserForm() {
    const form: SilverField[] = [
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Name',
        placeholder: 'Enter Name',
        type: 'text',
        key: 'name',
        value: '',
        valid: { required: true },
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Email',
        placeholder: 'Enter email',
        type: 'text',
        key: 'email',
        value: '',
        valid: { required: true, email: true },
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Username',
        placeholder: 'Enter username',
        type: 'text',
        key: 'username',
        value: '',
        valid: { required: true, username: true },
      },
      {
        controlType: SilverFieldTypes.SELECT,
        label: 'Roles',
        key: 'roleId',
        value: [],
        valid: { required: true },
        multiple: true,
        options: Object.values(ROLES).map((v) => [v.id, v.name]),
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Password',
        placeholder: 'Enter password',
        type: 'password',
        key: 'password',
        value: '',
        cssClass: 'mb-3',
        valid: { password: true },
      },
    ];
    const result = this.#profileFormModalService.openForm({
      heading: 'Add',
      autoImageField: 'name',
      form,
      closeOnSubmit: false,
      imgOptions: { resize: [144, 144], exportTo: 'image/png', forceSize: false },
    });
    result.onSubmit$.subscribe({
      next: async (data) => {
        const done = await this.createUser(data);
        if (done) {
          this.meta = { total: 0 };
          this.refresh$.next(true);
          result.close();
        }
      },
    });
  }

  public async createUser(param: { value: any; image: File }) {
    const data = param.value;
    data.meta = {};
    try {
      const v: any = await this.#authController.createUserByAdmin(data);
      const userData = v.data.user;
      const filesToUpload: URLResolver[] = [];
      if (param.image) filesToUpload.push(() => this.getThumbUrl(userData.id, param.image));
      if (filesToUpload.length > 0) await this.#fileUploaderService.uploadFiles(filesToUpload);
      this.#snackBarService.success('User Created');
      return true;
    } catch (error) {
      this.#snackBarService.error(error.error.message);
      return false;
    }
  }

  public async userAction(user) {
    const result = await this.#actionModalService.open({
      actions: {
        update: 'Edit',
        remove: { name: 'Delete', cssClass: 'text-error' },
      },
    });
    if (!result?.action) return;
    if (result.key === 'update') this.updateUser(user);
    else if (result.key === 'remove') this.deleteUser(user);
  }

  public searchResult(searchValue: string) {
    this.filterData.search = searchValue;
    this.refresh$.next(true);
  }

  public updateUser(user) {
    const form: SilverField[] = [
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Name',
        placeholder: 'Enter Name',
        type: 'text',
        key: 'name',
        value: user.name,
        valid: { required: true },
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Email',
        placeholder: 'Enter email',
        type: 'text',
        key: 'email',
        value: user.email,
        valid: { required: true, email: true },
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Username',
        placeholder: 'Enter username',
        type: 'text',
        key: 'username',
        value: user.username,
        valid: { required: true, username: true },
      },
      {
        controlType: SilverFieldTypes.SELECT,
        label: 'Roles',
        key: 'roleId',
        value: user.roles,
        valid: { required: true },
        multiple: true,
        options: Object.values(ROLES).map((v) => [v.id, v.name]),
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Password',
        placeholder: 'Enter password',
        type: 'password',
        key: 'password',
        value: '',
        cssClass: 'mb-3',
        valid: { password: true },
      },
    ];
    const result = this.#profileFormModalService.openForm({
      heading: 'Update',
      autoImageField: 'name',
      form,
      imageUrl: user.pic,
      closeOnSubmit: false,
      imgOptions: { resize: [144, 144], exportTo: 'image/png', forceSize: false },
    });
    result.onSubmit$.subscribe({
      next: async (data) => {
        const done = await this.updateProfile(user.id, data);
        if (done) {
          this.meta = { total: 0 };
          this.refresh$.next(true);
          result.close();
        }
      },
    });
  }

  public async updateProfile(userId: number, data: { value: any; image: File }) {
    try {
      await this.#authController.updateUser({ id: userId }, data.value);
      const filesToUpload: URLResolver[] = [];
      if (data.image) filesToUpload.push(() => this.getThumbUrl(userId, data.image));
      if (filesToUpload.length > 0) await this.#fileUploaderService.uploadFiles(filesToUpload);
      this.#snackBarService.success('Profile Updated');
      return true;
    } catch (error) {
      this.#snackBarService.error(error.error.message);
      return false;
    }
  }

  public async deleteUser(user: any) {
    const isAllow = await this.#alertModalService.deleteConfirm(
      `Are you sure you want to delete User **${user.name}**?`,
    );
    if (!isAllow) return;
    try {
      await this.#authController.remove({ id: user.id });
      this.#snackBarService.success('User deleted');
      this.refresh$.next(true);
    } catch (error) {
      this.#snackBarService.error(error.error.message);
    }
  }

  public getConfigs(): JDataConfig {
    const { pageIndex, pageSize } = this.paginator();
    return { page: pageIndex + 1, limit: pageSize || 50, sort: 'name', order: 'ASC' };
  }

  public getFilter() {
    const filters = [];
    if (this.filterData.search) {
      const abc: JDataFilterGroup = { condition: 'OR', rules: [], valid: true };
      const { search } = this.filterData;
      abc.rules.push({ field: 'name', operator: 'like', value: search || '', entity: undefined });
      abc.rules.push({
        field: 'username',
        operator: 'like',
        value: search || '',
        entity: undefined,
      });
      filters.push(abc);
    }
    return filters;
  }

  private async getThumbUrl(id: number, file: File) {
    const result = await this.#authController.setThumbnail({ id }, { file: getFileMeta(file) });
    return [{ url: result.data.preUrl }, file] as [any, File];
  }

  public ngOnDestroy(): void {
    for (const sub of Object.values(this.#subscriptions)) sub.unsubscribe();
    (this as any).#subscriptions = undefined;
  }
}
