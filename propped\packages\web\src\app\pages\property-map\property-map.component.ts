import { CommonModule } from '@angular/common';
import {
  type AfterViewChecked,
  type AfterViewInit,
  Component,
  type ElementRef,
  inject,
  type OnDestroy,
  type OnInit,
  ViewChild,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { Router, RouterModule } from '@angular/router';
import { apiRPC, injectController } from '@api/rpc';
import { SnackBarService } from '@lib/angular/snack-bar.service';
import { Chart, registerables } from 'chart.js';
import { type LayerSpecification, LngLatBounds, type Map as mapBoxMap } from 'mapbox-gl';
import {
  GeoJSONSourceComponent,
  LayerComponent,
  MapComponent,
  MarkerComponent,
  PopupComponent,
} from 'ngx-mapbox-gl';

Chart.register(...registerables);

@Component({
  selector: 'app-property-map',
  templateUrl: './property-map.component.html',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatIconModule,
    RouterModule,
    GeoJSONSourceComponent,
    LayerComponent,
    MapComponent,
    PopupComponent,
    MarkerComponent,
  ],
})
export class PropertyMapComponent implements OnInit, AfterViewInit, OnDestroy, AfterViewChecked {
  @ViewChild('mapContainer', { static: true }) mapContainer!: ElementRef;
  readonly #hdbController = injectController(apiRPC.HDBController);
  readonly #schoolController = injectController(apiRPC.SchoolController);
  readonly #metroController = injectController(apiRPC.MetroController);
  readonly #hospitalController = injectController(apiRPC.HospitalController);
  readonly #hawkerController = injectController(apiRPC.HawkerController);
  readonly #busStopsController = injectController(apiRPC.BusStopsController);
  // Map properties
  readonly #snackBarService = inject(SnackBarService);
  googleMap: google.maps.Map | null = null;
  propertyMarker: google.maps.Marker | null = null;
  lng = 103.8198;
  lat = 1.3521;
  isMapLoading = true;
  router = inject(Router);
  propertyData = this.router.getCurrentNavigation()?.extras.state?.data;
  public townColors: { [key: string]: string } = {};
  private townColorPalette = [
    '#4285F4', // Google Blue
    '#EA4335', // Google Red
    '#FBBC05', // Google Yellow
    '#34A853', // Google Green
    '#8E24AA', // Purple
    '#16A085', // Turquoise
    '#F39C12', // Orange
    '#D35400', // Pumpkin
    '#C0392B', // Pomegranate
    '#2980B9', // Belize Hole
    '#27AE60', // Nephritis
    '#F1C40F', // Sunflower
    '#E74C3C', // Alizarin
    '#9B59B6', // Amethyst
    '#FB6D9D', // Pink
    '#95A5A6', // Concrete
    '#1ABC9C', // Aqua
    '#2ECC71', // Emerald
    '#3498DB', // Peter River
    '#9B27B0', // Deep Purple
    '#FF5722', // Deep Orange
    '#607D8B', // Blue Grey
    '#3F51B5', // Indigo
    '#795548', // Brown
  ];
  hdbSourceData = {
    type: 'FeatureCollection',
    features: [],
  };
  propertyAddress = 'Block 220C, Compassvale Bow, Singapore';
  map?: mapBoxMap;
  style = 'mapbox://styles/mapbox/streets-v12';
  zoom = 12;
  public selectedRadius = 1; // Default radius of 1km
  public showAmenitiesPopup = false;
  public amenitiesPopUpContent: any = null;
  public amenitiesFeature: GeoJSON.Feature<GeoJSON.Point> | null = null;
  public showPropertyPopup = false;
  public nearbyOptions = {
    schools: false,
    hawker: false,
    busStops: false,
    mrt: false,
    salesListings: false,
    hdbData: false,
    healthcare: false,
  };
  amenitiesSource = {
    type: 'FeatureCollection',
    features: [] as GeoJSON.Feature[],
  };

  // Radius circle source
  radiusCircleSource = {
    type: 'FeatureCollection',
    features: [] as GeoJSON.Feature[],
  };

  // Map markers
  markers: google.maps.Marker[] = [];
  public propertyLocation: GeoJSON.Feature<GeoJSON.Point> | null = null;
  hdbTowns: string[] = [];
  showEthnicBuyerQuery = false;
  showEthnicSellerQuery = false;
  public selectedMarkerCoordinates: number[] | null = null;
  public selectedPropertyName: string | null = null;
  public selectedProperty: any = null;
  selectedPropertyFeature: GeoJSON.Feature<GeoJSON.Point> | null = null;
  public showPropertyDetails = false;
  public showTransactionChart = false;
  public propertyTransactionData: any = null;
  public transactionMedianPrices: any[] = [];
  public transactionAveragePSF: any[] = [];
  // Chart references and instances
  @ViewChild('medianPriceChart') medianPriceChartRef?: ElementRef<HTMLCanvasElement>;
  @ViewChild('avgPsfChart') avgPsfChartRef?: ElementRef<HTMLCanvasElement>;
  private medianPriceChart?: Chart;
  private avgPsfChart?: Chart;
  private chartsInitialized = false;

  async ngOnInit(): Promise<void> {
    console.log('PROPERTY DATA FROM PREVIEW LISTING', this.propertyData);
    this.propertyLocation = {
      type: 'Feature',
      geometry: {
        type: 'Point',
        coordinates: [
          this.propertyData?.longitude || 103.8198,
          this.propertyData?.latitude || 1.3521,
        ],
      },
      properties: {},
    };
    this.fitMapToMarkers([
      [this.propertyData?.longitude || 103.8198, this.propertyData?.latitude || 1.3521],
    ]);
    // Initialize variables
    const response = await this.#hdbController.getAllTowns();
    this.hdbTowns = response.data.map((town: any) => town.name);
    console.log(this.propertyLocation);
    this.assignColorsToTowns();
  }
  // Implement AfterViewChecked to initialize charts after the view is rendered
  ngAfterViewChecked(): void {
    if (
      this.showTransactionChart &&
      !this.chartsInitialized &&
      this.medianPriceChartRef &&
      this.avgPsfChartRef
    ) {
      this.initializeCharts();
    }
  }
  // Initialize charts
  private initializeCharts(): void {
    if (
      !this.medianPriceChartRef ||
      !this.avgPsfChartRef ||
      !this.transactionMedianPrices ||
      !this.transactionAveragePSF ||
      this.transactionMedianPrices.length === 0
    ) {
      return;
    }

    // Destroy existing charts if they exist
    this.destroyCharts();

    // Extract data for charts
    const labels = this.transactionMedianPrices.map((entry) => entry[0]);
    const medianPrices = this.transactionMedianPrices.map((entry) => entry[1]);
    const avgPSFs = this.transactionAveragePSF.map((entry) => entry[1]);

    // Create median price chart
    this.medianPriceChart = new Chart(this.medianPriceChartRef.nativeElement, {
      type: 'line',
      data: {
        labels,
        datasets: [
          {
            label: 'Median Price',
            data: medianPrices,
            fill: false,
            tension: 0.2,
            borderColor: '#4f46e5',
            backgroundColor: 'rgba(79, 70, 229, 0.2)',
          },
        ],
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: false,
            ticks: {
              callback: (value) => {
                return '$' + value.toLocaleString();
              },
            },
          },
        },
      },
    });

    // Create average PSF chart
    this.avgPsfChart = new Chart(this.avgPsfChartRef.nativeElement, {
      type: 'line',
      data: {
        labels,
        datasets: [
          {
            label: 'Average PSF',
            data: avgPSFs,
            fill: false,
            tension: 0.2,
            borderColor: '#10b981',
            backgroundColor: 'rgba(16, 185, 129, 0.2)',
          },
        ],
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: false,
            ticks: {
              callback: (value) => {
                return '$' + value.toLocaleString();
              },
            },
          },
        },
      },
    });

    this.chartsInitialized = true;
  }
  private assignColorsToTowns(): void {
    this.townColors = {};
    this.hdbTowns.forEach((town, index) => {
      const townName = town; // Town name is at index 1
      this.townColors[townName] = this.townColorPalette[index % this.townColorPalette.length];
    });
    console.log('Town colors:', this.townColors);
  }
  onMapLoad(map: mapBoxMap): void {
    console.log('MAP LOADED', map);
    this.map = map;
    this.isMapLoading = false;
    const layers = map.getStyle().layers;
    console.log('LAYERS', layers);
    if (!layers) {
      return;
    }
    // this.labelLayerId = this.getLabelLayerId(layers);
    // console.log('LABEL LAYER', this.labelLayerId);
  }

  private getLabelLayerId(layers: LayerSpecification[]) {
    for (const layer of layers) {
      if (layer.type === 'symbol' && layer.layout?.['text-field']) {
        return layer.id;
      }
    }
    return;
  }
  updateMapStyle(map: mapBoxMap): void {
    const allowedSymbolLayers = [
      'school-label',
      'railway-label',
      'bus-stop-label',
      'place-town',
      'settlement-subdivision-label',
      'metro-label',
      'transit-label',
      'building',
      'road-street',
      'road-label',
      'building-number-label',
      'block-number-label',
    ];

    map.style.stylesheet.layers.forEach(function (layer) {
      if (layer.type === 'symbol' && !allowedSymbolLayers.includes(layer.id)) {
        map.setLayoutProperty(layer.id, 'visibility', 'none');
      }
    });
  }

  ngAfterViewInit(): void {
    // Initialize the map after the view is initialized
    setTimeout(() => {
      console.log('MAP', this.map);
    }, 100);
  }

  ngOnDestroy(): void {
    // Clean up resources
    this.clearMarkers();
  }

  /**
   * Initialize the Google Map
   */
  private initMap(): void {
    this.isMapLoading = true;

    try {
      // Make sure the Google Maps API is loaded
      if (typeof google === 'undefined' || typeof google.maps === 'undefined') {
        console.error('Google Maps API not loaded');
        this.isMapLoading = false;
        return;
      }

      // Create a new map instance
      // this.googleMap = new google.maps.Map(this.mapContainer.nativeElement, {
      //   center: this.propertyLocation,
      //   zoom: 15,
      //   mapTypeControl: true,
      //   streetViewControl: true,
      //   fullscreenControl: true,
      //   zoomControl: true,
      //   mapTypeId: google.maps.MapTypeId.ROADMAP,
      // });

      // Add property marker
      this.addPropertyMarker();

      // Add event listener for map idle event
      this.googleMap.addListener('idle', () => {
        this.isMapLoading = false;
      });
    } catch (error) {
      console.error('Error initializing map:', error);
      this.isMapLoading = false;
    }
  }

  /**
   * Add a marker for the property location
   */
  private addPropertyMarker(): void {
    if (!this.googleMap) return;

    try {
      // Create a marker with a custom icon or default icon if custom one is not available
      // this.propertyMarker = new google.maps.Marker({
      //   position: this.propertyLocation,
      //   map: this.googleMap,
      //   icon: {
      //     path: google.maps.SymbolPath.CIRCLE,
      //     fillColor: '#4285F4', // Google blue
      //     fillOpacity: 1,
      //     strokeColor: '#FFFFFF',
      //     strokeWeight: 2,
      //     scale: 10,
      //   },
      //   title: this.propertyAddress,
      //   animation: google.maps.Animation.DROP,
      //   zIndex: 1000, // Ensure it's above other markers
      // });

      // Add info window
      const infoWindow = new google.maps.InfoWindow({
        content: `
          <div class="p-2">
            <h3 class="font-bold">${this.propertyAddress}</h3>
            <p class="text-sm text-gray-600">4-Room HDB Flat</p>
          </div>
        `,
      });

      this.propertyMarker.addListener('click', () => {
        infoWindow.open(this.googleMap, this.propertyMarker);
      });

      // Open info window by default
      infoWindow.open(this.googleMap, this.propertyMarker);
    } catch (error) {
      console.error('Error adding property marker:', error);
    }
  }

  /**
   * Clear all markers from the map
   */
  private clearMarkers(): void {
    this.markers.forEach((marker) => marker.setMap(null));
    this.markers = [];
  }

  getAmenitiesPopUp(event: any): void {
    // Extract the feature data from the event
    const feature = event.features[0];

    if (!feature) {
      console.error('No feature found in event');
      return;
    }

    console.log('FEATURE', feature);

    // Extract properties from the feature
    const properties = feature.properties || {};
    const amenityType = properties.type || 'unknown';

    // Create a structured object based on amenity type
    let amenityData: any = {
      type: amenityType,
      name:
        properties.name ||
        properties.schoolName ||
        properties.stationName ||
        properties.buildingName ||
        'Unknown',
      coordinates: feature.geometry.coordinates,
    };

    // Add type-specific properties
    switch (amenityType) {
      case 'school':
        amenityData = {
          ...amenityData,
          address: properties.address || '',
          postal: properties.postal || '',
          telephoneNumber: properties.telephoneNumber || '',
          email: properties.email || '',
          town: properties.town || '',
          schoolLevel: properties.schoolLevel || '',
          schoolType: properties.schoolType || '',
          category: 'Education',
        };
        break;

      case 'mrt':
        amenityData = {
          ...amenityData,
          buildingName: properties.buildingName || '',
          alphaNumericCode: properties.alphaNumericCode || '',
          address: properties.address || '',
          lines: properties.lines || [],
          interchange: properties.interchange || false,
          category: 'Transportation',
        };
        break;

      case 'healthcare':
        amenityData = {
          ...amenityData,
          buildingName: properties.buildingName || '',
          address: properties.address || '',
          postal: properties.postal || '',
          facilityType: properties.facilityType || 'Healthcare Facility',
          services: properties.services || [],
          category: 'Healthcare',
        };
        break;

      case 'busStop':
        amenityData = {
          ...amenityData,
          details: properties.details || '',
          busServices: properties.busServices || [],
          category: 'Transportation',
        };
        break;

      case 'hawker':
        amenityData = {
          ...amenityData,
          description: properties.description || '',
          address: properties.blockHouseNumber
            ? `${properties.blockHouseNumber} ${properties.streetName || ''}`
            : properties.address || '',
          postalCode: properties.postalCode || '',
          stallCount: properties.cookedFoodStalls || '',
          photoUrl: properties.photoUrl || '',
          category: 'Food & Dining',
        };
        break;

      default:
        amenityData = {
          ...amenityData,
          address: properties.address || '',
          description: properties.description || '',
          category: 'Point of Interest',
        };
    }

    // Store the amenity data for the popup
    this.amenitiesPopUpContent = amenityData;

    // Create a GeoJSON feature for the popup
    const popupFeature: GeoJSON.Feature<GeoJSON.Point> = {
      type: 'Feature',
      geometry: {
        type: 'Point',
        coordinates: feature.geometry.coordinates,
      },
      properties: {},
    };

    // Set the popup feature and show it
    this.amenitiesFeature = popupFeature;
    this.showAmenitiesPopup = true;
  }

  async findNearbyAmenities(
    type: 'userLocation' | 'searchLocation',
    amenityType: 'schools' | 'busStops' | 'hawker' | 'mrt' | 'healthcare' | 'hdbData',
  ): Promise<void> {
    if (!type || !this[type]) {
      this.#snackBarService.error('Please set your location first');
      return;
    }

    try {
      // Convert radius from km to meters for calculations
      const radiusInMeters = this.selectedRadius * 1000;

      // Get user coordinates
      const coordinates = this[type].geometry.coordinates;
      const [longitude, latitude] = coordinates;

      // Draw radius circle
      this.drawRadiusCircle([longitude, latitude], this.selectedRadius);

      let response;
      let amenityName = '';

      // Call the appropriate controller based on amenity type
      switch (amenityType) {
        case 'schools':
          response = await this.#schoolController.findSchoolsWithinRadius({
            latitude,
            longitude,
            radius: radiusInMeters,
          });
          if (response.success) {
            this.addSchoolMarkers(response.schools);
            amenityName = 'schools';
          }
          break;

        case 'busStops':
          response = await this.#busStopsController.findBusStopsWithinRadius({
            latitude,
            longitude,
            radius: radiusInMeters,
          });
          if (response.success) {
            this.addBusStopMarkers(response.busStops);
            amenityName = 'bus stops';
          }
          break;

        case 'hawker':
          response = await this.#hawkerController.findHawkerCentersWithinRadius({
            latitude,
            longitude,
            radius: radiusInMeters,
          });
          if (response.success) {
            this.addHawkerCenterMarkers(response.hawkerCenters);
            amenityName = 'hawker centers';
          }
          break;

        case 'mrt':
          response = await this.#metroController.getMetroStationsWithinRadius({
            latitude,
            longitude,
            radius: radiusInMeters,
          });
          if (response.success) {
            this.addMetroStationMarkers(response.metroStations);
            amenityName = 'metro stations';
          }
          break;

        case 'healthcare':
          response = await this.#hospitalController.getHospitalsWithinRadius({
            latitude,
            longitude,
            radius: radiusInMeters,
          });
          if (response.success) {
            this.addHospitalMarkers(response.hospitals);
            amenityName = 'hospitals';
          }
          break;

        case 'hdbData':
          response = await this.#hdbController.findHDBPropertiesNearUserLocation({
            latitude,
            longitude,
            radius: radiusInMeters,
          });
          if (response.success) {
            this.addHDBMarkers(response.hdb);
            amenityName = 'HDB properties';
          }
          break;
      }

      if (response && response.success) {
        const count = response.count || (response.hdb ? response.hdb.length : 0);
        this.#snackBarService.success(
          `Found ${count} ${amenityName} within ${this.selectedRadius}km`,
        );
      } else {
        this.#snackBarService.error(`Failed to find ${amenityName}`);
      }
    } catch (error) {
      console.error(`Error finding ${amenityType}:`, error);
      this.#snackBarService.error(`Error finding ${amenityType}`);
    }
  }
  private drawRadiusCircle(center: [number, number], radiusInKm: number): void {
    this.radiusCircleSource = {
      type: 'FeatureCollection',
      features: [],
    };
    const points = 64;
    const km = radiusInKm;
    const earthRadius = 6371;
    const [centerLng, centerLat] = center;
    const centerLatRad = (centerLat * Math.PI) / 180;
    const centerLngRad = (centerLng * Math.PI) / 180;

    // Create points for the circle
    const coordinates = [];
    for (let i = 0; i <= points; i++) {
      const angle = (i * 2 * Math.PI) / points;
      const dx = (km / earthRadius) * Math.cos(angle);
      const dy = (km / earthRadius) * Math.sin(angle);

      const latRad = centerLatRad + dy;
      const lngRad = centerLngRad + dx / Math.cos(centerLatRad);

      const lat = (latRad * 180) / Math.PI;
      const lng = (lngRad * 180) / Math.PI;
      coordinates.push([lng, lat]);
    }

    // Create a polygon feature for the circle
    const circleFeature: GeoJSON.Feature = {
      type: 'Feature',
      geometry: {
        type: 'Polygon',
        coordinates: [coordinates],
      },
      properties: {
        radius: radiusInKm,
        center: center,
      },
    };

    // Update the radius circle source
    this.radiusCircleSource = {
      type: 'FeatureCollection',
      features: [circleFeature],
    };
  }

  handleNearbyOptionChange(
    optionId: string,
    isChecked: boolean,
    type: 'userLocation' | 'searchLocation',
  ): void {
    // Update the option state
    this.nearbyOptions[optionId as keyof typeof this.nearbyOptions] = isChecked;
    console.log('UPDATED SELECTION', this.nearbyOptions);
    // Implement the logic to show/hide nearby options
    console.log(`Option ${optionId} changed to ${isChecked}`);
    this.showPropertyPopup = false;
    // Example implementation for different options
    switch (optionId) {
      case 'schools':
        if (isChecked) {
          this.findNearbyAmenities(type, 'schools');
        } else {
          this.clearNearbyMarkers();
        }
        break;
      case 'busStops':
        if (isChecked) {
          this.findNearbyAmenities(type, 'busStops');
        } else {
          this.clearNearbyMarkers();
        }
        break;
      case 'healthcare':
        if (isChecked) {
          this.findNearbyAmenities(type, 'healthcare');
        } else {
          this.clearNearbyMarkers();
        }
        break;
      case 'hawker':
        // Logic to show/hide hawker centers
        if (isChecked) {
          this.findNearbyAmenities(type, 'hawker');
        } else {
          this.clearNearbyMarkers();
        }
        break;
      case 'mrt':
        if (isChecked) {
          this.findNearbyAmenities(type, 'mrt');
        } else {
          this.clearNearbyMarkers();
        }
        break;
      case 'salesListings':
        if (isChecked) {
          // this.displaySaleListings();
        } else {
          this.clearMarkers();
        }
        break;
      case 'hdbData':
        if (isChecked) {
          this.findNearbyAmenities(type, 'hdbData');
        } else {
          this.clearMarkers();
        }
        break;
      // Add other cases as needed
    }
  }
  private clearNearbyMarkers(): void {
    this.amenitiesSource = {
      type: 'FeatureCollection',
      features: [],
    };

    // Clear radius circle if no nearby options are active
    const anyActive = Object.values(this.nearbyOptions).some((value) => value);
    if (!anyActive) {
      this.radiusCircleSource = {
        type: 'FeatureCollection',
        features: [],
      };
    }
  }
  private addBusStopMarkers(busStops: any[]): void {
    // Clear existing bus stop markers;
    this.clearNearbyMarkers();

    const features = busStops
      .map((busStop) => {
        if (busStop.location?.coordinates?.length === 2) {
          const [longitude, latitude] = busStop.location.coordinates;

          return {
            type: 'Feature',
            style:
              'background-image: url(https://picsum.photos/id/870/50/50/); width: 50px; height: 50px',
            geometry: {
              type: 'Point',
              coordinates: [longitude, latitude],
            },
            properties: {
              icon: 'bus',
              title: busStop.name,
              name: busStop.name,
              details: busStop.details || '',
              busServices: busStop.busServices || [],
              type: 'busStop',
              description: `
            <div class="p-4">
              <h3 class="font-bold">${busStop.name}</h3>
              ${busStop.details ? `<p><span class="font-semibold">Landmark:</span> ${busStop.details}</p>` : ''}
              ${
                busStop.busServices && busStop.busServices.length > 0
                  ? `<p><span class="font-semibold">Bus Services:</span> ${busStop.busServices.join(', ')}</p>`
                  : ''
              }
            </div>
          `,
            },
          };
        }
        return null;
      })
      .filter(Boolean) as GeoJSON.Feature[];

    // Update the schools source
    this.amenitiesSource = {
      type: 'FeatureCollection',
      features,
    };
  }

  private addSchoolMarkers(schools: any[]): void {
    // Clear existing school markers
    this.clearNearbyMarkers();

    const features = schools
      .map((school) => {
        if (school.location?.coordinates?.length === 2) {
          const [longitude, latitude] = school.location.coordinates;

          return {
            type: 'Feature',
            geometry: {
              type: 'Point',
              coordinates: [longitude, latitude],
            },
            properties: {
              icon: 'school',
              title: school.name,
              schoolName: school.schoolName,
              address: school.address,
              postal: school.postal,
              telephoneNumber: school.telephoneNumber || 'N/A',
              email: school.email || 'N/A',
              town: school.town,
              schoolLevel: school.schoolLevel,
              schoolType: school.schoolType,
              type: 'school',
              description: `
            <div class="p-2 max-w-md text-sm text-gray-800">
              <h3 class="text-lg text-center font-bold text-blue-700 mb-1">${school.schoolName}</h3>
              <div class="space-y-1">
                <p><span class="font-semibold py-1">Address:</span> ${school.address}</p>
                <p><span class="font-semibold py-1">Postal Code:</span> ${school.postal}</p>
                <p><span class="font-semibold py-1">Telephone:</span> ${school.telephoneNumber || 'N/A'}</p>
                <p>
                  <span class="font-semibold py-1">Email:</span>
                  <a href="mailto:${school.email}" class="text-blue-600 underline">${school.email || 'N/A'}</a>
                </p>
                <p><span class="font-semibold py-1">Town:</span> ${school.town}</p>
                <p><span class="font-semibold py-1">Level:</span> ${school.schoolLevel}</p>
                <p><span class="font-semibold py-1">Type:</span> ${school.schoolType}</p>
              </div>
            </div>
          `,
            },
          };
        }
        return null;
      })
      .filter(Boolean) as GeoJSON.Feature[];

    // Update the schools source
    this.amenitiesSource = {
      type: 'FeatureCollection',
      features,
    };
  }

  private addHawkerCenterMarkers(hawkers: any[]): void {
    // Clear existing bus stop markers;
    this.clearNearbyMarkers();

    const features = hawkers
      .map((hawker) => {
        if (hawker.location?.coordinates?.length === 2) {
          const [longitude, latitude] = hawker.location.coordinates;

          return {
            type: 'Feature',
            geometry: {
              type: 'Point',
              coordinates: [longitude, latitude],
            },
            properties: {
              icon: 'hawker',
              title: hawker.name,
              name: hawker.name,
              description: hawker.description || '',
              blockHouseNumber: hawker.blockHouseNumber || '',
              streetName: hawker.streetName || '',
              address:
                hawker.address ||
                (hawker.blockHouseNumber
                  ? `${hawker.blockHouseNumber} ${hawker.streetName || ''}`
                  : ''),
              postalCode: hawker.postalCode || '',
              cookedFoodStalls: hawker.cookedFoodStalls || '',
              photoUrl: hawker.photoUrl || '',
              type: 'hawker',
            },
          };
        }
        return null;
      })
      .filter(Boolean) as GeoJSON.Feature[];

    // Update the schools source
    this.amenitiesSource = {
      type: 'FeatureCollection',
      features,
    };
  }

  private addMetroStationMarkers(metroStations: any[]): void {
    this.clearNearbyMarkers();
    const features = metroStations
      .map((metroStation) => {
        if (metroStation.location?.coordinates?.length === 2) {
          const [longitude, latitude] = metroStation.location.coordinates;

          return {
            type: 'Feature',
            geometry: {
              type: 'Point',
              coordinates: [longitude, latitude],
            },
            properties: {
              icon: 'metro',
              title: metroStation.stationName,
              stationName: metroStation.stationName,
              name: metroStation.stationName,
              buildingName: metroStation.buildingName || '',
              alphaNumericCode: metroStation.alphaNumericCode || '',
              address: metroStation.address || '',
              lines: metroStation.lines || [],
              interchange: metroStation.interchange || false,
              type: 'mrt',
              description: `
            <div class="p-4 max-w-xs text-sm text-gray-800">
              <div class="flex items-center mb-2">
                <img src="https://maps.google.com/mapfiles/kml/shapes/rail.png" class="w-5 h-5 mr-2" alt="Metro Icon" />
                <h3 class="text-base font-bold text-blue-700">${metroStation.stationName}</h3>
              </div>
              <div class="space-y-1">
                <p><span class="font-semibold">Code:</span> ${metroStation.alphaNumericCode || 'N/A'}</p>
                <p><span class="font-semibold">Address:</span> ${metroStation.address || 'N/A'}</p>
              </div>
            </div>
          `,
            },
          };
        }
        return null;
      })
      .filter(Boolean) as GeoJSON.Feature[];

    // Update the schools source
    this.amenitiesSource = {
      type: 'FeatureCollection',
      features,
    };
  }

  private addHospitalMarkers(hospitals: any[]): void {
    this.clearNearbyMarkers();
    const features = hospitals
      .map((hospital) => {
        if (hospital.location?.coordinates?.length === 2) {
          const [longitude, latitude] = hospital.location.coordinates;

          return {
            type: 'Feature',
            geometry: {
              type: 'Point',
              coordinates: [longitude, latitude],
            },
            properties: {
              icon: 'hospital',
              title: hospital.name,
              name: hospital.name,
              address: hospital.address || '',
              postal: hospital.postal || '',
              buildingName: hospital.buildingName || '',
              facilityType: hospital.facilityType || 'Hospital',
              services: hospital.services || [],
              type: 'healthcare',
            },
          };
        }
        return null;
      })
      .filter(Boolean) as GeoJSON.Feature[];

    // Update the schools source
    this.amenitiesSource = {
      type: 'FeatureCollection',
      features,
    };
  }
  private addHDBMarkers(properties: any[]): void {
    // Create features for HDB properties
    const features = properties
      .filter((property) => property.location.coordinates.length === 2 && property.postalCode)
      .map((property) => {
        // Extract coordinates
        const [lng, lat] = property.location.coordinates;
        return {
          type: 'Feature',
          geometry: {
            type: 'Point',
            coordinates: [lng, lat],
          },
          properties: {
            id: property._id,
            title: property.title || `Block ${property.blockNumber}, ${property.streetAddress}`,
            blockNumber: property.blockNumber,
            streetAddress: property.streetAddress,
            town: property.town,
            // color: this.townColors[property.town],
          },
        };
      });

    // Update the HDB source
    this.hdbSourceData = {
      type: 'FeatureCollection',
      features,
    };
  }

  clearAmenities() {
    this.amenitiesSource = {
      type: 'FeatureCollection',
      features: [],
    };
    this.radiusCircleSource = {
      type: 'FeatureCollection',
      features: [],
    };
    this.hdbSourceData = {
      type: 'FeatureCollection',
      features: [],
    };
    this.showAmenitiesPopup = false;
    this.amenitiesFeature = null;
    this.amenitiesPopUpContent = null;
  }
  fitMapToMarkers(coordsArray: number[][]): void {
    const bounds = new LngLatBounds();

    coordsArray.forEach((coord) => {
      bounds.extend(coord as [number, number]);
    });

    const center = bounds.getCenter();
    this.lng = center.lng;
    this.lat = center.lat;
    this.zoom = 12;
  }
  formatHDBTransactionTableData(tableData: any) {
    const formattedData = tableData.map((transaction: any) => {
      const values = Object.values(transaction).flat();
      const date = values[2];
      const flatType = values[3];
      const floorRange = values[6];
      const areaSqm = Number(values[7]);
      const areaSqft = Math.round(areaSqm * 10.7639);
      const price = Number(values[8]);
      const psf = Math.round(price / areaSqft);
      const formattedPrice = `$${price.toLocaleString()}`;
      const formattedPsf = `$${psf}`;
      return [date, flatType, floorRange, areaSqft.toString(), formattedPrice, formattedPsf];
    });
    return formattedData;
  }
  async getPropertyDetails(feature: any): Promise<void> {
    console.log('Property clicked', feature);
    const id = feature.properties.id;
    if (
      feature.geometry &&
      'coordinates' in feature.geometry &&
      Array.isArray(feature.geometry.coordinates)
    ) {
      this.selectedPropertyFeature = {
        type: 'Feature',
        geometry: {
          type: 'Point',
          coordinates: feature.geometry.coordinates as [number, number],
        },
        properties: {
          ...feature.properties,
        },
      };

      // Set property name for the marker label
      if (feature.properties.blockNumber && feature.properties.streetAddress) {
        this.selectedPropertyName = `Block ${feature.properties.blockNumber}, ${feature.properties.streetAddress}`;
      } else if (feature.properties.title) {
        this.selectedPropertyName = feature.properties.title;
      } else if (feature.properties.projectName) {
        this.selectedPropertyName = feature.properties.projectName;
      } else {
        this.selectedPropertyName = 'Selected Property';
      }

      // Store selected marker coordinates for highlighting
      this.selectedMarkerCoordinates = feature.geometry.coordinates as number[];

      // Update marker colors
      this.updateMarkerColors();
      const response = await this.#hdbController.getById({ id });
      console.log('Property response', response);
      if (response.data) {
        this.togglePropertyModal(response.data);
      }
    }
  }

  private updateMarkerColors(): void {
    // Update HDB markers
    if (this.hdbSourceData && this.hdbSourceData.features) {
      console.log('Updating marker colors', this.selectedMarkerCoordinates);
      this.hdbSourceData.features = this.hdbSourceData.features.map((feature) => {
        if (
          this.selectedMarkerCoordinates &&
          feature.geometry.coordinates[0] === this.selectedMarkerCoordinates[0] &&
          feature.geometry.coordinates[1] === this.selectedMarkerCoordinates[1]
        ) {
          // Highlight the selected marker
          return {
            ...feature,
            properties: {
              ...feature.properties,
              color: '#FF0000', // Bright red for selected marker
              selectedMarker: true,
            },
          };
        } else {
          // Reset color for other markers
          return {
            ...feature,
            properties: {
              ...feature.properties,
              color: feature.properties.color || '#3FB1CE',
              selectedMarker: false,
            },
          };
        }
      });
    }
  }
  public generateMonthlyMedianPricesAndAvgPSF(data: any[]) {
    console.log('Generating monthly median prices and average PSF...', data);
    if (!data || data.length === 0) {
      return { transactionMedianPrices: [], transactionAveragePSF: [] };
    }

    const pricesByYearMonth = new Map<string, number[]>();
    const psfByYearMonth = new Map<string, number[]>();

    // Conversion factor from square meters to square feet
    const sqmToSqftFactor = 10.764;

    data.forEach((row) => {
      // Use month field instead of date
      const monthStr = row.month; // Format: 'YYYY-MM'
      if (!monthStr) return;

      // Extract resale_price instead of price
      const price = Number(row.resale_price);

      // Calculate PSF from floor_area_sqm
      const areaSqm = Number(row.floor_area_sqm);
      const areaSqft = areaSqm * sqmToSqftFactor;
      const psf = price / areaSqft;

      // Skip invalid entries
      if (isNaN(price) || isNaN(psf) || price <= 0 || psf <= 0) return;

      if (!pricesByYearMonth.has(monthStr)) {
        pricesByYearMonth.set(monthStr, []);
        psfByYearMonth.set(monthStr, []);
      }

      pricesByYearMonth.get(monthStr)!.push(price);
      psfByYearMonth.get(monthStr)!.push(psf);
    });

    const result = Array.from(pricesByYearMonth.keys()).map((yearMonth) => {
      const prices = pricesByYearMonth.get(yearMonth)!;
      const psfs = psfByYearMonth.get(yearMonth)!;

      // Calculate median price
      const sortedPrices = [...prices].sort((a, b) => a - b);
      const median =
        sortedPrices.length % 2 === 0
          ? (sortedPrices[sortedPrices.length / 2 - 1] + sortedPrices[sortedPrices.length / 2]) / 2
          : sortedPrices[Math.floor(sortedPrices.length / 2)];

      // Calculate average PSF
      const avgPSF = psfs.reduce((sum, val) => sum + val, 0) / psfs.length;

      // Format date for display
      const [year, month] = yearMonth.split('-');
      const date = new Date(Number(year), Number(month) - 1);
      const formattedDate = date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
      });

      return {
        date: formattedDate,
        medianPrice: Math.round(median), // Round to nearest dollar
        averagePSF: Math.round(avgPSF), // Round to nearest dollar
      };
    });

    // Sort results chronologically
    const sortedResult = result.sort((a, b) => {
      const dateA = new Date(a.date);
      const dateB = new Date(b.date);
      return dateA.getTime() - dateB.getTime();
    });

    // Format data for charts
    const transactionMedianPrices = sortedResult.map(({ date, medianPrice }) => [
      date,
      medianPrice,
    ]);
    const transactionAveragePSF = sortedResult.map(({ date, averagePSF }) => [date, averagePSF]);

    return { transactionMedianPrices, transactionAveragePSF };
  }

  async togglePropertyModal(data: any) {
    try {
      const [longitude, latitude] = data.location.coordinates;
      const hdbResponse = await this.#hdbController.extractHDBTransactionData({
        latitude: Number(latitude),
        longitude: Number(longitude),
      });
      // Format the transaction data
      const formattedData = this.formatHDBTransactionTableData(hdbResponse?.data);

      const tableData = {
        heading: 'Transaction Details',
        header: ['Date', 'Type', 'Floor Level', 'Area (SQFT)', 'Price (S$)', 'PSF (S$)'],
        data: formattedData?.slice(0, 10),
      };

      const { transactionMedianPrices, transactionAveragePSF } =
        this.generateMonthlyMedianPricesAndAvgPSF(hdbResponse?.data);

      // Update the component properties for the embedded panel
      this.selectedProperty = data;
      this.propertyTransactionData = tableData;
      this.transactionMedianPrices = transactionMedianPrices;
      this.transactionAveragePSF = transactionAveragePSF;
      this.showPropertyDetails = true;

      console.log('SELECTED PROPERTY DATA', this.selectedProperty);
      this.showTransactionChart = false;
      this.selectedMarkerCoordinates = data.location.coordinates;

      // Resize the map
      if (this.map) {
        setTimeout(() => {
          this.fitMapToMarkers([this.selectedProperty.location.coordinates]);
        }, 100);
      }
    } catch (error) {
      console.error('Error in togglePropertyModal:', error);
    }
  }
  private destroyCharts(): void {
    if (this.medianPriceChart) {
      this.medianPriceChart.destroy();
      this.medianPriceChart = undefined;
    }

    if (this.avgPsfChart) {
      this.avgPsfChart.destroy();
      this.avgPsfChart = undefined;
    }

    this.chartsInitialized = false;
  }
  closePropertyDetails(): void {
    this.showPropertyDetails = false;
    this.selectedProperty = null;
    this.propertyTransactionData = null;
    this.showTransactionChart = false;
    this.selectedMarkerCoordinates = null;

    // Reset marker colors
    this.updateMarkerColors();

    // Destroy charts if they exist
    this.destroyCharts();

    // Resize the map
    if (this.map) {
      setTimeout(() => {
        this.fitMapToMarkers([this.selectedProperty.geometry.coordinates]);
      }, 100);
    }
  }
  toggleTransactionChart(): void {
    this.showTransactionChart = !this.showTransactionChart;
    this.chartsInitialized = false; // Reset chart initialization flag
  }
}
