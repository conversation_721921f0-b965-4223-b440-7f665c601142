import { Schema, model, type Document, type Model } from 'mongoose';

export interface ISchool extends Document {
  schoolName: string;
  address: string;
  postal: number;
  telephoneNumber: number;
  email: string | null;
  town: string;
  schoolLevel: string;
  schoolType: string;
  location: {
    type: 'Point';
    coordinates: [number, number];
  };
}

const SchoolSchema = new Schema<ISchool>({
  schoolName: { type: String, required: true },
  address: { type: String, required: true },
  postal: { type: Number, required: true },
  telephoneNumber: { type: Number, required: true },
  email: { type: String, default: null },
  town: { type: String, required: true },
  schoolLevel: { type: String, required: true },
  schoolType: { type: String, required: true },
  location: {
    type: {
      type: String,
      enum: ['Point'],
      default: 'Point',
      required: true,
    },
    coordinates: {
      type: [Number],
      required: true,
    },
  },
});

SchoolSchema.index({ location: '2dsphere' });

export const SchoolModel: Model<ISchool> = model<ISchool>('School', SchoolSchema);
