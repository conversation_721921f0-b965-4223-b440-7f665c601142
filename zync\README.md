

This is Backend code for learning
NPM installing 

<!-- 1) Express -->
<!-- We use Express router, middleware, request, response -->

<!-- 2) Nodemon -->
<!-- We use Nodemon for auto restart -->
<!-- "start": "node server.js" npm start -->
<!-- "dev": "nodemon server.js" npm run dev -->

<!-- 3) dotenv -->
<!-- We use dotenv for environment variables -->

<!-- 4) ejs -->
<!-- We use ejs for template engine -->

<!-- 5) mysql2 -->
<!-- We use mysql2 for database -->

<!-- 6) bcrypt --> 
<!-- We use bcrypt for password encryption -->

<!-- 7) body-parser -->
<!-- We use body-parser for parsing request body -->

<!-- 8) jsonwebtoken -->
<!-- We use jsonwebtoken for authentication and storing password in database -->

<!-- 9) nodemailer -->
<!-- We use nodemailer for sending emails -->




