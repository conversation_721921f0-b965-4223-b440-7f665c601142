import type { Observable } from 'rxjs';
import { Subject, firstValueFrom, fromEvent, map, merge, race, startWith, takeUntil } from 'rxjs';

export const noop = () => {
  //
};

export const [SQL, HTML, JS, css, TS] = Array(6).fill(String.raw);

// export const groupBy = <T>(x: T[], f: (v: T, i: number, x: T[]) => string) =>
//   x.reduce((a, b, i) => ((a[f(b, i, x)] ||= []).push(b), a), {} as Record<string, T[]>);

export const makeKebab = (str: string) =>
  str.replace(/[A-Z]+(?![a-z])|[A-Z]/g, ($, ofs) => (ofs ? '-' : '') + $.toLowerCase());

export const getRandomCode = (length: number) => {
  const a = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
  return [...Array(length)].map(() => a[Math.floor(Math.random() * a.length)]).join('');
};

export const fastQueue = async <T, Q = void>(
  x: T[],
  threads: number,
  fn: (v: T, i: number, a: T[], threadId: number) => Promise<Q>,
) => {
  let k = 0;
  const result = Array<Q>(x.length);
  await Promise.all(
    [...Array(Math.min(threads, x.length))].map(async (_, i) => {
      while (k < x.length) result[k] = await fn(x[k], k++, x, i);
    }),
  );
  return result;
};

type EventsMap = Record<string, any>;

export class ReactiveEmitter<Events extends EventsMap> {
  events: Partial<{ [E in keyof Events]: Subject<Events[E]> }> = {};

  emit<K extends keyof Events>(event: K, arg: Events[K]) {
    this.events[event]?.next(arg);
  }

  on<K extends keyof Events>(event: K) {
    return (this.events[event] ??= new Subject<Events[K]>());
  }
}

export async function* otag<Value>(
  observable: Observable<Value>,
  signal?: AbortSignal,
): AsyncIterableIterator<Value> {
  let res!: (value: Value | PromiseLike<Value>) => void;
  let rej!: (reason?: any) => void;
  let finished = false;
  const sub = observable.subscribe({
    next(value) {
      res?.(value);
      res = rej = undefined!;
    },
    error(error: unknown) {
      rej?.(error instanceof Error ? error : new Error(String(error)));
      res = rej = undefined!;
    },
    complete() {
      finished = true;
    },
  });

  try {
    // TODO: signal should unsubscribe on resolve/reject or wait for rxjs-8
    signal?.addEventListener(
      'abort',
      () => {
        finished = true;
        sub.unsubscribe();
        rej?.(new AbortErrorException('O2AG: Task was aborted'));
      },
      { once: true },
    );
    while (true) {
      const x = new Promise<Value>((resolve, reject) => {
        res = resolve;
        rej = reject;
      });
      if (finished) break;
      const value = await x;
      yield value;
    }
  } finally {
    sub.unsubscribe();
  }
}

export class AbortErrorException extends Error {
  code = 20;
  override name = 'AbortError';
  constructor(message?: string) {
    super(message ?? 'AbortError');
  }
}

export const firstEvent_ = (events: [any, string[]][]) => {
  const d = Promise.withResolvers<any>();
  const listeners = new Map<any, Record<string, any>>();
  const resolve = (ee: any, event: string) => {
    const listener = (...args: any) => {
      d.resolve({ ee, event, args });
      removeListeners();
    };
    const holder = listeners.get(ee) ?? {};
    holder[event] = listener;
    listeners.set(ee, holder);
    return listener;
  };
  const removeListeners = () => {
    for (const [emitter, eventNames] of events) {
      if (emitter) {
        for (const eventName of eventNames) {
          if (emitter.off) emitter.off(eventName, listeners.get(emitter)![eventName]);
          else emitter.removeEventListener(eventName, listeners.get(emitter)![eventName]);
        }
      }
    }
  };
  for (const [emitter, eventNames] of events) {
    if (emitter) {
      for (const eventName of eventNames) {
        if (emitter.once) emitter.once(eventName, resolve(emitter, eventName));
        else
          emitter.addEventListener(eventName, resolve(emitter, eventName), {
            once: true,
          });
      }
    }
  }
  return d.promise;
};

export const firstValue = async <T>(o: Observable<T>) => {
  const v = await firstValueFrom(o, { defaultValue: o });
  if (v === o) throw new AbortErrorException();
  return v as Promise<T>;
};

export const once = (emitter: any, name: string, signal?: AbortSignal) => {
  // todo: fix type
  let o$ = fromEvent(emitter, name);
  if (signal) o$ = o$.pipe(takeUntil(fromEvent(signal, 'abort')));
  return firstValue(o$);
};

export const firstEvent = (events: [any, string[]][]) => {
  // todo: fix type
  const reactive = events
    .filter(([emitter]) => !!emitter)
    .flatMap(([emitter, names]) =>
      names.map((name) =>
        fromEvent(emitter, name).pipe(map((value) => ({ emitter, name, value }))),
      ),
    );
  return firstValueFrom(race(reactive));
};

export const lazyFastQueue = <T, Q = void>(
  threads: number,
  fn: (v: T, i: number, other: { signal?: AbortSignal }) => Promise<Q>,
  onIdle?: () => void,
) => {
  const queue: { v: T; d: PromiseWithResolvers<Q>; s?: AbortSignal }[] = [];
  let ix = 0;
  let k = 0;
  let busy = 0;
  const fun = async () => {
    while (ix < queue.length) {
      const item = queue[ix];
      queue[ix] = undefined as never;
      ix += 1;
      if (item.s?.aborted) {
        k++;
        item.d.reject(new AbortErrorException('Queue: Task was aborted'));
      } else {
        try {
          item.d.resolve(await fn(item.v, k++, { signal: item.s }));
        } catch (error) {
          item.d.reject(error);
        }
      }
    }
    busy -= 1;
    if (busy === 0) {
      queue.length = 0;
      ix = 0;
      onIdle?.();
    }
  };
  return (v: T, opt: { signal?: AbortSignal } = {}) => {
    const d = Promise.withResolvers<Q>();
    queue.push({ v, d, s: opt.signal });
    if (busy < threads) {
      busy += 1;
      fun();
    }
    return d.promise;
  };
};

export const flat2DArray = <T>(x: T[][]) => {
  const result = [] as T[];
  for (const item of x) for (const item2 of item) result.push(item2);
  return result;
};

export const keyOf = (x: any, v: any) => {
  return Object.keys(x).find((key) => x[key] === v);
};

export const partitionBy = <T>(arr: T[], predicate: (v: T, i: number, ar: T[]) => boolean) =>
  arr.reduce(
    (acc, item, index, array) => {
      acc[+!predicate(item, index, array)].push(item);
      return acc;
    },
    [[], []] as [T[], T[]],
  );

export const partition = <T>(x: T[], n: number) => {
  const r = x.length % n;
  const c = Math.ceil(x.length / n);
  const f = Math.floor(x.length / n);
  return [...(Array(n) as never[])].reduce(
    (a, _, i) => (a[0].push(x.slice(a[1], (a[1] += i < r ? c : f))), a),
    [[], 0] as [T[][], number],
  )[0];
};

export const partition2 = <T>(array: T[], parts: number): T[][] =>
  array.reduce(
    (acc, v, i) => (acc[i % parts].push(v), acc),
    [...Array(parts)].map(() => []) as T[][],
  );

/**
 * Waits for a specified number of milliseconds before resolving the promise.
 * If an AbortSignal is provided and it is aborted before the timeout, the promise is rejected with an AbortErrorException.
 *
 * @param {number} [ms=0] - The number of milliseconds to wait before resolving the promise.
 * @param {AbortSignal} [signal] - An optional AbortSignal to cancel the wait.
 * @returns {Promise<number>} A promise that resolves with the number of milliseconds waited, or rejects with an AbortErrorException if aborted.
 *
 * @throws {AbortErrorException} If the wait is aborted via the provided AbortSignal.
 */
export const wait = (ms = 0, signal?: AbortSignal): Promise<number> => {
  if (!signal) return new Promise((res) => setTimeout(res, ms, ms));
  if (signal.aborted) return Promise.reject(new AbortErrorException());
  return new Promise((res, rej) => {
    const signalHandler = () => {
      clearTimeout(id);
      rej(new AbortErrorException('Wait: Task was aborted'));
    };
    const resolve = () => (signal.removeEventListener('abort', signalHandler), res(ms));
    const id = setTimeout(resolve, ms);
    signal.addEventListener('abort', signalHandler, { once: true });
  });
};

export const loop = (total: number, fn: (i: number) => void) => {
  for (let i = 0; i < total; i++) fn(i);
};

export const loopWait = async (total: number, fn: (i: number) => Promise<void>) => {
  for (let i = 0; i < total; i++) await fn(i);
};

export const autoIncrement = (a: number) => () => a++;

export const hasAny = <T>(a: T[], b: T[]) => a.some((v) => b.includes(v));

export const chunk = <T>(array: T[], parts: number) =>
  [...Array(Math.ceil(array.length / parts))].map((_, i) =>
    array.slice(i * parts, (i + 1) * parts),
  );

export const shuffle = <T>(array: T[]) => {
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [array[i], array[j]] = [array[j], array[i]];
  }
  return array;
};

export const unique = <T>(array: T[]) => [...new Set(array)];

export const intersection = <T>(array1: T[], array2: T[]) =>
  array1.filter((v) => array2.includes(v));

export const diff = <T>(array1: T[], array2: T[]) => array1.filter((v) => !array2.includes(v));

export const symDiff = <T>(array1: T[], array2: T[]) =>
  diff(array1, array2).concat(diff(array2, array1));

export const union = <T>(array1: T[], array2: T[]) => diff(array1, array2).concat(array2);

export const intersectionBy = <T>(
  array1: T[],
  array2: T[],
  predicate: (array1Value: T, array2Value: T) => boolean,
) => array1.filter((v) => array2.some((u) => predicate(v, u)));

export const diffBy = <T>(
  array1: T[],
  array2: T[],
  predicate: (array1Value: T, array2Value: T) => boolean,
) => array1.filter((v) => !array2.some((u) => predicate(v, u)));

export const uniqueBy = <T>(array: T[], predicate: (v: T, i: number, a: T[]) => string) =>
  Object.values(
    array.reduce((acc: Record<string, T>, value, index) => {
      acc[predicate(value, index, array)] = value;
      return acc;
    }, {}),
  );

export const randomItem = <T>(array: T[]) =>
  // todo: should support string and typed array or array like
  array[Math.floor(Math.random() * array.length)];

export const isNil = (x: any) => x === undefined || x === null;

export const setPropertyByPath = (obj: any, path: string, value: any) =>
  path
    .split('.')
    .reduce(
      (acc, cur, i, arr) => ((acc[cur] = i === arr.length - 1 ? value : acc[cur] || {}), acc[cur]),
      obj,
    );

export const getPropertyByPath = (obj: any, path: string) =>
  path.split('.').reduce((acc, cur) => (acc ? acc[cur] : undefined), obj);

export const randInt = (x: number, y = 0) => {
  const [min, max] = [Math.min(x, y), Math.max(x, y)];
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

export const binarySearch = <T>(
  array: T[],
  comparator: (a: T) => number,
  min = 0,
  max = array.length - 1,
) => {
  let mid: number;
  let cmp: number;
  min |= 0;
  max |= 0;
  if (min < 0 || min > max) throw new RangeError('invalid lower bound');
  if (max < min || max >= array.length) throw new RangeError('invalid upper bound');
  while (min <= max) {
    mid = (max + min) >>> 1;
    cmp = Number(comparator(array[mid]));
    if (cmp < 0.0) min = mid + 1;
    else if (cmp > 0.0) max = mid - 1;
    else return mid;
  }
  return ~min;
};

const templateToJS3 = (content: string) =>
  content
    .split(/(<%- .*?%>)/gs)
    .map((v, i) => (i % 2 ? v.replace('<%-', '(').replace('%>', ')') : JSON.stringify(v)))
    .join('+');

const templateToJS2 = (content: string) =>
  content
    .split(/(<%= .*?%>)/gs)
    .map((v, i) => (i % 2 ? v.replace('<%=', '(_e(').replace('%>', '))') : templateToJS3(v)))
    .join('+');

const templateToJS = (content: string) =>
  content
    .split(/(<% .*?%>)/gs)
    .map((v, i) => (i % 2 ? v.replace('<%', '').replace('%>', ';') : `_w(${templateToJS2(v)});`))
    .join('');

export const template = (c: string) =>
  new Function(
    'p',
    `const _e=l=>(l+'').replaceAll("&","&amp;").replaceAll("<","&lt;").replaceAll(">","&gt;").replaceAll('"',"&quot;").replaceAll("'","&#039;");let _r='';const _w=x=>{_r+=x};${templateToJS(
      c,
    )};return _r;`,
  );

const sizes = ' KMGTP';

export const readableByteSize = (bytes: number) => {
  const ex = bytes ? Math.floor(Math.log(bytes) / Math.log(1024)) : 0;
  return `${(bytes / 1024 ** ex).toFixed(2) + sizes[ex]}B`;
};

export const simplifyPath = (path: string) =>
  `/${path
    .split('/')
    .filter((v) => v !== '.' && v !== '')
    .reduce((a, b) => (b === '..' ? a.pop() : a.push(b), a), [] as string[])
    .join('/')}`;

export const round = (n: number, d = 0) => {
  const m = 10 ** d;
  return Math.round(n * m) / m;
};

export const clamp = (v: number, min: number, max: number) => (v < min ? min : v > max ? max : v);
export const degToRad = (d: number) => (d * Math.PI) / 180;
export const radToDeg = (r: number) => (r * 180) / Math.PI;

export const hashMessage = async (message: string, algorithm = 'SHA-256') => {
  const msgUint8 = new TextEncoder().encode(message);
  const hashBuffer = await crypto.subtle.digest(algorithm, msgUint8);
  const hashHex = Array.from(new Uint8Array(hashBuffer))
    .map((b) => b.toString(16).padStart(2, '0'))
    .join('');
  return hashHex;
};

export const sha1 = (str: string) => hashMessage(str, 'SHA-1');
export const smartStringSorter = (key: string) => {
  const { compare } = new Intl.Collator(undefined, { numeric: true, sensitivity: 'base' });
  return <T>(a: T, b: T) => compare((a as any)[key], (b as any)[key]);
};

export const sha2 = async (str: string) => {
  const enc = new TextEncoder();
  const hash = await crypto.subtle.digest('SHA-1', enc.encode(str));
  return Array.from(new Uint8Array(hash))
    .map((v) => v.toString(16).padStart(2, '0'))
    .join('');
};

export const isElectron = !!(globalThis as any).require?.('process');

export const base64Url = (str: string) =>
  str.replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');

export const base64UrlDecode = (str: string) => {
  if (str.length % 4 !== 0) str += '==='.slice(0, 4 - (str.length % 4));
  return str.replace(/-/g, '+').replace(/_/g, '/');
};

export const codeToDataUrl = (code: string) => `data:application/ecmascript;base64,${btoa(code)}`;
export const importProxy = new Function('return import(arguments[0])');

export const checkNetworkStatus = () => {
  return merge(
    fromEvent(globalThis as any, 'online').pipe(map(() => true)),
    fromEvent(globalThis as any, 'offline').pipe(map(() => false)),
  ).pipe(startWith((globalThis as any).navigator.onLine));
};
