import { Injectable, NgZone, inject } from '@angular/core';
import type { MatSnackBarConfig } from '@angular/material/snack-bar';
import { MatSnackBar } from '@angular/material/snack-bar';
import { EnsureSingleInstance } from '../common/test-root-service';

export enum SnackStyle {
  ERROR = 'error',
  INFO = 'info',
  SUCCESS = 'success',
  WARN = 'warn',
  NORMAL = 'normal',
}

const defaultOptions = {
  action: 'Close',
  style: SnackStyle.NORMAL,
  duration: 3000,
  horizontalPosition: 'center',
  verticalPosition: 'top',
};

const panelClasses = {
  error: 'red-500',
  info: 'blue-grey-500',
  success: 'green-500',
  warn: 'yellow-500',
  normal: '',
};

@Injectable({ providedIn: 'root' })
export class SnackBarService {
  readonly #snackBar = inject(MatSnackBar);
  readonly #ngZone = inject(NgZone);

  constructor() {
    EnsureSingleInstance(this);
  }

  public normal(message: string, action?: string, options?: MatSnackBarConfig) {
    this.open(message, action, { ...options, style: SnackStyle.NORMAL });
  }

  public warn(message: string, action?: string, options?: MatSnackBarConfig) {
    this.open(message, action, { ...options, style: SnackStyle.WARN });
  }

  public info(message: string, action?: string, options?: MatSnackBarConfig) {
    this.open(message, action, { ...options, style: SnackStyle.INFO });
  }

  public error(message: string, action?: string, options?: MatSnackBarConfig) {
    this.open(message, action, { ...options, style: SnackStyle.ERROR });
  }

  public success(message: string, action?: string, options?: MatSnackBarConfig) {
    this.open(message, action, { ...options, style: SnackStyle.SUCCESS });
  }

  public open(
    message: string,
    action?: string,
    options?: { style?: SnackStyle } & MatSnackBarConfig<any>,
  ) {
    const opts: { style?: SnackStyle } & MatSnackBarConfig<any> = {
      ...defaultOptions,
      ...options,
    } as any;
    const { style, ...conf } = opts;
    conf.panelClass = style ? panelClasses[style] : '';
    this.show(message, action ?? undefined, conf);
  }

  private show(message: string, action: string | undefined, configuration: MatSnackBarConfig) {
    this.#ngZone.run(() => this.#snackBar.open(message, action ?? undefined, configuration));
  }
}
