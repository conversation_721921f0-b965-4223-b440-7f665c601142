import { EnsureSingleInstance } from '@lib/common/test-root-service';
import { html } from '@lib/web/v-html';
import nodemailer from 'nodemailer';
import { type Options as MailOptions } from 'nodemailer/lib/mailer';
import { getSES } from '../../environments/env-modules';
import { environment } from '../../environments/environment';
import { MailBase, MailLayout } from '../templates/jsx/mail.template';

export class MailBuilderService {
  private _layout = (prop: {
    headerContent: string;
    content: string;
    footer: string;
    headerUrl: string;
  }) => {
    return [
      `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">`,
      MailBase({ html: MailLayout(prop) as any, css: '' }),
      // MailBase(twindIfy(MailLayout(prop) as any)),
    ].join('');
  };

  private _layoutParam = { headerUrl: '', headerContent: '', content: '', footer: '' };

  constructor() {
    EnsureSingleInstance(this);
    this._layoutParam.headerContent = environment.appName;
    this._layoutParam.headerUrl = environment.appUrl;
    this._layoutParam.footer = `© ${new Date().getFullYear()} ${environment.appName}. All rights reserved.`;
  }

  public build(data: { content: string }, _options: { inlineCss?: boolean } = {}) {
    data.content = html([data.content] as any) as string;
    if (Array.isArray(data.content)) data.content = data.content.join('');
    return this._layout({ ...this._layoutParam, ...data });
  }
}

export class MailClientService {
  private _from = { address: '', name: '' };
  private _transport;

  constructor() {
    EnsureSingleInstance(this);
    this._transport =
      'SES' in environment.mailerConfig
        ? nodemailer.createTransport(getSES(environment.mailerConfig.SES))
        : nodemailer.createTransport(environment.mailerConfig);
    this._from.address = environment.mail.from.address;
    this._from.name = environment.mail.from.name;
  }

  public send(mailData: MailOptions) {
    const mailOptions: MailOptions = { from: mailData.from || this._from, ...mailData };
    return this._transport.sendMail(mailOptions);
  }
}
