import type { OnInit } from '@angular/core';
import {
  ChangeDetectionStrategy,
  Component,
  Injectable,
  inject,
  model,
  viewChild,
} from '@angular/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { lastValueFrom } from 'rxjs';
import { EnsureSingleInstance } from '../../../common/test-root-service';
import { TimePickerComponent } from './time-picker.component';

export interface ModalDataI {
  heading: string;
  btn1Name?: string;
  btn2Name?: string;
  value?: Date | null;
}

export interface SilverTimeResult_I {
  action: boolean;
  value: any;
}

@Injectable({ providedIn: 'root' })
export class SilverTimePickerModalService {
  readonly #dialog = inject(MatDialog);

  constructor() {
    EnsureSingleInstance(this);
  }

  public async open(data: ModalDataI, options = {}) {
    const dialogRef = this.#dialog.open(SilverSelectModalComponent, {
      width: '400px',
      data: { data },
      ...options,
    });
    return (await lastValueFrom(dialogRef.afterClosed())) as SilverTimeResult_I;
  }
}

@Component({
  imports: [MatDatepickerModule, MatDialogModule, TimePickerComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `<div mat-dialog-title class="!px-4 !pb-3">
      {{ modalData.heading }}
    </div>
    <mat-dialog-content class="mat-typography !p-0 !text-inherit">
      <mat-calendar class="w-[280px] m-auto" [(selected)]="selected" />
      <div class="h-2"></div>
      <app-time-picker />
    </mat-dialog-content>
    <mat-dialog-actions align="end">
      <button class="btn btn-outline btn-error mr-3 btn-sm" [mat-dialog-close]="false" color="warn">
        {{ modalData.btn1Name }}
      </button>
      <button class="btn btn-outline btn-success btn-sm" color="accent" (click)="submit()">
        {{ modalData.btn2Name }}
      </button>
    </mat-dialog-actions>`,
})
export class SilverSelectModalComponent implements OnInit {
  readonly #dialogRef: MatDialogRef<SilverSelectModalComponent> = inject(MatDialogRef);
  readonly #data: { data: ModalDataI; ref: any } = inject(MAT_DIALOG_DATA);
  readonly timePicker = viewChild.required(TimePickerComponent);
  selected = model<Date | null>(new Date());

  public modalData!: ModalDataI;
  public value!: any[];

  public ngOnInit(): void {
    this.modalData = this.#data.data;
    const value = this.modalData.value;
    if (value) {
      this.selected.set(value);
      this.timePicker().setTime(value);
    }
    this.modalData.heading ??= 'Pick Time';
    this.modalData.btn1Name ??= 'Cancel';
    this.modalData.btn2Name ??= 'Confirm';
  }

  public submit() {
    try {
      const date = this.selected()!;
      const time = this.timePicker().getTime() as [number, number, string];
      const [, minute, period] = time;
      let hour = time[0];
      if (period === 'PM' && hour !== 12) hour += 12;
      else if (period === 'AM' && hour === 12) hour = 0;
      date.setHours(hour, minute, 0);
      const res = { action: true, value: date };
      this.#dialogRef.close(res);
    } catch {
      this.#dialogRef.close({ action: false, value: null });
    }
  }
}
