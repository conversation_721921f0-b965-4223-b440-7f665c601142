const express = require('express');
const bodyParser = require('body-parser'); // Optional (can use built-in)
const path = require('path');
const dotenv = require('dotenv');

dotenv.config();
 
const app = express();
const PORT = process.env.PORT || 3000;

// Import database initialization
const initializeAuthDatabase = require('../config/initAuthDB');
const authRoutes = require('../routes/auth_routes');
const userRoutes = require('../routes/user_routes');

// --- Body Parsing (Choose one) ---
// Option 1: Using body-parser (works)
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Option 2: Use built-in Express (preferred in newer apps)
// app.use(express.json());
// app.use(express.urlencoded({ extended: true }));

// --- Static and Views ---
app.use(express.static(path.join(__dirname, 'src/public'))); // corrected path
app.set('views', path.join(__dirname, 'src/templates/views')); // corrected path
app.set('view engine', 'ejs');

app.use('/auth', authRoutes);
app.use('/user', userRoutes);


// --- 404 Handler ---
app.use((req, res) => {
    res.status(404).json({ message: 'This URL does not exist' });
});

// --- Start Server ---
async function startServer() {
    try {
        // Initialize authentication database and tables
        await initializeAuthDatabase();
        
        // Start the server
        app.listen(PORT, () => {
            console.log(`✅ Server is running on port ${PORT}`);
            console.log(`🌐 http://localhost:${PORT}`);
        });
    } catch (error) {
        console.error('❌ Failed to start server:', error.message);
        process.exit(1);
    }
}

startServer();
