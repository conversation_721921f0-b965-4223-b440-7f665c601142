import { Location } from '@angular/common';
import type { <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { Component, inject } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { apiRPC, injectController } from '@api/rpc';
import type { ActiveUser } from '@lib/angular/auth/models/active-user.model';
import { ProfileFormModalService } from '@lib/angular/dynamic-forms/profile-form-modal.service';
import type { SilverField } from '@lib/angular/dynamic-forms/silver-field.component';
import { SilverFieldTypes } from '@lib/angular/dynamic-forms/silver-field.component';
import { FileDownloaderService } from '@lib/angular/file-downloader.service';
import type { URLResolver } from '@lib/angular/file-uploader.service';
import { FileUploaderService } from '@lib/angular/file-uploader.service';
import { SnackBarService } from '@lib/angular/snack-bar.service';
import { THEME_CONTROLLER } from '@lib/angular/theming.service';
import { getFileMeta } from '@lib/web/file.fun';
import type { Subscription } from 'rxjs';
import { firstValueFrom } from 'rxjs';
import { AppCommonModule } from '../../../common/common.module';
import { AuthService } from '../../../services/auth.service';
import { SideNavService } from '../../../services/design/sidenav.service';

@Component({
  imports: [MatIconModule, AppCommonModule],
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
})
export class HeaderComponent implements OnInit, OnDestroy {
  readonly #authService = inject(AuthService);
  readonly #fileUploaderService = inject(FileUploaderService);
  readonly #location = inject(Location);
  readonly #sideNavService = inject(SideNavService);
  readonly #snackBarService = inject(SnackBarService);
  readonly fileDownloaderService = inject(FileDownloaderService);
  readonly profileFormModalService = inject(ProfileFormModalService);

  readonly #authController = injectController(apiRPC.AuthController);

  public loginUser: ActiveUser;
  public theme = 'dark';

  #themeSub: Subscription;

  async ngOnInit(): Promise<void> {
    this.loginUser = await this.#authService.getActiveUser();
    this.#themeSub = THEME_CONTROLLER.subscribe((event: string) => (this.theme = event));
  }

  back() {
    this.#location.back();
  }

  async toggleSideBar() {
    const data = await firstValueFrom(this.#sideNavService.sideNav);
    this.#sideNavService.sideNav.next({ open: !data.open });
  }

  async toggleWidgetsPanel() {
    const data = await firstValueFrom(this.#sideNavService.widgetPanel);
    this.#sideNavService.widgetPanel.next({ open: !data.open });
  }

  public logout() {
    this.#authService.logout();
  }

  public setTheme(theme: string) {
    THEME_CONTROLLER.next(theme);
  }

  public updateUser(user) {
    const form: SilverField[] = [
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Name',
        placeholder: 'Enter Name',
        type: 'text',
        key: 'name',
        value: user.name,
        valid: { required: true },
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Email',
        placeholder: 'Enter Email',
        type: 'text',
        key: 'email',
        value: user.email,
        valid: { required: true, email: true },
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Password',
        placeholder: 'Enter Password',
        type: 'password',
        key: 'password',
        value: '',
        cssClass: 'mb-3',
        valid: { password: true },
      },
    ];
    const result = this.profileFormModalService.openForm({
      heading: 'Update',
      autoImageField: 'name',
      form,
      imageUrl: user.pic,
      closeOnSubmit: false,
      imgOptions: { resize: [144, 144], exportTo: 'image/png', forceSize: false },
    });
    result.onSubmit$.subscribe({
      next: async (data) => {
        const done = await this.updateProfile(user.id, data);
        if (!done) return;
        result.close();
        this.loginUser = await this.#authService.getActiveUser(true);
      },
    });
  }

  public async updateProfile(userId: number, data: { value: any; image: File }) {
    try {
      await this.#authController.updateUser({ id: userId }, data.value);
      const filesToUpload: URLResolver[] = [];
      if (data.image) filesToUpload.push(() => this.getThumbUrl(userId, data.image));
      if (filesToUpload.length > 0) await this.#fileUploaderService.uploadFiles(filesToUpload);
      this.#snackBarService.success('Profile Updated');
      return true;
    } catch (error) {
      this.#snackBarService.error(error.error.message);
      return false;
    }
  }

  private async getThumbUrl(id: number, file: File) {
    const result = await this.#authController.setThumbnail({ id }, { file: getFileMeta(file) });
    return [{ url: result.data.preUrl }, file] as [any, File];
  }

  public ngOnDestroy(): void {
    this.#themeSub?.unsubscribe();
    this.#themeSub = null;
  }
}
