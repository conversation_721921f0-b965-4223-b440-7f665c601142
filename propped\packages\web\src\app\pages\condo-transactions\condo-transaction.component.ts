import { CommonModule } from '@angular/common';
import { Component, type OnInit, inject } from '@angular/core';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatSliderModule } from '@angular/material/slider';
import { apiRPC, injectController } from '@api/rpc';
import { FormModalService } from '@lib/angular/dynamic-forms/form-modal.service';
import {
  type SilverField,
  SilverFieldTypes,
} from '@lib/angular/dynamic-forms/silver-field.component';
import { SnackBarService } from '@lib/angular/snack-bar.service';
import { SVGIconComponent } from '@lib/angular/svg-icon.component';
import { html } from '@lib/common/jsx-dom';
import { LngLatBounds, type Map as mapBoxMap } from 'mapbox-gl';
import {
  GeoJSONSourceComponent,
  LayerComponent,
  MapComponent,
  PopupComponent,
} from 'ngx-mapbox-gl';
import { ChartViewerService } from '../../component/data-chart/data-chart.component';
import { StackedChartComponent } from '../../component/data-chart/stacked-chart.component';
import { MapsService } from '../../services/maps.service';
import { CondoSalesService } from './condo-transaction.service';

@Component({
  selector: 'app-condo-sales',
  templateUrl: './condo-transaction.component.html',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatSliderModule,
    MatIconModule,
    ReactiveFormsModule,
    SVGIconComponent,
    StackedChartComponent,
    MapComponent,
    GeoJSONSourceComponent,
    LayerComponent,
    PopupComponent,
  ],
})
export class CondoTransactionComponent implements OnInit {
  readonly #condoSalesService = inject(CondoSalesService);
  readonly #formModalService = inject(FormModalService);
  readonly #mapsService = inject(MapsService);
  readonly #condoController = injectController(apiRPC.CondoController);
  readonly #chartViewerService = inject(ChartViewerService);
  readonly #snackBarService = inject(SnackBarService);
  pageChange = false;

  filter = {} as any;
  config = { page: 1, limit: 10, sort: 'contract_date', order: 'DESC', lastId: 0 };
  db: any;
  headers = [];
  data = [];
  meta: any = {};
  sortColumn = 'contract_date';
  sortOrder = 'DESC';
  showChartViewer = false;
  growthPercentage = new FormControl(0);
  growthYears = new FormControl(0);
  forecastPrice = 0;
  forecastPSF = 0;
  medianChartData: any = null;
  psfChartData: any = null;
  loading = true;
  statsLoading = true;
  initialLoad = true;
  showBookmarkedChartViewer = false;
  showBookmarkedChart = false;
  bookmarkedPriceChartData: any = null;
  bookmarkedPsfChartData: any = null;

  // Map related properties
  showMap = false;
  showBookmarkedOnMap = false;
  private googleMap: google.maps.Map | null = null;
  private mapElement: HTMLDivElement = html`<div style="width: 100%; height: 100%;"></div>`;
  private markers: google.maps.Marker[] = [];
  singaporeCenter = { lat: 1.3521, lng: 103.8198 }; // Singapore center coordinates
  lat = 1.3521;
  lng = 103.8198;
  // Property popup related properties
  showPropertyInformation = false;
  selectedMarkerCoordinates: [number, number] = [0, 0];
  selectedProperty: {
    projectName: string;
    address: string;
    district: string;
    propertyType: string;
    price: string;
    psf: string;
    rowId: number;
  } | null = null;
  private selectedRowId: number | null = null;
  private mapBoxMap?: mapBoxMap;
  condoMapMarkers = {
    type: 'FeatureCollection',
    features: [],
  };
  bookmarkMapMarkers = {
    type: 'FeatureCollection',
    features: [],
  };
  style = 'mapbox://styles/mapbox/streets-v12';
  zoom = 12;

  // Columns that can be sorted
  sortableColumns = ['contract_date', 'price', 'psf', 'street_name', 'area_sqm'];

  // Column definitions
  columnDefinitions = [
    {
      title: 'Txn Date',
      field: 'contract_date',
      index: 1,
      transform: (value: string) => this.parseToDate(value),
    },
    { title: 'Project Name', field: 'project_name', index: 2 },
    { title: 'Street', field: 'street_name', index: 3 },
    { title: 'District', field: 'district', index: 4 },
    { title: 'Floor Level', field: 'floor_range', index: 5 },
    {
      title: 'Area (sqft)',
      field: 'area_sqm',
      index: 6,
      transform: (value) => Math.round(value * 10.73692),
    },
    {
      title: 'Price',
      field: 'price',
      index: 7,
      transform: (value) => `$${value.toLocaleString()}`,
    },
    {
      title: 'PSF',
      field: 'psf',
      index: 8,
      transform: (value) => `$${Math.round(value).toLocaleString()}`,
    },
    { title: 'Tenure', field: 'tenure', index: 9 },
  ];
  quarterlyMedianPrices: any[] = [];
  // Bookmarked rows
  bookmarkedRows: any[] = [];
  // Transaction statistics
  transactionCount = 0;
  priceStats = {
    highest: 0,
    median: 0,
    lowest: 0,
  };
  psfStats = {
    highest: 0,
    median: 0,
    lowest: 0,
  };
  private allFilteredData: any[] = [];
  monthlyMedianPrices: any[] = [];
  monthlyPSFValues: any[] = [];

  async ngOnInit(): Promise<void> {
    this.db = await this.#condoSalesService.loadDB();
    await this.getData();
    // Load Google Maps API
    await this.#mapsService.loadGoogleMapsApi();
    this.growthPercentage.valueChanges.subscribe(() => this.calculateForecast());
    this.growthYears.valueChanges.subscribe(() => this.calculateForecast());
    this.generateMonthlyMedianPrices();
    this.generateMonthlyPSFValues();
    this.loading = false;

    setTimeout(() => {
      this.loadStatsData();
    }, 100);
  }

  parseToDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', { year: 'numeric', month: '2-digit' });
  }

  openMedianPriceChart() {
    this.generateMonthlyMedianPrices();

    // Create chart data with or without bookmarked transactions
    const chartData = {
      heading: 'Monthly Median Price Trends',
      header: ['Date', 'Median Price'],
      data: this.monthlyMedianPrices,
      extraPoint:
        this.showBookmarkedChartViewer && this.bookmarkedRows.length > 0
          ? {
              label: 'Bookmarked Transactions',
              values: this.bookmarkedRows.map((row) => Number(row[7])),
              labels: this.bookmarkedRows.map((row) => {
                // Extract date from row[3] which contains the transaction date
                const dateStr = row[1];
                if (!dateStr) return '';

                // Extract year and month from the date string (format: YYYY-MM-DD)
                const [year, month] = dateStr.split('-');
                const date = new Date(Number(year), Number(month) - 1);
                return date.toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'short',
                });
              }),
            }
          : undefined,
    };

    this.#chartViewerService.open(chartData);
  }

  openPSFChart() {
    this.generateMonthlyPSFValues();

    // Create chart data with or without bookmarked transactions
    const chartData = {
      heading: 'Monthly Average PSF Trends',
      header: ['Date', 'Average PSF'],
      data: this.monthlyPSFValues,
      extraPoint:
        this.showBookmarkedChartViewer && this.bookmarkedRows.length > 0
          ? {
              label: 'Bookmarked Transactions',
              values: this.bookmarkedRows.map((row) => Number(row[8])),
              labels: this.bookmarkedRows.map((row) => {
                // Extract date from row[3] which contains the transaction date
                const dateStr = row[1];
                if (!dateStr) return '';

                // Extract year and month from the date string (format: YYYY-MM-DD)
                const [year, month] = dateStr.split('-');
                const date = new Date(Number(year), Number(month) - 1);
                return date.toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'short',
                });
              }),
            }
          : undefined,
    };

    this.#chartViewerService.open(chartData);
  }

  private generateMonthlyPSFValues() {
    console.log('All filtered data', this.allFilteredData);

    if (this.allFilteredData.length === 0) return;

    // Group transactions by year and month
    const psfByYearMonth = new Map();

    this.allFilteredData.forEach((row) => {
      // Date is in format "YYYY-MM-DD" at index 2
      const dateStr = row[2];
      if (!dateStr) return;

      // Extract year and month from the date string
      const [year, month] = dateStr.split('-');
      const yearMonth = `${year}-${month}`;

      // PSF is at index 1
      const psf = row[1];

      if (!psfByYearMonth.has(yearMonth)) {
        psfByYearMonth.set(yearMonth, []);
      }

      psfByYearMonth.get(yearMonth).push(psf);
    });

    // Calculate average PSF for each month and sort chronologically
    const averagePSFValues = Array.from(psfByYearMonth.entries())
      .map(([yearMonth, psfValues]) => {
        // Calculate average PSF
        const sum = psfValues.reduce((acc, val) => acc + val, 0);
        const average = Math.round(sum / psfValues.length);

        // Format the date for display (convert YYYY-MM to MMM YYYY)
        const [year, month] = yearMonth.split('-');
        const date = new Date(Number(year), Number(month) - 1);
        const formattedDate = date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
        });

        return [formattedDate, average];
      })
      .sort((a, b) => {
        const dateA = new Date(a[0]);
        const dateB = new Date(b[0]);
        return dateA.getTime() - dateB.getTime();
      });

    this.monthlyPSFValues = averagePSFValues;
    this.psfChartData = {
      heading: 'Monthly Average PSF Trends',
      header: ['Date', 'Average PSF'],
      data: this.showChartViewer ? this.monthlyPSFValues : undefined,
      extraPoint:
        this.showBookmarkedChartViewer && this.bookmarkedRows.length > 0
          ? {
              label: 'Bookmarked Transactions',
              values: this.bookmarkedRows.map((row) => Number(row[8])),
              labels: this.bookmarkedRows.map((row) => {
                // Extract date from row[3] which contains the transaction date
                const dateStr = row[1];
                if (!dateStr) return '';

                // Extract year and month from the date string (format: YYYY-MM-DD)
                const [year, month] = dateStr.split('-');
                const date = new Date(Number(year), Number(month) - 1);
                return date.toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'short',
                });
              }),
            }
          : undefined,
    };

    console.log('Monthly average PSF values', this.monthlyPSFValues);
  }

  private generateMonthlyMedianPrices() {
    console.log('All filtered data', this.allFilteredData);

    if (this.allFilteredData.length === 0) return;

    // Group transactions by year and month
    const pricesByYearMonth = new Map();

    this.allFilteredData.forEach((row) => {
      // Date is in format "YYYY-MM-DD" at index 2
      const dateStr = row[2];
      if (!dateStr) return;

      // Extract year and month from the date string
      const [year, month] = dateStr.split('-');
      const yearMonth = `${year}-${month}`;

      // Price is at index 0
      const price = row[0];

      if (!pricesByYearMonth.has(yearMonth)) {
        pricesByYearMonth.set(yearMonth, []);
      }

      pricesByYearMonth.get(yearMonth).push(price);
    });

    // Calculate median for each month and sort chronologically
    const medianPrices = Array.from(pricesByYearMonth.entries())
      .map(([yearMonth, prices]) => {
        const sortedPrices = [...prices].sort((a, b) => a - b);
        const median = this.calculateMedian(sortedPrices);

        // Format the date for display (convert YYYY-MM to MMM YYYY)
        const [year, month] = yearMonth.split('-');
        const date = new Date(Number(year), Number(month) - 1);
        const formattedDate = date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
        });

        return [formattedDate, median];
      })
      .sort((a, b) => {
        const dateA = new Date(a[0]);
        const dateB = new Date(b[0]);
        return dateA.getTime() - dateB.getTime();
      });

    this.monthlyMedianPrices = medianPrices;
    this.medianChartData = {
      heading: 'Monthly Median Price',
      header: ['Date', 'Median Price'],
      data: this.showChartViewer ? this.monthlyMedianPrices : undefined,
      extraPoint:
        this.showBookmarkedChartViewer && this.bookmarkedRows.length > 0
          ? {
              label: 'Bookmarked Transactions',
              values: this.bookmarkedRows.map((row) => Number(row[7])),
              labels: this.bookmarkedRows.map((row) => {
                // Extract date from row[3] which contains the transaction date
                const dateStr = row[1];
                if (!dateStr) return '';

                // Extract year and month from the date string (format: YYYY-MM-DD)
                const [year, month] = dateStr.split('-');
                const date = new Date(Number(year), Number(month) - 1);
                return date.toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'short',
                });
              }),
            }
          : undefined,
    };
    console.log('Monthly median prices', this.monthlyMedianPrices);
  }

  toggleChartViewer(): void {
    this.showChartViewer = !this.showChartViewer;

    // Generate chart data if needed
    if (this.showChartViewer) {
      this.generateMonthlyMedianPrices();
      this.generateMonthlyPSFValues();
    }
  }

  toggleBookmarkedChartViewer(): void {
    this.showBookmarkedChartViewer = !this.showBookmarkedChartViewer;

    // Generate chart data if needed
    if (this.showBookmarkedChartViewer) {
      this.generateMonthlyMedianPrices();
      this.generateMonthlyPSFValues();

      // Show the chart viewer if it's not already visible
      this.showChartViewer = true;
    } else {
      // Regenerate charts without bookmarked data
      this.generateMonthlyMedianPrices();
      this.generateMonthlyPSFValues();
    }
  }

  calculateForecast(): void {
    if (this.growthPercentage.value <= 0 || this.growthYears.value <= 0) return;
    // Get the latest median price from statistics
    const latestPrice = this.monthlyMedianPrices.at(this.monthlyMedianPrices.length - 1)?.[1];
    const latestPSF = this.monthlyPSFValues.at(this.monthlyPSFValues.length - 1)?.[1];

    if (!latestPrice || !latestPSF) {
      console.warn('No price data available for forecast calculation');
      return;
    }

    // Get values from form controls
    const growthRate = this.growthPercentage.value || 6;
    const years = this.growthYears.value || 3;

    // Apply the formula: ((1+(growth_percentage/100))^number_of_years)*latest price
    const growthFactor = Math.pow(1 + growthRate / 100, years);

    // Calculate forecast price and PSF
    this.forecastPrice = Math.round(growthFactor * latestPrice);
    this.forecastPSF = Math.round(growthFactor * latestPSF);

    this.medianChartData = {
      heading: 'Monthly Median Price Trends',
      header: ['Date', 'Median Price'],
      data: [...this.monthlyMedianPrices, ['Forecast Price', this.forecastPrice]],
    };

    this.psfChartData = {
      heading: 'Monthly Median PSF Trends',
      header: ['Date', 'Median PSF'],
      data: [...this.monthlyPSFValues, ['Forecast PSF', this.forecastPSF]],
    };

    console.log(
      `Forecast calculation: $${latestPrice.toLocaleString()} × (1 + ${growthRate}%)^${years} = $${this.forecastPrice.toLocaleString()}`,
    );
  }

  private async loadStatsData(): Promise<void> {
    this.statsLoading = true;
    try {
      await this.getStatsData();
      this.initialLoad = false;
    } catch (error) {
      console.error('Error loading stats data:', error);
      this.#snackBarService.error('Failed to load statistics data');
    } finally {
      this.statsLoading = false;
    }
  }

  async getData() {
    const res = await this.#condoSalesService.generateSqlQuery(this.db, this.filter, this.config);
    // console.log("QUERY DATA",res);
    console.log('FILTER BY:', this.filter);
    this.headers = res.headers;
    this.data = res.data;
    this.meta = res.meta;
    this.config.lastId = this.data.at(-1)?.[0] || 0;

    if (!this.pageChange && !this.initialLoad) {
      await this.loadStatsData();
      // this.calculateTransactionStats();
    }
    this.pageChange = false;

    // Refresh map markers if map is visible
    if (this.showMap) {
      setTimeout(async () => {
        // await this.addCondoMarkers();
        await this.addCondoMapBoxMarkers();
      }, 100);
    }
  }

  /**
   * Sort data by column
   * @param column Column to sort by
   */
  sortByColumn(column: string) {
    // Check if column is sortable
    if (!this.sortableColumns.includes(column)) {
      return;
    }

    // Toggle sort order if already sorting by this column
    if (this.config.sort === column) {
      this.config.order = this.config.order === 'ASC' ? 'DESC' : 'ASC';
    } else {
      this.config.sort = column;
      this.config.order = 'DESC'; // Default to descending
    }

    this.sortColumn = this.config.sort;
    this.sortOrder = this.config.order;

    // Reset pagination
    this.config.page = 1;
    this.config.lastId = 0;

    // Fetch data with new sorting
    this.getData();
  }
  private async getStatsData(): Promise<void> {
    try {
      // Get all data matching the current filters for accurate statistics
      const statsData = await this.#condoSalesService.getAllFilteredDataForStats(
        this.db,
        this.filter,
      );
      const { data } = this.#condoSalesService.getStatsQuery(this.db, this.filter);

      this.priceStats = { highest: data[0], lowest: data[1], median: data[4] };
      this.psfStats = { highest: data[2], lowest: data[3], median: data[5] };

      // Check if data is available
      if (statsData && statsData.data) {
        this.allFilteredData = statsData.data;
        this.generateMonthlyMedianPrices();
        this.generateMonthlyPSFValues();
        // Update transaction count from the total filtered data
        this.transactionCount = statsData.count || 0;
      } else {
        console.warn('No stats data available');
        this.allFilteredData = [];
        this.transactionCount = 0;
      }
    } catch (error) {
      console.error('Error getting stats data:', error);
      this.allFilteredData = [];
      this.transactionCount = 0;
    }
  }

  /**
   * Get the current sort direction for a column
   * @param column Column name
   * @returns Sort direction: 'asc', 'desc', or null if not sorted
   */
  getSortDirection(column: string): 'asc' | 'desc' | null {
    if (this.sortColumn === column) {
      return this.sortOrder === 'ASC' ? 'asc' : 'desc';
    }
    return null;
  }

  /**
   * Check if a column is sortable
   * @param column Column name
   * @returns True if the column is sortable
   */
  isSortableColumn(column: string): boolean {
    return this.sortableColumns.includes(column);
  }

  previous() {
    if (this.config.page === 1) return;
    this.config.page = this.config.page - 1;
    this.pageChange = true;
    this.getData();
  }

  next() {
    const totalPages = Math.ceil(this.meta.total / this.config.limit);
    if (this.config.page === totalPages) return;
    this.config.page = this.config.page + 1;
    this.pageChange = true;
    this.getData();
  }
  onMapLoad(map: mapBoxMap) {
    this.mapBoxMap = map;
    console.log('MAP LOADED', this.mapBoxMap);
    if (this.data && this.data.length > 0) {
      this.addCondoMapBoxMarkers();
    }
  }
  /**
   * Toggle map visibility
   */
  toggleMap() {
    this.showMap = !this.showMap;

    if (this.showMap) {
      // Initialize the map when it's first shown
      setTimeout(async () => {
        if (!this.googleMap) {
          this.initializeMap();
        }
        // Always refresh markers when showing the map to ensure latest data is displayed
        // await this.addCondoMarkers();
        await this.addCondoMapBoxMarkers();
      }, 100);
    }
  }
  fitMapToMarkers(coordsArray: number[][]): void {
    const bounds = new LngLatBounds();

    coordsArray.forEach((coord) => {
      bounds.extend(coord as [number, number]);
    });

    const center = bounds.getCenter();
    this.lng = center.lng;
    this.lat = center.lat;
    this.zoom = 12;
  }

  /**
   * Toggle showing bookmarked items on map with different color
   */
  toggleBookmarkedOnMap(): void {
    this.showBookmarkedOnMap = !this.showBookmarkedOnMap;

    // If map is visible, refresh markers to show the new colors
    // if (this.showMap && this.googleMap) {
    //   this.addCondoMarkers();
    // }
    if (!this.showBookmarkedOnMap) {
      this.bookmarkMapMarkers.features = [];
    }
    if (this.showMap) {
      this.addCondoMapBoxMarkers();
    }
  }

  private calculateMedian(sortedArray: number[]): number {
    if (sortedArray.length === 0) return 0;

    const mid = Math.floor(sortedArray.length / 2);

    if (sortedArray.length % 2 === 0) {
      // Even number of elements, average the middle two
      return (sortedArray[mid - 1] + sortedArray[mid]) / 2;
    } else {
      // Odd number of elements, return the middle one
      return sortedArray[mid];
    }
  }

  private calculateTransactionStats(): void {
    if (!this.data || this.data.length === 0) return;

    this.transactionCount = this.meta.total;

    // Extract price and PSF values
    const prices = this.data.map((row) => Number(row[7])).filter((price) => !isNaN(price));
    const psfValues = this.data.map((row) => Number(row[8])).filter((psf) => !isNaN(psf));

    // Sort values for calculations
    const sortedPrices = [...prices].sort((a, b) => a - b);
    const sortedPsf = [...psfValues].sort((a, b) => a - b);

    // Calculate price statistics
    this.priceStats = {
      highest: Math.max(...prices),
      median: this.calculateMedian(sortedPrices),
      lowest: Math.min(...prices),
    };

    // Calculate PSF statistics
    this.psfStats = {
      highest: Math.max(...psfValues),
      median: this.calculateMedian(sortedPsf),
      lowest: Math.min(...psfValues),
    };
  }

  /**
   * Check if a row should be highlighted (pink background)
   * Only highlight when the row is selected AND the map is open
   */
  isRowHighlighted(row: any): boolean {
    // Only highlight the row if it's selected AND the map is open
    return row[0] === this.selectedRowId && this.showMap;
  }

  selectRow(row: any) {
    // Toggle selection - if same row is clicked, deselect it
    this.selectedRowId = this.selectedRowId === row[0] ? null : row[0];
    // if (this.showMap && this.googleMap) {
    //   this.updateMarkerColors();
    // }
    if (this.showMap) {
      this.updateMapMarkerColors();
    }
  }
  updateMapMarkerColors() {
    if (!this.mapBoxMap) return;

    // Update regular markers
    const updatedFeatures = this.condoMapMarkers.features.map((feature) => {
      const featureId = feature.properties.id;
      const isBookmarked = feature.properties.isBookmarked;

      // If this feature's ID matches the selected row ID, make it purple
      if (featureId === this.selectedRowId) {
        return {
          ...feature,
          properties: {
            ...feature.properties,
            color: '#9c27b0', // Purple for selected
            isSelected: true,
          },
        };
      } else if (isBookmarked && !this.showBookmarkedOnMap) {
        // If it's bookmarked but not selected, keep it red
        return {
          ...feature,
          properties: {
            ...feature.properties,
            color: '#e53935', // Red for bookmarked
            isSelected: false,
          },
        };
      } else {
        // Regular marker, not selected, not bookmarked
        return {
          ...feature,
          properties: {
            ...feature.properties,
            color: '#008000', // Green for regular
            isSelected: false,
          },
        };
      }
    });

    // Update the markers collection with the new colors
    this.condoMapMarkers = {
      type: 'FeatureCollection',
      features: updatedFeatures,
    };

    // If showing bookmarked markers separately, update those too
    if (this.showBookmarkedOnMap && this.bookmarkMapMarkers.features.length > 0) {
      const updatedBookmarkFeatures = this.bookmarkMapMarkers.features.map((feature) => {
        const featureId = feature.properties.id;

        // If this feature's ID matches the selected row ID, make it purple
        if (featureId === this.selectedRowId) {
          return {
            ...feature,
            properties: {
              ...feature.properties,
              color: '#9c27b0', // Purple for selected
              isSelected: true,
            },
          };
        } else {
          // Bookmarked but not selected
          return {
            ...feature,
            properties: {
              ...feature.properties,
              color: '#e53935', // Red for bookmarked
              isSelected: false,
            },
          };
        }
      });

      // Update the bookmarked markers collection
      this.bookmarkMapMarkers = {
        type: 'FeatureCollection',
        features: updatedBookmarkFeatures,
      };
    }
  }
  private updateMarkerColors(): void {
    if (!this.markers || !this.data) return;

    this.markers.forEach((marker, index) => {
      const row = this.data[index];
      if (!row) return;

      const isRowBookmarked = this.isBookmarked(row);
      const isSelected = row[0] === this.selectedRowId;

      let icon;

      if (isRowBookmarked) {
        // Red diamond for bookmarked transactions
        icon = {
          path: 'M 0 -10 L 10 0 L 0 10 L -10 0 Z',
          fillColor: '#FF0000',
          fillOpacity: 1,
          strokeWeight: 1,
          strokeColor: '#FFFFFF',
          scale: 1,
        };
      } else if (isSelected) {
        // Purple square for selected transactions
        icon = {
          path: 'M -10 -10 L 10 -10 L 10 10 L -10 10 Z',
          fillColor: '#800080', // Purple
          fillOpacity: 1,
          strokeWeight: 1,
          strokeColor: '#FFFFFF',
          scale: 1,
        };
      } else {
        // Green square for regular transactions
        icon = {
          path: 'M -10 -10 L 10 -10 L 10 10 L -10 10 Z',
          fillColor: '#008000',
          fillOpacity: 1,
          strokeWeight: 1,
          strokeColor: '#FFFFFF',
          scale: 1,
        };
      }

      marker.setIcon(icon);
    });
  }

  /**
   * Show property information popup when a marker is clicked
   */
  showPropertyInfo(event: any): void {
    // Get the clicked feature
    const feature = event.features[0];
    if (!feature) return;

    // Get the feature properties and coordinates
    const properties = feature.properties;
    const coordinates = feature.geometry.coordinates.slice() as [number, number];

    // Find the corresponding data row
    const rowId = properties.id;
    const row =
      this.data.find((r) => r[0] === rowId) || this.bookmarkedRows.find((r) => r[0] === rowId);

    if (!row) return;

    // Set the selected property information
    this.selectedProperty = {
      projectName: row[2], // Project name
      address: row[3], // Address
      district: row[4], // District
      propertyType: row[5], // Property type
      price: Number(row[7]).toLocaleString(), // Price
      psf: row[8], // PSF
      rowId: row[0], // Row ID
    };

    // Set the coordinates and show the popup
    this.selectedMarkerCoordinates = coordinates;
    this.showPropertyInformation = true;

    // Highlight the selected row in the table
    this.selectRow(row);
  }

  private initializeMap(): void {
    if (!google || !google.maps) {
      console.error('Google Maps API not loaded');
      return;
    }

    const { Map: GoogleMap, MapTypeId } = google.maps;

    try {
      this.googleMap = new GoogleMap(this.mapElement, {
        zoom: 11,
        center: this.singaporeCenter,
        mapTypeId: MapTypeId.ROADMAP,
      });
      console.log('Map initialized successfully');
    } catch (error) {
      console.error('Error initializing map:', error);
    }
  }

  private clearMarkers(): void {
    if (this.markers.length > 0) {
      this.markers.forEach((marker) => marker.setMap(null));
      this.markers = [];
    }
  }

  /**
   * Add markers for Condo transactions
   */
  private async addCondoMarkers(): Promise<void> {
    if (!this.googleMap || !this.data || this.data.length === 0) return;

    // Clear existing markers
    this.clearMarkers();

    try {
      // Prepare transaction data for API call
      const transactions = this.data.map((row) => ({
        projectName: row[2], // Project Name
        address: row[3], // Address
        district: row[4], // District
      }));

      // Get coordinates from API using the controller directly
      const response = await this.#condoController.getCondoCoordinates({ transactions });

      if (response.success && response.locations.length > 0) {
        const { Marker, InfoWindow } = google.maps;
        const bounds = new google.maps.LatLngBounds();
        console.log('RESPONSE', response);
        // Add a marker for each location
        response.locations.forEach((location, index) => {
          const position = {
            lat: location.latitude,
            lng: location.longitude,
          };

          // Get the corresponding data row
          const row =
            this.data.find((r) => r[2] === location.project_name && r[3] === location.address) ||
            this.data[index];

          const isRowBookmarked = this.isBookmarked(row);
          const isSelected = row[0] === this.selectedRowId;

          let markerIcon;

          if (isRowBookmarked) {
            // Red diamond for bookmarked transactions
            markerIcon = {
              path: 'M 0 -10 L 10 0 L 0 10 L -10 0 Z',
              fillColor: '#FF0000',
              fillOpacity: 1,
              strokeWeight: 1,
              strokeColor: '#FFFFFF',
              scale: 1,
            };
          } else if (isSelected) {
            // Purple square for selected transactions
            markerIcon = {
              path: 'M -10 -10 L 10 -10 L 10 10 L -10 10 Z',
              fillColor: '#800080', // Purple
              fillOpacity: 1,
              strokeWeight: 1,
              strokeColor: '#FFFFFF',
              scale: 1,
            };
          } else {
            // Green square for regular transactions
            markerIcon = {
              path: 'M -10 -10 L 10 -10 L 10 10 L -10 10 Z',
              fillColor: '#008000',
              fillOpacity: 1,
              strokeWeight: 1,
              strokeColor: '#FFFFFF',
              scale: 1,
            };
          }
          // Create marker
          const marker = new Marker({
            position,
            map: this.googleMap,
            title: `${location.block_no} ${location.street_name}`,
            animation: google.maps.Animation.DROP,
            icon: markerIcon,
          });

          // Add info window
          const infoWindow = new InfoWindow({
            content: `
              <div style="padding: 10px; max-width: 300px;">
                <h3 style="font-weight: bold; margin-bottom: 5px;">${location.project_name}</h3>
                <p style="margin-bottom: 5px;">${location.address}</p>
                <p style="margin-bottom: 5px;">District: ${row[4] || 'N/A'}</p>
                <p style="margin-bottom: 5px;">Floor: ${row[5] || 'N/A'}</p>
                <p style="margin-bottom: 5px;">Area: ${Math.round(row[6] * 10.73692) || 'N/A'} sqft</p>
                <p style="margin-bottom: 5px;">Price: $${row[7]?.toLocaleString() || 'N/A'}</p>
                <p style="margin-bottom: 0; font-size: 0.8em;">${location.full_address || ''}</p>
              </div>
            `,
          });

          marker.addListener('click', () => {
            infoWindow.open(this.googleMap, marker);
            // Update the selected row ID
            this.selectedRowId = row[0];
            // Update all marker colors
            this.updateMarkerColors();
            // Scroll the row into view
            const rowElement = document.querySelector(`[data-row-id="${row[0]}"]`);
            if (rowElement) {
              rowElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
          });

          this.markers.push(marker);
          bounds.extend(position);
        });

        // Fit map to bounds if we have markers
        if (this.markers.length > 0) {
          this.googleMap.fitBounds(bounds);

          // If we only have one marker, zoom in a bit
          if (this.markers.length === 1) {
            this.googleMap.setZoom(15);
          }
        }
      } else {
        console.warn('No location data returned from API, using fallback markers');
        this.addFallbackMarkers();
      }
    } catch (error) {
      console.error('Error fetching Condo coordinates:', error.message || error);
      this.addFallbackMarkers();
    }
  }

  async addCondoMapBoxMarkers(): Promise<void> {
    if (!this.data || this.data.length === 0) return;

    // Clear existing markers
    this.condoMapMarkers.features = [];
    this.bookmarkMapMarkers.features = [];

    try {
      // Prepare transaction data for API call
      const transactions = this.data.map((row) => ({
        projectName: row[2], // Project Name
        address: row[3], // Address
        district: row[4], // District
      }));

      const bookmarkTransactions = this.bookmarkedRows.map((row) => ({
        projectName: row[2], // Project Name
        address: row[3], // Address
        district: row[4], // District
      }));

      // Get coordinates from API using the controller directly
      const response = await this.#condoController.getCondoCoordinates({ transactions });

      // Only fetch bookmarked coordinates if we have bookmarks and showBookmarkedOnMap is true
      let bookmarkResponse: any = { success: false, locations: [] };
      if (this.bookmarkedRows.length > 0) {
        bookmarkResponse = await this.#condoController.getCondoCoordinates({
          transactions: bookmarkTransactions,
        });
      }

      if (response.success && response.locations.length > 0) {
        // Process regular markers
        response.locations.forEach((location, index) => {
          const position = {
            lat: location.latitude,
            lng: location.longitude,
          };

          // Get the corresponding data row
          const row =
            this.data.find((r) => r[2] === location.project_name && r[3] === location.address) ||
            this.data[index];

          const rowId = row[0]; // Assuming row ID is at index 0
          const isBookmarked = this.isBookmarked(row);
          const isSelected = this.selectedRowId === rowId;

          // If showBookmarkedOnMap is true, we'll separate bookmarked markers
          // Otherwise, we'll just color them differently in the same layer
          if (isBookmarked && this.showBookmarkedOnMap) {
            // Skip adding to condoMapMarkers as we'll add it to bookmarkMapMarkers
            // This prevents duplicate markers
            return;
          }

          // For non-bookmarked markers or when not showing bookmarked separately
          let markerColor = '#008000'; // Green for regular markers

          // If not showing bookmarked separately but this is a bookmarked marker
          if (isBookmarked && !this.showBookmarkedOnMap) {
            markerColor = '#e53935'; // Red for bookmarked
          } else if (isSelected) {
            markerColor = '#9c27b0'; // Purple for selected
          }

          this.condoMapMarkers.features.push({
            type: 'Feature',
            geometry: {
              type: 'Point',
              coordinates: [position.lng, position.lat],
            },
            properties: {
              title: `${row[2]} ${row[3]}`, // Project name + Address
              color: markerColor,
              id: rowId,
              isBookmarked,
              isSelected,
              shape: 'square',
            },
          });
        });

        // Force update of regular markers
        this.condoMapMarkers = { ...this.condoMapMarkers };

        // Process bookmarked markers if showing them separately
        if (
          this.showBookmarkedOnMap &&
          bookmarkResponse.success &&
          bookmarkResponse.locations.length > 0
        ) {
          bookmarkResponse.locations.forEach((location) => {
            // Find the corresponding bookmarked row
            const bookmarkedRow = this.bookmarkedRows.find(
              (br) => br[2] === location.projectName && br[3] === location.address,
            );

            if (bookmarkedRow) {
              const position = {
                lat: location.latitude,
                lng: location.longitude,
              };

              const rowId = bookmarkedRow[0];
              const isSelected = this.selectedRowId === rowId;

              // Always red for bookmarked markers
              const markerColor = isSelected ? '#9c27b0' : '#e53935';

              this.bookmarkMapMarkers.features.push({
                type: 'Feature',
                geometry: {
                  type: 'Point',
                  coordinates: [position.lng, position.lat],
                },
                properties: {
                  title: `${bookmarkedRow[2]} ${bookmarkedRow[3]}`,
                  color: markerColor,
                  id: rowId,
                  isBookmarked: true,
                  isSelected,
                },
              });
            }
          });

          // Force update of bookmarked markers
          this.bookmarkMapMarkers = { ...this.bookmarkMapMarkers };
        }

        // Fit map to all markers (both regular and bookmarked)
        const allCoordinates = [
          ...this.condoMapMarkers.features.map((feature) => feature.geometry.coordinates),
          ...this.bookmarkMapMarkers.features.map((feature) => feature.geometry.coordinates),
        ];

        if (allCoordinates.length > 0) {
          this.fitMapToMarkers(allCoordinates);
        }
      }
    } catch (error) {
      console.error('Error adding markers:', error);
    }
  }
  /**
   * Add fallback markers with random positions
   */
  private addFallbackMarkers(): void {
    if (!this.googleMap || !this.data || this.data.length === 0) return;

    const { Marker, InfoWindow } = google.maps;

    this.data.forEach((row) => {
      const projectName = row[2];
      const address = row[3];

      // Create a marker at a random position around Singapore
      const lat = this.singaporeCenter.lat + (Math.random() - 0.5) * 0.1;
      const lng = this.singaporeCenter.lng + (Math.random() - 0.5) * 0.1;

      // Determine if this row is bookmarked and selected status
      const isRowBookmarked = this.isBookmarked(row);
      const isSelected = row[0] === this.selectedRowId;

      // Set marker icon shape and color based on selection and bookmark status
      let markerIcon;

      if (isRowBookmarked) {
        // Red diamond for bookmarked transactions
        markerIcon = {
          path: 'M 0 -10 L 10 0 L 0 10 L -10 0 Z',
          fillColor: '#FF0000',
          fillOpacity: 1,
          strokeWeight: 1,
          strokeColor: '#FFFFFF',
          scale: 1,
        };
      } else if (isSelected) {
        // Purple square for selected transactions
        markerIcon = {
          path: 'M -10 -10 L 10 -10 L 10 10 L -10 10 Z',
          fillColor: '#800080', // Purple
          fillOpacity: 1,
          strokeWeight: 1,
          strokeColor: '#FFFFFF',
          scale: 1,
        };
      } else {
        // Green square for regular transactions
        markerIcon = {
          path: 'M -10 -10 L 10 -10 L 10 10 L -10 10 Z',
          fillColor: '#008000',
          fillOpacity: 1,
          strokeWeight: 1,
          strokeColor: '#FFFFFF',
          scale: 1,
        };
      }

      const marker = new Marker({
        position: { lat, lng },
        map: this.googleMap,
        title: projectName,
        icon: markerIcon,
      });

      // Add info window with property details
      const infoWindow = new InfoWindow({
        content: `
          <div>
            <h3>${projectName}</h3>
            <p>${address}</p>
            <p>Price: $${row[7]?.toLocaleString() || 'N/A'}</p>
            <p>Area: ${Math.round(row[6] * 10.73692) || 'N/A'} sqft</p>
          </div>
        `,
      });

      marker.addListener('click', () => {
        infoWindow.open(this.googleMap, marker);
        // Update the selected row ID
        this.selectedRowId = row[0];
        // Update all marker colors
        this.updateMarkerColors();
        // Scroll the row into view
        const rowElement = document.querySelector(`[data-row-id="${row[0]}"]`);
        if (rowElement) {
          rowElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      });

      this.markers.push(marker);
    });
  }
  removeAllBookmarks(): void {
    // Clear all bookmarks
    this.bookmarkedRows = [];

    // If map is visible and showing bookmarked items with different color, refresh markers
    // if (this.showMap && this.showBookmarkedOnMap) {
    //   this.addCondoMarkers();
    // }
    if (this.showMap) {
      this.addCondoMapBoxMarkers();
    }
  }

  /**
   * Toggle bookmark for a row
   * @param row The row to bookmark/unbookmark
   * @param event The checkbox change event
   */
  toggleBookmark(row: any, event: Event): void {
    event.stopPropagation(); // Prevent row selection when clicking the checkbox
    const index = this.bookmarkedRows.findIndex((bookmarkedRow) => bookmarkedRow[0] === row[0]);

    if (index === -1) {
      this.bookmarkedRows.push(row);
    } else {
      this.bookmarkedRows.splice(index, 1);
    }

    // If map is visible and showing bookmarked items with different color, refresh markers
    // if (this.showMap && this.showBookmarkedOnMap) {
    //   this.addCondoMarkers();
    // }
    if (this.showMap) {
      this.addCondoMapBoxMarkers();
    }
  }

  /**
   * Check if a row is bookmarked
   * @param row The row to check
   * @returns True if the row is bookmarked
   */
  isBookmarked(row: any): boolean {
    return this.bookmarkedRows.some(
      (bookmarkedRow) => bookmarkedRow[0] === row[0], // Compare by ID (first column)
    );
  }

  /**
   * Remove a row from bookmarks
   * @param row The row to remove from bookmarks
   * @param event The button click event
   */
  removeBookmark(row: any, event: Event): void {
    // Prevent event propagation
    event.stopPropagation();

    const index = this.bookmarkedRows.findIndex(
      (bookmarkedRow) => bookmarkedRow[0] === row[0], // Compare by ID (first column)
    );

    if (this.showBookmarkedChart) {
      this.generateBookmarkedCharts();
    }

    if (index !== -1) {
      this.bookmarkedRows.splice(index, 1);
    }
  }

  public async openFilterForm() {
    const form: SilverField[] = [
      {
        controlType: SilverFieldTypes.SELECT,
        label: 'District',
        key: 'districtId',
        value: null,
        nullable: true,
        valid: { required: false },
        multiple: true,
        options: this.#condoSalesService.getAllDistricts(this.db),
      },
      {
        controlType: SilverFieldTypes.SELECT,
        label: 'Street',
        key: 'streetId',
        value: null,
        nullable: true,
        valid: { required: false },
        multiple: true,
        options: this.#condoSalesService.getAllStreets(this.db),
      },
      {
        controlType: SilverFieldTypes.SELECT,
        label: 'Project',
        key: 'project',
        value: null,
        nullable: true,
        valid: { required: false },
        multiple: true,
        options: this.#condoSalesService.getAllProjects(this.db),
      },
      {
        controlType: SilverFieldTypes.SELECT,
        label: 'Market Segment',
        key: 'marketSegmentId',
        value: null,
        nullable: true,
        valid: { required: false },
        multiple: true,
        options: this.#condoSalesService.getAllMarketSegments(this.db),
      },
      {
        controlType: SilverFieldTypes.SELECT,
        label: 'Property Type',
        key: 'propertyTypeId',
        value: null,
        nullable: true,
        valid: { required: false },
        multiple: true,
        options: this.#condoSalesService.getAllPropertyTypes(this.db),
      },
      {
        controlType: SilverFieldTypes.SELECT,
        label: 'Type of Area',
        key: 'typeOfAreaId',
        value: null,
        nullable: true,
        valid: { required: false },
        multiple: true,
        options: this.#condoSalesService.getAllTypeOfAreas(this.db),
      },
      {
        controlType: SilverFieldTypes.DATE_RANGE,
        label: 'Time Period',
        key: 'timePeriod',
        value: { from: null, to: null },
        valid: { required: false },
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Minimum Price',
        placeholder: 'Minimum Price',
        type: 'number',
        key: 'minimumPrice',
        value: '',
        valid: { required: false },
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Maximum Price',
        placeholder: 'Maximum Price',
        type: 'number',
        key: 'maximumPrice',
        value: '',
        valid: { required: false },
      },
      // {
      //   controlType: SilverFieldTypes.TEXT,
      //   label: 'Minimum Lease Year',
      //   placeholder: 'Minimum Lease Year',
      //   type: 'number',
      //   key: 'minimumLeaseYear',
      //   value: '',
      //   valid: { required: false },
      // },
      // {
      //   controlType: SilverFieldTypes.TEXT,
      //   label: 'Maximum Lease Year',
      //   placeholder: 'Maximum Lease Year',
      //   type: 'number',
      //   key: 'maximumLeaseYear',
      //   value: '',
      //   valid: { required: false },
      // },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Minimum Floor',
        placeholder: 'Minimum Floor',
        type: 'number',
        key: 'minimumStorey',
        value: '',
        valid: { required: false },
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Maximum Floor',
        placeholder: 'Maximum Floor',
        type: 'number',
        key: 'maximumStorey',
        value: '',
        valid: { required: false },
      },
      // {
      //   controlType: SilverFieldTypes.TEXT,
      //   label: 'Area (sqm)',
      //   placeholder: 'Area (sqm)',
      //   type: 'number',
      //   key: 'areaSqm',
      //   value: '',
      //   valid: { required: false },
      // },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Minimum SQFT',
        placeholder: 'Minimum SQFT',
        type: 'number',
        key: 'minimumSqft',
        value: '',
        valid: { required: false },
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Maximum SQFT',
        placeholder: 'Maximum SQFT',
        type: 'number',
        key: 'maximumSqft',
        value: '',
        valid: { required: false },
      },
    ];

    const result = await this.#formModalService.open({ heading: 'Filter Condo Sales', form });
    if (!result.action) return;
    this.filter = result.value;
    this.config.page = 1;
    this.config.lastId = 0;
    this.getData();
  }

  /**
   * Toggle the display of the bookmarked transactions chart
   */
  toggleBookmarkedChart(): void {
    this.showBookmarkedChart = !this.showBookmarkedChart;

    if (this.showBookmarkedChart && this.bookmarkedRows.length > 0) {
      this.generateBookmarkedCharts();
    }
  }

  /**
   * Generate charts specifically for bookmarked transactions
   */
  private generateBookmarkedCharts(): void {
    if (this.bookmarkedRows.length === 0) return;

    console.log('BOOKMARKED ROWS', this.bookmarkedRows);

    // Group bookmarked transactions by year and month for price chart
    const pricesByYearMonth = new Map<string, number[]>();
    const psfByYearMonth = new Map<string, number[]>();

    this.bookmarkedRows.forEach((row) => {
      // Date is in format "YYYY-MM-DD" at index 9
      const dateStr = row[1];
      console.log(dateStr);
      if (!dateStr) return;

      // Extract year and month from the date string
      const [year, month] = dateStr.split('-');
      const yearMonth = `${year}-${month}`;

      // Price is at index 4
      const price = Number(row[7]);
      if (!pricesByYearMonth.has(yearMonth)) {
        pricesByYearMonth.set(yearMonth, []);
      }
      pricesByYearMonth.get(yearMonth)!.push(price);

      // PSF is at index 5
      const psf = Number(row[8]);
      if (!psfByYearMonth.has(yearMonth)) {
        psfByYearMonth.set(yearMonth, []);
      }
      psfByYearMonth.get(yearMonth)!.push(psf);
    });

    // Calculate median price for each month and sort chronologically
    const medianPrices = Array.from(pricesByYearMonth.entries())
      .map(([yearMonth, prices]) => {
        const sortedPrices = [...prices].sort((a, b) => a - b);
        const median = this.calculateMedian(sortedPrices);

        // Format the date for display (convert YYYY-MM to MMM YYYY)
        const [year, month] = yearMonth.split('-');
        const date = new Date(Number(year), Number(month) - 1);
        const formattedDate = date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
        });

        return [formattedDate, median];
      })
      .sort((a, b) => {
        // Sort by date (convert MMM YYYY back to Date objects for comparison)
        const dateA = new Date(a[0]);
        const dateB = new Date(b[0]);
        return dateA.getTime() - dateB.getTime();
      });

    // Calculate average PSF for each month and sort chronologically
    const averagePSFValues = Array.from(psfByYearMonth.entries())
      .map(([yearMonth, psfValues]) => {
        const total = psfValues.reduce((sum, val) => sum + val, 0);
        const average = total / psfValues.length; // Calculate the average

        // Format the date for display (convert YYYY-MM to MMM YYYY)
        const [year, month] = yearMonth.split('-');
        const date = new Date(Number(year), Number(month) - 1);
        const formattedDate = date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
        });

        return [formattedDate, average];
      })
      .sort((a, b) => {
        // Sort by date (convert MMM YYYY back to Date objects for comparison)
        const dateA = new Date(a[0]);
        const dateB = new Date(b[0]);
        return dateA.getTime() - dateB.getTime();
      });

    // Prepare data for price chart
    this.bookmarkedPriceChartData = {
      heading: 'Bookmarked Transactions - Monthly Median Price',
      header: ['Date', 'Median Price'],
      data: medianPrices,
    };

    // Prepare data for PSF chart
    this.bookmarkedPsfChartData = {
      heading: 'Bookmarked Transactions - Monthly Average PSF',
      header: ['Date', 'Average PSF'],
      data: averagePSFValues,
    };
  }
  handleClosePropertyInfo() {
    this.showPropertyInformation = false;
  }
}
