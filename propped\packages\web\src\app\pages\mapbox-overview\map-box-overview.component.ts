import { CommonModule } from '@angular/common';
import {
  type After<PERSON><PERSON>wChecked,
  Component,
  type ElementRef,
  inject,
  type On<PERSON><PERSON>roy,
  type OnInit,
  ViewChild,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { Router } from '@angular/router';
import { type ICondo } from '@api/controllers/condo/condo.model';
import { apiRPC, injectController } from '@api/rpc';
import { FormModalService } from '@lib/angular/dynamic-forms/form-modal.service';
import {
  type SilverField,
  SilverFieldTypes,
} from '@lib/angular/dynamic-forms/silver-field.component';
import { SnackBarService } from '@lib/angular/snack-bar.service';
import { daisyMerge } from '@lib/common/tailwind-merge';
import { Chart, registerables } from 'chart.js';
import type * as mapboxgl from 'mapbox-gl';
import {
  type LayerSpecification,
  LngLatBounds,
  type LngLatLike,
  type Map as MapBoxMap,
} from 'mapbox-gl';
import {
  GeoJSONSourceComponent,
  LayerComponent,
  MapComponent,
  MarkerComponent,
  PopupComponent,
} from 'ngx-mapbox-gl';
import { condoSalesListingData, hdbSalesListingData } from './sales-listing-data';

Chart.register(...registerables);

interface MarkerData {
  lngLat: LngLatLike;
  title: string;
  color?: string;
}

const getUserLocation = (options?: PositionOptions): Promise<GeolocationPosition> =>
  new Promise((s, f) => navigator.geolocation.getCurrentPosition(s, f, options));

@Component({
  selector: 'app-map-box-overview',
  templateUrl: './map-box-overview.component.html',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MapComponent,
    GeoJSONSourceComponent,
    LayerComponent,
    MatIconModule,
    PopupComponent,
    MarkerComponent,
  ],
})
export class MapBoxOverviewComponent implements OnInit, OnDestroy, AfterViewChecked {
  readonly #formModalService = inject(FormModalService);
  readonly #router = inject(Router);
  readonly #snackBarService = inject(SnackBarService);

  readonly #busStopsController = injectController(apiRPC.BusStopsController);
  readonly #condoController = injectController(apiRPC.CondoController);
  readonly #hawkerController = injectController(apiRPC.HawkerController);
  readonly #hdbController = injectController(apiRPC.HDBController);
  readonly #hospitalController = injectController(apiRPC.HospitalController);
  readonly #metroController = injectController(apiRPC.MetroController);
  readonly #oneMapSearchController = injectController(apiRPC.OneMapSearchController);
  readonly #schoolController = injectController(apiRPC.SchoolController);

  public userLocation: GeoJSON.Feature<GeoJSON.Point> | null = null;
  public searchLocation: GeoJSON.Feature<GeoJSON.Point> | null = null;
  public showAmenitiesPopup = false;
  public amenitiesPopUpContent: any = null;
  public amenitiesFeature: GeoJSON.Feature<GeoJSON.Point> | null = null;
  public isDraggingUserLocation = false;
  public userLocationDragMarker: mapboxgl.Marker | null = null;
  // Map configuration
  map?: MapBoxMap;
  public currentLightPreset: 'day' | 'night' | 'dawn' | 'dusk' | null = null;
  style = 'mapbox://styles/mapbox/streets-v12';
  lat = 1.3521;
  lng = 103.8198;
  zoom = 11;
  center: [number, number] = [this.lng, this.lat];
  isMapLoaded = false;
  hdbSourceData = { type: 'FeatureCollection', features: [] };
  condoSourceData = { type: 'FeatureCollection', features: [] };
  bounds = [
    [103.68, 1.2], // Southwest coordinates (approximate)
    [104.0, 1.44], // Northeast coordinates (approximate)
  ];

  hdbTowns: string[] = [];
  db: any = null;
  markers: MarkerData[] = [];
  singaporeGeoJSON = {
    type: 'Feature',
    geometry: { type: 'Point', coordinates: this.center },
    properties: { title: 'Singapore' },
  };
  public showDropdown = false;
  public activeTab: 'HDB' | 'Condo' = 'HDB';
  public selectedTowns: Set<string> = new Set();
  public selectedTown = '';
  public showCondoData = false;
  public showHDBListings = false;
  public showCondoListings = false;
  public filter: any = {};
  public filteredPropertyData: any[] = [];
  public propertyData: any[] = hdbSalesListingData;
  public condoData: any[] = condoSalesListingData;
  public showPropertyDetails = false;
  public selectedProperty: any = null;
  public showSalesPropertyDetails = false;
  public salesProperty: any = null;
  public propertyTransactionData: any = null;
  public transactionMedianPrices: any[] = [];
  public transactionAveragePSF: any[] = [];
  public showTransactionChart = false;
  public selectedMarkerCoordinates: number[] | null = null;
  userLocationSource = {
    type: 'FeatureCollection',
    features: [] as GeoJSON.Feature[],
  };
  searchLocationSource = {
    type: 'FeatureCollection',
    features: [] as GeoJSON.Feature[],
  };
  public selectedPropertyFeature: GeoJSON.Feature<GeoJSON.Point> | null = null;
  public selectedPropertyName = '';
  // Map controls
  geolocateOptions = {
    positionOptions: {
      enableHighAccuracy: true,
    },
    trackUserLocation: true,
  };
  public townColors: { [key: string]: string } = {};
  public daisyMerge = daisyMerge;
  public is3DEnabled = false;
  public labelLayerId?: string = '';
  public showMoreInfoPopUp = false;

  // Predefined colors for towns
  private townColorPalette = [
    '#4285F4', // Google Blue
    '#EA4335', // Google Red
    '#FBBC05', // Google Yellow
    '#34A853', // Google Green
    '#8E24AA', // Purple
    '#16A085', // Turquoise
    '#F39C12', // Orange
    '#D35400', // Pumpkin
    '#C0392B', // Pomegranate
    '#2980B9', // Belize Hole
    '#27AE60', // Nephritis
    '#F1C40F', // Sunflower
    '#E74C3C', // Alizarin
    '#9B59B6', // Amethyst
    '#FB6D9D', // Pink
    '#95A5A6', // Concrete
    '#1ABC9C', // Aqua
    '#2ECC71', // Emerald
    '#3498DB', // Peter River
    '#9B27B0', // Deep Purple
    '#FF5722', // Deep Orange
    '#607D8B', // Blue Grey
    '#3F51B5', // Indigo
    '#795548', // Brown
  ];

  public paginationOptions = [10, 50, 100, 500, 1000];
  public currentCondoPage = 0;
  public selectedCondoLimit = 50;
  public totalCondoCount = 0;
  apiCondoData: ICondo[] = [];
  public Math = Math;
  public searchQuery = '';
  public searchResults: any[] = [];
  public showSearchResults = false;
  public recentSearches: string[] = [];
  public showUserLocationPopup = false;
  public showSearchLocationPopup = false;
  public selectedRadius = 1; // Default radius of 1km
  public nearbyOptions = {
    schools: false,
    hawker: false,
    busStops: false,
    mrt: false,
    salesListings: false,
    hdbData: false,
    healthcare: false,
  };
  amenitiesSource = {
    type: 'FeatureCollection',
    features: [] as GeoJSON.Feature[],
  };

  // Radius circle source
  radiusCircleSource = {
    type: 'FeatureCollection',
    features: [] as GeoJSON.Feature[],
  };

  // Loading states
  isLoadingSchools = false;
  private searchDebounceTimer: any = null;
  public searchResultsDropdown = false;
  public sellerEthnicData: any = [];
  public buyerEthnicData: any = [];
  public showBuyerEthnicQuota = false;
  public showSellerEthnicQuota = false;

  // Chart references and instances
  @ViewChild('medianPriceChart') medianPriceChartRef?: ElementRef<HTMLCanvasElement>;
  @ViewChild('avgPsfChart') avgPsfChartRef?: ElementRef<HTMLCanvasElement>;
  private medianPriceChart?: Chart;
  private avgPsfChart?: Chart;
  private chartsInitialized = false;

  setActiveTab(tab: 'HDB' | 'Condo'): void {
    this.activeTab = tab;
    this.hdbSourceData = {
      type: 'FeatureCollection',
      features: [],
    };
    this.condoSourceData = {
      type: 'FeatureCollection',
      features: [],
    };
    if (tab === 'Condo' && !this.showCondoData) {
      this.showCondoData = true;
      this.loadCondoPage(0);
    }
  }

  // Toggle 3D buildings layer
  toggle3DBuildings(): void {
    this.is3DEnabled = !this.is3DEnabled;
    console.log('Toggled 3D buildings:', this.is3DEnabled);
  }

  async ngOnInit(): Promise<void> {
    console.log('Map initialized');
    try {
      const response = await this.#hdbController.getAllTowns();
      this.hdbTowns = response.data.map((town: any) => town.name);
      this.assignColorsToTowns();
    } catch (error) {
      console.error('Error fetching HDB towns:', error);
    }

    // Load recent searches from localStorage
    this.loadRecentSearches();

    console.log('Loaded HDB towns:', this.hdbTowns);
  }

  async getUserLocation(): Promise<void> {
    if (!navigator.geolocation) {
      this.#snackBarService.error('Geolocation is not supported by this browser.');
      return;
    }
    try {
      const {
        coords: { latitude, longitude },
      } = await getUserLocation({ enableHighAccuracy: true, timeout: 5000, maximumAge: 0 });
      console.log('CURRENT USER LOCATION', latitude, longitude);
      this.userLocation = {
        type: 'Feature',
        geometry: { type: 'Point', coordinates: [103.8198, 1.3521] /* [long, lat] */ },
        properties: {
          title: 'Your Location',
          description: this.createUserLocationPopupContent(),
          pulse: 0,
        },
      };
      this.userLocationSource = { type: 'FeatureCollection', features: [this.userLocation] };
    } catch (error) {
      this.#snackBarService.error(
        error instanceof Error ? error.message : 'Failed to get user location',
      );
    }
  }
  /**
   * Creates HTML content for the user location popup
   */
  createUserLocationPopupContent(): string {
    return `
      <div class="info-window-content p-4 max-w-xs">
        <div class="mb-3">
          <h3 class="text-lg font-bold">My Location</h3>
        </div>

        <div class="mb-3">
          <div class="flex items-center justify-between mb-2 gap-1">
            <span class="text-md">Show Nearby within</span>
            <select id="radius-dropdown" class="px-2 py-1 rounded w-24">
              <option value="1" selected>1 km</option>
              <option value="2">2 km</option>
              <option value="3">3 km</option>
              <option value="4">4 km</option>
            </select>
          </div>

          <div class="flex items-center mb-2">
            <input type="checkbox" id="schools" class="mr-2">
            <label for="schools" class="text-md">Schools</label>
          </div>

          <div class="flex items-center mb-2">
            <input type="checkbox" id="hawker" class="mr-2">
            <label for="hawker" class="text-md">Hawker Centres</label>
          </div>

          <div class="flex items-center mb-2">
            <input type="checkbox" id="busStops" class="mr-2">
            <label for="busStops" class="text-md">Bus Stops</label>
          </div>

          <div class="flex items-center mb-2">
            <input type="checkbox" id="mrt" class="mr-2">
            <label for="mrt" class="text-md">MRT/LRT Stations</label>
          </div>

          <div class="flex items-center mb-2">
            <input type="checkbox" id="salesListings" class="mr-2">
            <label for="salesListings" class="text-md">Sales Listings</label>
          </div>

          <div class="flex items-center mb-2">
            <input type="checkbox" id="hdbData" class="mr-2">
            <label for="hdbData" class="text-md">HDB Data</label>
          </div>

          <div class="flex items-center mb-2">
            <input type="checkbox" id="healthcare" class="mr-2">
            <label for="healthcare" class="text-md">Healthcare</label>
          </div>
        </div>
      </div>
    `;
  }
  async findNearbyAmenities(
    type: 'userLocation' | 'searchLocation',
    amenityType: 'schools' | 'busStops' | 'hawker' | 'mrt' | 'healthcare' | 'hdbData',
  ): Promise<void> {
    if (!type || !this[type]) {
      this.#snackBarService.error('Please set your location first');
      return;
    }

    this.isLoadingSchools = true;

    try {
      // Convert radius from km to meters for calculations
      const radiusInMeters = this.selectedRadius * 1000;

      // Get user coordinates
      const coordinates = this[type].geometry.coordinates;
      const [longitude, latitude] = coordinates;

      // Draw radius circle
      this.drawRadiusCircle([longitude, latitude], this.selectedRadius);

      let response;
      let amenityName = '';

      // Call the appropriate controller based on amenity type
      switch (amenityType) {
        case 'schools':
          response = await this.#schoolController.findSchoolsWithinRadius({
            latitude,
            longitude,
            radius: radiusInMeters,
          });
          if (response.success) {
            this.addSchoolMarkers(response.schools);
            amenityName = 'schools';
          }
          break;

        case 'busStops':
          response = await this.#busStopsController.findBusStopsWithinRadius({
            latitude,
            longitude,
            radius: radiusInMeters,
          });
          if (response.success) {
            this.addBusStopMarkers(response.busStops);
            amenityName = 'bus stops';
          }
          break;

        case 'hawker':
          response = await this.#hawkerController.findHawkerCentersWithinRadius({
            latitude,
            longitude,
            radius: radiusInMeters,
          });
          if (response.success) {
            this.addHawkerCenterMarkers(response.hawkerCenters);
            amenityName = 'hawker centers';
          }
          break;

        case 'mrt':
          response = await this.#metroController.getMetroStationsWithinRadius({
            latitude,
            longitude,
            radius: radiusInMeters,
          });
          if (response.success) {
            this.addMetroStationMarkers(response.metroStations);
            amenityName = 'metro stations';
          }
          break;

        case 'healthcare':
          response = await this.#hospitalController.getHospitalsWithinRadius({
            latitude,
            longitude,
            radius: radiusInMeters,
          });
          if (response.success) {
            this.addHospitalMarkers(response.hospitals);
            amenityName = 'hospitals';
          }
          break;

        case 'hdbData':
          response = await this.#hdbController.findHDBPropertiesNearUserLocation({
            latitude,
            longitude,
            radius: radiusInMeters,
          });
          if (response.success) {
            this.addHDBMarkers(response.hdb);
            amenityName = 'HDB properties';
          }
          break;
      }

      if (response && response.success) {
        const count = response.count || (response.hdb ? response.hdb.length : 0);
        this.#snackBarService.success(
          `Found ${count} ${amenityName} within ${this.selectedRadius}km`,
        );
      } else {
        this.#snackBarService.error(`Failed to find ${amenityName}`);
      }
    } catch (error) {
      console.error(`Error finding ${amenityType}:`, error);
      this.#snackBarService.error(`Error finding ${amenityType}`);
    } finally {
      this.isLoadingSchools = false;
    }
  }

  /**
   * Handle nearby option changes
   */
  handleNearbyOptionChange(
    optionId: string,
    isChecked: boolean,
    type: 'userLocation' | 'searchLocation',
  ): void {
    // Update the option state
    this.nearbyOptions[optionId as keyof typeof this.nearbyOptions] = isChecked;
    console.log('UPDATED SELECTION', this.nearbyOptions);
    // Implement the logic to show/hide nearby options
    console.log(`Option ${optionId} changed to ${isChecked}`);
    if (type === 'userLocation') {
      this.showUserLocationPopup = false;
    } else {
      this.showSearchLocationPopup = false;
    }
    // Example implementation for different options
    switch (optionId) {
      case 'schools':
        if (isChecked) {
          this.findNearbyAmenities(type, 'schools');
        } else {
          this.clearNearbyMarkers();
        }
        break;
      case 'busStops':
        if (isChecked) {
          this.findNearbyAmenities(type, 'busStops');
        } else {
          this.clearNearbyMarkers();
        }
        break;
      case 'healthcare':
        if (isChecked) {
          this.findNearbyAmenities(type, 'healthcare');
        } else {
          this.clearNearbyMarkers();
        }
        break;
      case 'hawker':
        // Logic to show/hide hawker centers
        if (isChecked) {
          this.findNearbyAmenities(type, 'hawker');
        } else {
          this.clearNearbyMarkers();
        }
        break;
      case 'mrt':
        if (isChecked) {
          this.findNearbyAmenities(type, 'mrt');
        } else {
          this.clearNearbyMarkers();
        }
        break;
      case 'salesListings':
        if (isChecked) {
          this.displaySaleListings();
        } else {
          this.clearMarkers();
        }
        break;
      case 'hdbData':
        if (isChecked) {
          this.findNearbyAmenities(type, 'hdbData');
        } else {
          this.clearMarkers();
        }
        break;
        if (isChecked) {
          this.findNearbyHDBProperties(type);
        } else {
          this.clearMarkers();
        }
        break;
      // Add other cases as needed
    }
  }
  showCurrentLocation(): void {
    console.log('MAP OBJECT', this.map);
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          console.log('CURRENT USER LOCATION', latitude, longitude);
          if (this.map) {
            console.log('MAP OBJECT', this.map);

            // Update center coordinates
            this.lng = longitude;
            this.lat = latitude;
            // this.center = [longitude, latitude];
            this.center = [103.8198, 1.3521];

            // Fly to location
            this.map.flyTo({
              center: this.center,
              zoom: 15,
              essential: true,
            });

            // Update user location feature
            this.userLocation = {
              type: 'Feature',
              geometry: {
                type: 'Point',
                // coordinates: [longitude, latitude],
                coordinates: [103.8198, 1.3521],
              },
              properties: {
                title: 'Your Location',
                description: 'Your current location',
              },
            };

            // Update user location source
            this.userLocationSource = {
              type: 'FeatureCollection',
              features: [this.userLocation],
            };
          }
        },
        (error) => {
          console.error('Error getting location:', error);
          this.#snackBarService.error(
            'Unable to get your location. Please check your browser permissions.',
          );
        },
        {
          enableHighAccuracy: true,
          timeout: 5000,
          maximumAge: 0,
        },
      );
    } else {
      this.#snackBarService.error('Geolocation is not supported by your browser.');
    }
  }

  /**
   * Fetch search results from the OneMap API
   */
  async fetchSearchResults(): Promise<void> {
    try {
      const response = await this.#oneMapSearchController.getSearchResults({
        searchVal: this.searchQuery.trim(),
      });

      if (response.success && response.locations && response.locations.length > 0) {
        this.searchResults = response.locations;
        console.log('Search results:', this.searchResults);
      } else {
        this.searchResults = [];
      }
    } catch (error) {
      console.error('Error fetching search results:', error);
      this.#snackBarService.error('Error searching for locations');
      this.searchResults = [];
    }
  }

  selectSearchResult(result: any): void {
    this.searchResultsDropdown = false;
    this.searchQuery = '';
    // Add to recent searches if not already there
    this.saveToRecentSearches(result);
    if (this.searchLocation) {
      this.searchLocation = null;
      this.searchLocationSource = {
        type: 'FeatureCollection',
        features: [],
      };
    }
    if (this.userLocation) {
      this.userLocationSource = {
        type: 'FeatureCollection',
        features: [],
      };
    }
    this.clearMarkers();

    // Extract coordinates
    const longitude = Number(result.LONGITUDE);
    const latitude = Number(result.LATITUDE);

    // Add marker at the selected location
    this.searchLocation = {
      type: 'Feature',
      geometry: {
        type: 'Point',
        coordinates: [longitude, latitude],
      },
      properties: {
        title: result.ADDRESS,
        pulse: 0,
      },
    };

    // Update user location source
    this.searchLocationSource = {
      type: 'FeatureCollection',
      features: [this.searchLocation],
    };
    this.fitMapToMarkers([this.searchLocation.geometry.coordinates]);
    this.showMoreInfoPopUp = true;
    this.searchResults = [];
  }

  onSearchInput(): void {
    // Clear any previous timer
    this.searchResultsDropdown = true;
    if (this.searchDebounceTimer) {
      clearTimeout(this.searchDebounceTimer);
    }

    // If search query is empty, clear results
    if (!this.searchQuery || this.searchQuery.trim().length < 2) {
      this.searchResults = [];
      return;
    }
    this.searchDebounceTimer = setTimeout(() => {
      this.fetchSearchResults();
    }, 300); // 300ms debounce
  }

  saveToRecentSearches(query: string): void {
    if (!query) return;
    this.recentSearches = this.recentSearches.filter((s) => s !== query);
    this.recentSearches.unshift(query);
    if (this.recentSearches.length > 5) {
      this.recentSearches = this.recentSearches.slice(0, 5);
    }
    localStorage.setItem('mapbox-recent-searches', JSON.stringify(this.recentSearches));
  }
  loadRecentSearches(): void {
    const saved = localStorage.getItem('mapbox-recent-searches');
    if (saved) {
      try {
        this.recentSearches = JSON.parse(saved);
      } catch (e) {
        console.error('Error parsing recent searches:', e);
        this.recentSearches = [];
      }
    }
  }
  removeRecentSearch(query: string): void {
    this.recentSearches = this.recentSearches.filter((s) => s !== query);
    localStorage.setItem('mapbox-recent-searches', JSON.stringify(this.recentSearches));
  }

  async loadCondoPage(page: number): Promise<void> {
    if (page < 0) return;
    console.log('LOAD CONDO PAGE', page);
    try {
      this.currentCondoPage = page;
      const response = await this.#condoController.getAll({
        page: this.currentCondoPage,
        limit: this.selectedCondoLimit,
      });
      console.log('CONDO RESPONSE', response);
      this.apiCondoData = response.data;
      this.totalCondoCount = response.total;
      this.paginationOptions.push(this.totalCondoCount);
      this.#snackBarService.success(
        `Loaded ${this.apiCondoData.length} of ${this.totalCondoCount} condo properties`,
      );

      // Add condo markers to the map
      this.addCondoMarkers();
    } catch (error) {
      console.error('Error fetching Condo data:', error);
      this.#snackBarService.error('Error fetching Condo data');
    }
  }

  addCondoMarkers(): void {
    this.condoSourceData = {
      type: 'FeatureCollection',
      features: [],
    };
    const features = this.apiCondoData
      .filter((condo: ICondo) => condo.location?.coordinates?.length === 2)
      .map((condo: ICondo) => ({
        type: 'Feature',
        geometry: {
          type: 'Point',
          coordinates: condo.location.coordinates,
        },
        properties: {
          id: condo._id,
          title: condo.projectName,
          projectName: condo.projectName,
          address: condo.address,
          district: condo.district,
          type: 'condo',
          color: '#FF5722', // Distinctive color for condos
        },
      }));
    this.condoSourceData = {
      type: 'FeatureCollection',
      features,
    };

    // Fit map to markers if there are any
    // if (features.length > 0) {
    //   this.fitMapToMarkers(features.map((f: any) => f.geometry.coordinates));
    // }
  }

  onMapLoad(map: MapBoxMap): void {
    this.map = map;
    this.isMapLoaded = true;
    const layers = map.getStyle().layers;
    console.log('LAYERS', layers);
    if (!layers) {
      return;
    }
    this.labelLayerId = this.getLabelLayerId(layers);
    console.log('LABEL LAYER', this.labelLayerId);
    // initial light preset
    this.setMapLightPreset(this.currentLightPreset);
    // this.showCurrentLocation();
  }

  private getLabelLayerId(layers: LayerSpecification[]) {
    for (const layer of layers) {
      if (layer.type === 'symbol' && layer.layout?.['text-field']) {
        return layer.id;
      }
    }
    return;
  }
  updateMapStyle(map: MapBoxMap): void {
    const allowedSymbolLayers = [
      'school-label',
      'railway-label',
      'bus-stop-label',
      'place-town',
      'settlement-subdivision-label',
      'metro-label',
      'transit-label',
      'building',
      'road-street',
      'road-label',
      'building-number-label',
      'block-number-label',
    ];

    map.style.stylesheet.layers.forEach(function (layer) {
      if (layer.type === 'symbol' && !allowedSymbolLayers.includes(layer.id)) {
        console.log('Hiding SYMBOL LAYER:', layer.id);
        map.setLayoutProperty(layer.id, 'visibility', 'none');
      }
    });

    // Apply light preset if present
    if (this.currentLightPreset) {
      this.map?.setConfigProperty('basemap', 'lightPreset', this.currentLightPreset);
      console.log(`Map light preset applied: ${this.currentLightPreset}`);
    }
  }

  setMapLightPreset(preset: 'day' | 'night' | 'dawn' | 'dusk'): void {
    if (!this.map) return;

    // Toggle off if the same preset is selected
    if (this.currentLightPreset === preset) {
      this.currentLightPreset = null;
      this.style = 'mapbox://styles/mapbox/streets-v12';
      return;
    }

    // Set the current preset and change to satellite style
    this.currentLightPreset = preset;
    this.style = 'mapbox://styles/mapbox/standard-satellite';
    // The updateMapStyle method will be called automatically when the style loads
  }

  formatHDBTransactionTableData(tableData: any) {
    if (!tableData) {
      return [];
    }
    const formattedData = tableData.map((transaction: any) => {
      const values = Object.values(transaction).flat();

      // Get the date (index 2)
      const date = values[2];

      // Get the flat type (index 3)
      const flatType = values[3];

      // Get the floor range (index 6)
      const floorRange = values[6];

      // Convert area from sqm to sqft (index 7)
      // 1 sqm = 10.7639 sqft
      const areaSqm = Number(values[7]);
      const areaSqft = Math.round(areaSqm * 10.7639);

      // Get the price (index 8)
      const price = Number(values[8]);

      // Calculate price per square foot (PSF)
      const psf = Math.round(price / areaSqft);

      // Format price with dollar sign and commas
      const formattedPrice = `$${price.toLocaleString()}`;

      // Format PSF with dollar sign
      const formattedPsf = `$${psf}`;

      return [date, flatType, floorRange, areaSqft.toString(), formattedPrice, formattedPsf];
    });
    return formattedData;
  }

  async togglePropertyModal(data: any) {
    try {
      const [longitude, latitude] = data.location.coordinates;
      const hdbResponse = await this.#hdbController.extractHDBTransactionData({
        latitude: Number(latitude),
        longitude: Number(longitude),
      });
      // Format the transaction data
      console.log('HDB DATA TO SHOW IN MODAL', data);
      console.log('SELLER ETHNIC DATA', this.sellerEthnicData);

      console.log('BUYER ETHNIC DATA', this.buyerEthnicData);
      const formattedData = this.formatHDBTransactionTableData(hdbResponse?.data);

      const tableData = {
        heading: 'Transaction Details',
        header: ['Date', 'Type', 'Floor Level', 'Area (SQFT)', 'Price (S$)', 'PSF (S$)'],
        data: formattedData?.slice(0, 10),
      };

      const { transactionMedianPrices, transactionAveragePSF } =
        this.generateMonthlyMedianPricesAndAvgPSF(hdbResponse?.data);

      // Update the component properties for the embedded panel
      this.selectedProperty = data;
      this.propertyTransactionData = tableData;
      this.transactionMedianPrices = transactionMedianPrices;
      this.transactionAveragePSF = transactionAveragePSF;
      this.showPropertyDetails = true;
      this.showTransactionChart = false;
      this.selectedMarkerCoordinates = data.location.coordinates;

      this.sellerEthnicData = await this.#hdbController
        .getSellerEthnicGroupData({
          blockId: data?.blockNumber.substring(4),
          street: data?.title?.split(',')[1]?.trim(),
        })
        .then((res) => {
          console.log(res.data);
          return res.data;
        });

      this.buyerEthnicData = await this.#hdbController
      .getBuyerEthnicGroupData({
        blockId: data?.blockNumber.substring(4),
        street: data?.title?.split(',')[1]?.trim(),
      })
      .then((res) => {
        console.log(res.data);
        return res.data;
      });


      // Resize the map
      if (this.map) {
        setTimeout(() => {
          this.fitMapToMarkers([this.selectedProperty.geometry.coordinates]);
        }, 100);
      }
    } catch (error) {
      console.error('Error in togglePropertyModal:', error);
    }
  }
  public generateMonthlyMedianPricesAndAvgPSF(data: any[]) {
    console.log('Generating monthly median prices and average PSF...', data);
    if (!data || data.length === 0) {
      return { transactionMedianPrices: [], transactionAveragePSF: [] };
    }

    const pricesByYearMonth = new Map<string, number[]>();
    const psfByYearMonth = new Map<string, number[]>();

    // Conversion factor from square meters to square feet
    const sqmToSqftFactor = 10.764;

    data.forEach((row) => {
      // Use month field instead of date
      const monthStr = row.month; // Format: 'YYYY-MM'
      if (!monthStr) return;

      // Extract resale_price instead of price
      const price = Number(row.resale_price);

      // Calculate PSF from floor_area_sqm
      const areaSqm = Number(row.floor_area_sqm);
      const areaSqft = areaSqm * sqmToSqftFactor;
      const psf = price / areaSqft;

      // Skip invalid entries
      if (isNaN(price) || isNaN(psf) || price <= 0 || psf <= 0) return;

      if (!pricesByYearMonth.has(monthStr)) {
        pricesByYearMonth.set(monthStr, []);
        psfByYearMonth.set(monthStr, []);
      }

      pricesByYearMonth.get(monthStr)!.push(price);
      psfByYearMonth.get(monthStr)!.push(psf);
    });

    const result = Array.from(pricesByYearMonth.keys()).map((yearMonth) => {
      const prices = pricesByYearMonth.get(yearMonth)!;
      const psfs = psfByYearMonth.get(yearMonth)!;

      // Calculate median price
      const sortedPrices = [...prices].sort((a, b) => a - b);
      const median =
        sortedPrices.length % 2 === 0
          ? (sortedPrices[sortedPrices.length / 2 - 1] + sortedPrices[sortedPrices.length / 2]) / 2
          : sortedPrices[Math.floor(sortedPrices.length / 2)];

      // Calculate average PSF
      const avgPSF = psfs.reduce((sum, val) => sum + val, 0) / psfs.length;

      // Format date for display
      const [year, month] = yearMonth.split('-');
      const date = new Date(Number(year), Number(month) - 1);
      const formattedDate = date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
      });

      return {
        date: formattedDate,
        medianPrice: Math.round(median), // Round to nearest dollar
        averagePSF: Math.round(avgPSF), // Round to nearest dollar
      };
    });

    // Sort results chronologically
    const sortedResult = result.sort((a, b) => {
      const dateA = new Date(a.date);
      const dateB = new Date(b.date);
      return dateA.getTime() - dateB.getTime();
    });

    // Format data for charts
    const transactionMedianPrices = sortedResult.map(({ date, medianPrice }) => [
      date,
      medianPrice,
    ]);
    const transactionAveragePSF = sortedResult.map(({ date, averagePSF }) => [date, averagePSF]);

    return { transactionMedianPrices, transactionAveragePSF };
  }

  changeMapStyle(): void {
    // Style will be automatically updated by the library
    // We just need to handle re-adding markers if needed
    if (this.selectedTown) {
      setTimeout(() => {
        this.loadTownData(this.selectedTown);
      }, 500); // Give the map time to load the new style
    }
  }

  async applyFilters(): Promise<void> {
    console.log('Selected Town', this.selectedTown);
    if (this.selectedTown) {
      await this.loadTownData(this.selectedTown);
    } else {
      this.showDropdown = false;
      this.clearMarkers();
    }
  }

  async loadTownData(town: string): Promise<void> {
    this.clearMarkers();
    try {
      const response = await this.#hdbController.filterDataByTown({ town });
      console.log(`Response for ${town}:`, response);

      if (response.data?.length) {
        this.addTownMarkers(response.data, town);
      }
    } catch (error) {
      console.error(`Error fetching data for ${town}:`, error);
    }
  }

  addTownMarkers(data: any[], town: string): void {
    console.log('HDB block data:', data);

    const features = data
      .filter((block: any) => block.location?.coordinates?.length === 2 && block.postalCode)
      .map((block: any) => {
        return {
          type: 'Feature',
          geometry: {
            type: 'Point',
            coordinates: block.location.coordinates,
          },
          properties: {
            id: block._id,
            title: block.title || `Block ${block.blockNumber}, ${block.streetAddress}`,
            blockNumber: block.blockNumber,
            streetAddress: block.streetAddress,
            town: town,
            color: this.townColors[town],
          },
        };
      });

    this.hdbSourceData = {
      type: 'FeatureCollection',
      features,
    };

    if (features.length > 0) {
      this.fitMapToMarkers(features.map((f: any) => f.geometry.coordinates));
    }
  }

  clearMarkers(): void {
    this.markers = [];
    this.hdbSourceData = {
      type: 'FeatureCollection',
      features: [],
    };
  }

  isSelected(townName: string): boolean {
    return this.selectedTown === townName;
  }

  fitMapToMarkers(coordsArray: number[][]): void {
    const bounds = new LngLatBounds();

    coordsArray.forEach((coord) => {
      bounds.extend(coord as [number, number]);
    });

    const center = bounds.getCenter();
    this.lng = center.lng;
    this.lat = center.lat;
    this.zoom = 12;
  }

  ngOnDestroy(): void {
    console.log('Map component destroyed');
  }

  async toggleTown(townName: string) {
    if (this.selectedTown === townName) {
      this.selectedTown = '';
      this.showDropdown = false;
      this.clearMarkers();
    } else {
      this.selectedTown = townName;
      this.showDropdown = false;
      await this.loadTownData(townName);
    }
  }

  toggleDropdown() {
    this.showDropdown = !this.showDropdown;
    if (Object.keys(this.townColors).length === 0 && this.hdbTowns.length > 0) {
      this.assignColorsToTowns();
    }
  }

  displayPropertyData(): void {
    console.log('Displaying property data');
  }

  displaySaleListings(): void {
    if (this.activeTab === 'HDB') {
      if (this.showHDBListings) {
        this.showHDBListings = false;
        this.showCondoListings = false;
        this.clearMarkers();
        return;
      }
      this.showHDBListings = !this.showHDBListings;
      this.showCondoData = false;
      this.showCondoListings = false;
      if (this.showHDBListings) this.markSalesListingProperties('HDB');
      else this.clearMarkers();
    } else if (this.activeTab === 'Condo') {
      if (this.showCondoListings) {
        this.showCondoListings = false;
        this.showHDBListings = false;
        this.clearMarkers();
        return;
      }
      this.showCondoListings = !this.showCondoListings;
      this.showHDBListings = false;
      this.showCondoData = false;
      if (this.showCondoListings) this.markSalesListingProperties('Condo');
      else this.clearMarkers();
    }
  }

  markSalesListingProperties(type: 'HDB' | 'Condo'): void {
    // Clear existing markers
    this.clearMarkers();

    // Create GeoJSON features for the properties
    if (type === 'HDB') {
      console.log('Marking HDB sales listing properties');
      this.filteredPropertyData = this.propertyData;

      // Create features for each property with valid coordinates
      const features = this.filteredPropertyData
        .filter((property) => property.latitude && property.longitude)
        .map((property) => ({
          type: 'Feature',
          geometry: {
            type: 'Point',
            coordinates: [property.longitude, property.latitude],
          },
          properties: {
            id: property.blockNo,
            title: `${property.blockNo} ${property.street}, ${property.town}`,
            blockNumber: property.blockNo,
            streetAddress: property.street,
            town: property.town,
            postalCode: property.postalCode,
            salesPrice: property.salesPrice,
            type: 'hdb-sales',
            color: '#4CAF50', // Green color for sales listings
          },
        }));

      // Update the HDB source data
      this.hdbSourceData = {
        type: 'FeatureCollection',
        features,
      };

      // Fit map to markers if there are any
      if (features.length > 0) {
        this.fitMapToMarkers(features.map((f: any) => f.geometry.coordinates));
      }
    } else {
      console.log('Marking Condo sales listing properties');

      // Create features for each condo property with valid coordinates
      const features = this.condoData
        .filter((property) => property.latitude && property.longitude)
        .map((property) => ({
          type: 'Feature',
          geometry: {
            type: 'Point',
            coordinates: [property.longitude, property.latitude],
          },
          properties: {
            id: property.blockNo,
            title: `${property.projectName}, ${property.street}`,
            blockNumber: property.blockNo,
            projectName: property.projectName,
            streetAddress: property.street,
            town: property.town,
            postalCode: property.postalCode,
            salesPrice: property.salesPrice,
            type: 'condo-sales',
            color: '#FF5722', // Orange color for condo sales listings
          },
        }));

      this.condoSourceData = {
        type: 'FeatureCollection',
        features,
      };
      if (features.length > 0) {
        this.fitMapToMarkers(features.map((f: any) => f.geometry.coordinates));
      }
    }
  }
  private assignColorsToTowns(): void {
    this.townColors = {};
    this.hdbTowns.forEach((town, index) => {
      const townName = town; // Town name is at index 1
      this.townColors[townName] = this.townColorPalette[index % this.townColorPalette.length];
    });
    console.log('Town colors:', this.townColors);
  }

  async getPropertyDetails(feature: any): Promise<void> {
    console.log('FEATURE CLICK--->>>', feature);
    if (feature?.properties?.id) {
      const id = feature.properties.id;
      try {
        // Create a point feature for the selected property
        if (
          feature.geometry &&
          'coordinates' in feature.geometry &&
          Array.isArray(feature.geometry.coordinates)
        ) {
          this.selectedPropertyFeature = {
            type: 'Feature',
            geometry: {
              type: 'Point',
              coordinates: feature.geometry.coordinates as [number, number],
            },
            properties: {
              ...feature.properties,
            },
          };

          // Set property name for the marker label
          if (feature.properties.blockNumber && feature.properties.streetAddress) {
            this.selectedPropertyName = `Block ${feature.properties.blockNumber}, ${feature.properties.streetAddress}`;
          } else if (feature.properties.title) {
            this.selectedPropertyName = feature.properties.title;
          } else if (feature.properties.projectName) {
            this.selectedPropertyName = feature.properties.projectName;
          } else {
            this.selectedPropertyName = 'Selected Property';
          }

          // Store selected marker coordinates for highlighting
          this.selectedMarkerCoordinates = feature.geometry.coordinates as number[];

          // Update marker colors
          this.updateMarkerColors();
        }

        // Check the type of property based on the feature properties
        if (feature.properties.type === 'condo') {
          // Regular condo property
          const response = await this.#condoController.getById({ id });
          if (response.data) {
            this.toggleCondoPropertyModal(response.data);
          }
        } else if (feature.properties.type === 'condo-sales') {
          // Condo sales listing
          const property = this.condoData.find((p) => p.blockNo === id);
          if (property) {
            // this.#salesServiceModal.condoSalesOpen(property);
            this.showSalesPropertyDetails = true;
            this.salesProperty = property;
            this.showHDBListings = false;
            this.showCondoListings = false;
          }
        } else if (feature.properties.type === 'hdb-sales') {
          // HDB sales listing
          const property = this.propertyData.find((p) => p.blockNo === id);
          if (property) {
            // this.#salesServiceModal.open(property);
            const hdbResponse = await this.#hdbController.extractHDBTransactionData({
              latitude: Number(property.latitude),
              longitude: Number(property.longitude),
            });
            const formattedData = this.formatHDBTransactionTableData(hdbResponse?.data);
            const tableData = {
              heading: 'Transaction Details',
              header: ['Date', 'Type', 'Floor Level', 'Area (SQFT)', 'Price (S$)', 'PSF (S$)'],
              data: formattedData?.slice(0, 10),
            };
            const { transactionMedianPrices, transactionAveragePSF } =
              this.generateMonthlyMedianPricesAndAvgPSF(hdbResponse?.data);
            this.showSalesPropertyDetails = true;
            this.salesProperty = property;
            this.propertyTransactionData = tableData;
            this.transactionMedianPrices = transactionMedianPrices;
            this.transactionAveragePSF = transactionAveragePSF;
            this.showHDBListings = false;
            this.showCondoListings = false;
          }
        } else {
          // Regular HDB property
          const response = await this.#hdbController.getById({ id });
          console.log('HDB---RESPONSE', response);
          if (response.data) {
            this.togglePropertyModal(response.data);
          }
        }
      } catch (err) {
        console.error('Error fetching property details:', err);
      }
    }
  }
  public generateCondoMonthlyMedianPricesAndAvgPSF(data: any[]) {
    console.log('Generating condo monthly median prices and average PSF...', data);
    if (!data || data.length === 0) {
      return { transactionMedianPrices: [], transactionAveragePSF: [] };
    }

    const pricesByYearMonth = new Map<string, number[]>();
    const psfByYearMonth = new Map<string, number[]>();

    // Indices in the transaction array
    const DATE_INDEX = 0;
    const PRICE_INDEX = 4;
    const PSF_INDEX = 5;

    data.forEach((transaction) => {
      // Extract the date from the transaction array
      const dateStr = transaction[DATE_INDEX]; // Format: 'YYYY-MM-DD'
      if (!dateStr) return;

      // Convert to YYYY-MM format for grouping by month
      const yearMonth = dateStr.substring(0, 7); // Get YYYY-MM part

      // Extract price - remove $ and commas
      const priceStr = transaction[PRICE_INDEX]?.replace(/[$,]/g, '');
      const price = Number(priceStr);

      // Extract PSF - remove $ and commas
      const psfStr = transaction[PSF_INDEX]?.replace(/[$,]/g, '');
      const psf = Number(psfStr);
      console.log('PRICE AND PSF VALUES', psf, price, yearMonth);
      // Skip invalid entries
      if (isNaN(price) || isNaN(psf) || price <= 0 || psf <= 0) return;

      if (!pricesByYearMonth.has(yearMonth)) {
        pricesByYearMonth.set(yearMonth, []);
        psfByYearMonth.set(yearMonth, []);
      }

      pricesByYearMonth.get(yearMonth)!.push(price);
      psfByYearMonth.get(yearMonth)!.push(psf);
    });

    const result = Array.from(pricesByYearMonth.keys()).map((yearMonth) => {
      const prices = pricesByYearMonth.get(yearMonth)!;
      const psfs = psfByYearMonth.get(yearMonth)!;

      // Calculate median price
      const sortedPrices = [...prices].sort((a, b) => a - b);
      const median =
        sortedPrices.length % 2 === 0
          ? (sortedPrices[sortedPrices.length / 2 - 1] + sortedPrices[sortedPrices.length / 2]) / 2
          : sortedPrices[Math.floor(sortedPrices.length / 2)];

      // Calculate average PSF
      const avgPSF = psfs.reduce((sum, val) => sum + val, 0) / psfs.length;

      // Format date for display
      const [year, month] = yearMonth.split('-');
      const date = new Date(Number(year), Number(month) - 1);
      const formattedDate = date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
      });

      return {
        date: formattedDate,
        medianPrice: Math.round(median), // Round to nearest dollar
        averagePSF: Math.round(avgPSF), // Round to nearest dollar
      };
    });

    // Sort results chronologically
    const sortedResult = result.sort((a, b) => {
      const dateA = new Date(a.date);
      const dateB = new Date(b.date);
      return dateA.getTime() - dateB.getTime();
    });

    // Format data for charts
    const transactionMedianPrices = sortedResult.map(({ date, medianPrice }) => [
      date,
      medianPrice,
    ]);
    const transactionAveragePSF = sortedResult.map(({ date, averagePSF }) => [date, averagePSF]);

    return { transactionMedianPrices, transactionAveragePSF };
  }
  async toggleCondoPropertyModal(data: any) {
    try {
      console.log('Fetching Condo transaction data...');

      // First fetch transaction data
      let transactionData = null;
      // let chartData = null;

      if (data.location?.coordinates?.length === 2) {
        const [longitude, latitude] = data.location.coordinates;

        try {
          const response = await this.#condoController.getLatestTransactionsByLocation({
            lat: latitude.toString(),
            long: longitude.toString(),
          });

          if (response.success && response.data && response.data.length > 0) {
            console.log('Condo transaction data found:', response.data);

            // Format transaction data for table display
            transactionData = {
              heading: 'Transaction Details',
              header: ['Date', 'Type', 'Floor Level', 'Area (SQFT)', 'Price (S$)', 'PSF (S$)'],
              data: response.data.map((transaction: any) => [
                transaction.contract_date,
                transaction.property_type,
                transaction.floor_range,
                Math.round(transaction.area_sqm * 10.764), // Convert sqm to sqft
                `$${Number(transaction.price).toLocaleString()}`,
                `$${Math.round(transaction.price / (transaction.area_sqm * 10.764))}`,
              ]),
            };

            // Format data for chart
            // chartData = {
            //   heading: data.projectName,
            //   labels: response.data.map((t: any) => t.contract_date),
            //   datasets: [
            //     {
            //       label: 'Transaction Price',
            //       data: response.data.map((t: any) => t.price),
            //       borderColor: 'rgba(75, 192, 192, 1)',
            //       backgroundColor: 'rgba(75, 192, 192, 0.2)',
            //     },
            //   ],
            // };
          } else {
            console.log('No transaction data found');
          }
        } catch (error) {
          console.error('Error fetching transaction data:', error);
          this.#snackBarService.error('Error fetching transaction data');
        }
      }

      // Format property details
      const propertyDetails = {
        projectName: data.projectName,
        address: data.address,
        district: data.district,
        yearOfCompletion: data.yearOfCompletion || 'N/A',
        tenure: data.tenure || 'N/A',
        totalUnits: data.totalUnits || 'N/A',
        floorPlans: data.floorPlans || [],
        // Add any other relevant property details
      };

      const { transactionMedianPrices, transactionAveragePSF } =
        this.generateCondoMonthlyMedianPricesAndAvgPSF(transactionData?.data || []);

      // Update the component properties for the embedded panel
      this.selectedProperty = propertyDetails;
      this.propertyTransactionData = transactionData;
      this.transactionMedianPrices = transactionMedianPrices;
      this.transactionAveragePSF = transactionAveragePSF;
      this.showPropertyDetails = true;
      this.showTransactionChart = false;

      // Resize the map
      if (this.map) {
        setTimeout(() => {
          this.fitMapToMarkers([this.selectedProperty.geometry.coordinates]);
        }, 100);
      }
    } catch (error) {
      console.error('Error in toggleCondoPropertyModal:', error);
      this.#snackBarService.error('Error displaying property details');
    }
  }

  async openFilterForm() {
    const form: SilverField[] = [
      {
        controlType: SilverFieldTypes.SELECT,
        label: 'Town',
        key: 'town',
        value: null,
        nullable: true,
        valid: { required: false },
        multiple: true,
        options: this.hdbTowns,
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Minimum Price',
        placeholder: 'Minimum Price',
        type: 'number',
        key: 'minimumPrice',
        value: '',
        valid: { required: false },
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Maximum Price',
        placeholder: 'Maximum Price',
        type: 'number',
        key: 'maximumPrice',
        value: '',
        valid: { required: false },
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Postal Code',
        placeholder: 'Postal Code',
        type: 'text',
        key: 'postalCode',
        value: '',
        valid: { required: false },
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Street',
        placeholder: 'Street Name',
        type: 'text',
        key: 'street',
        value: '',
        valid: { required: false },
      },
    ];

    const result = await this.#formModalService.open({ heading: 'Filter Properties', form });
    if (!result.action) return;
    this.filter = result.value;
    // this.applySalesListingFilters();
  }
  centerMapOnProperty(property: any): void {
    if (!this.map || !property.latitude || !property.longitude) return;
    this.map.flyTo({
      center: [property.longitude, property.latitude],
      zoom: 16,
      essential: true,
    });
  }

  async findNearbyHDBProperties(type: 'userLocation' | 'searchLocation'): Promise<void> {
    if (!type) {
      this.#snackBarService.error('Please set your location first');
      return;
    }

    try {
      // Get user coordinates
      const coordinates = this[type].geometry.coordinates;
      const [longitude, latitude] = coordinates;
      const radius = this.selectedRadius * 1000; // Convert km to meters for MongoDB

      // Show loading indicator
      this.#snackBarService.info(`Finding HDB properties within ${this.selectedRadius} km...`);

      // Call HDB controller function
      const response = await this.#hdbController.findHDBPropertiesNearUserLocation({
        latitude,
        longitude,
        radius,
      });

      if (response.success && response.hdb.length > 0) {
        // Add markers for nearby HDB properties
        // Draw radius circle
        this.drawRadiusCircle([longitude, latitude], this.selectedRadius);
        this.addHDBMarkers(response.hdb);
        this.#snackBarService.success(
          `Found ${response.hdb.length} HDB properties within ${this.selectedRadius} km`,
        );
      } else {
        this.#snackBarService.info(`No HDB properties found within ${this.selectedRadius} km`);
      }
    } catch (error) {
      console.error('Error finding nearby HDB properties:', error);
      this.#snackBarService.error('Error finding nearby HDB properties');
    }
  }

  /**
   * Add HDB markers to the map
   */
  private addHDBMarkers(properties: any[]): void {
    // Create features for HDB properties
    const features = properties.map((property) => {
      // Extract coordinates
      const [lng, lat] = property.location.coordinates;
      console.log('HDB PROPERTY', property);
      if (property.postalCode && property.postalCode != null) {
        return {
          type: 'Feature',
          geometry: {
            type: 'Point',
            coordinates: [lng, lat],
          },
          properties: {
            id: property._id,
            title: property.title || `Block ${property.blockNumber}, ${property.streetAddress}`,
            blockNumber: property.blockNumber,
            streetAddress: property.streetAddress,
            town: property.town,
            color: this.townColors[property.town],
          },
        };
      }
    });

    // Update the HDB source
    this.hdbSourceData = {
      type: 'FeatureCollection',
      features,
    };
  }

  private addHospitalMarkers(hospitals: any[]): void {
    this.clearNearbyMarkers();
    const features = hospitals
      .map((hospital) => {
        if (hospital.location?.coordinates?.length === 2) {
          const [longitude, latitude] = hospital.location.coordinates;

          return {
            type: 'Feature',
            geometry: {
              type: 'Point',
              coordinates: [longitude, latitude],
            },
            properties: {
              icon: 'hospital',
              title: hospital.name,
              name: hospital.name,
              address: hospital.address || '',
              postal: hospital.postal || '',
              buildingName: hospital.buildingName || '',
              facilityType: hospital.facilityType || 'Hospital',
              services: hospital.services || [],
              type: 'healthcare',
            },
          };
        }
        return null;
      })
      .filter(Boolean) as GeoJSON.Feature[];

    // Update the schools source
    this.amenitiesSource = {
      type: 'FeatureCollection',
      features,
    };
  }

  private addMetroStationMarkers(metroStations: any[]): void {
    this.clearNearbyMarkers();
    const features = metroStations
      .map((metroStation) => {
        if (metroStation.location?.coordinates?.length === 2) {
          const [longitude, latitude] = metroStation.location.coordinates;

          return {
            type: 'Feature',
            geometry: {
              type: 'Point',
              coordinates: [longitude, latitude],
            },
            properties: {
              icon: 'metro',
              title: metroStation.stationName,
              stationName: metroStation.stationName,
              name: metroStation.stationName,
              buildingName: metroStation.buildingName || '',
              alphaNumericCode: metroStation.alphaNumericCode || '',
              address: metroStation.address || '',
              lines: metroStation.lines || [],
              interchange: metroStation.interchange || false,
              type: 'mrt',
              description: `
            <div class="p-4 max-w-xs text-sm text-gray-800">
              <div class="flex items-center mb-2">
                <img src="https://maps.google.com/mapfiles/kml/shapes/rail.png" class="w-5 h-5 mr-2" alt="Metro Icon" />
                <h3 class="text-base font-bold text-blue-700">${metroStation.stationName}</h3>
              </div>
              <div class="space-y-1">
                <p><span class="font-semibold">Code:</span> ${metroStation.alphaNumericCode || 'N/A'}</p>
                <p><span class="font-semibold">Address:</span> ${metroStation.address || 'N/A'}</p>
              </div>
            </div>
          `,
            },
          };
        }
        return null;
      })
      .filter(Boolean) as GeoJSON.Feature[];

    // Update the schools source
    this.amenitiesSource = {
      type: 'FeatureCollection',
      features,
    };
  }

  private addHawkerCenterMarkers(hawkers: any[]): void {
    // Clear existing bus stop markers;
    this.clearNearbyMarkers();

    const features = hawkers
      .map((hawker) => {
        if (hawker.location?.coordinates?.length === 2) {
          const [longitude, latitude] = hawker.location.coordinates;

          return {
            type: 'Feature',
            geometry: {
              type: 'Point',
              coordinates: [longitude, latitude],
            },
            properties: {
              icon: 'hawker',
              title: hawker.name,
              name: hawker.name,
              description: hawker.description || '',
              blockHouseNumber: hawker.blockHouseNumber || '',
              streetName: hawker.streetName || '',
              address:
                hawker.address ||
                (hawker.blockHouseNumber
                  ? `${hawker.blockHouseNumber} ${hawker.streetName || ''}`
                  : ''),
              postalCode: hawker.postalCode || '',
              cookedFoodStalls: hawker.cookedFoodStalls || '',
              photoUrl: hawker.photoUrl || '',
              type: 'hawker',
            },
          };
        }
        return null;
      })
      .filter(Boolean) as GeoJSON.Feature[];

    // Update the schools source
    this.amenitiesSource = {
      type: 'FeatureCollection',
      features,
    };
  }

  private addBusStopMarkers(busStops: any[]): void {
    // Clear existing bus stop markers;
    this.clearNearbyMarkers();

    const features = busStops
      .map((busStop) => {
        if (busStop.location?.coordinates?.length === 2) {
          const [longitude, latitude] = busStop.location.coordinates;

          return {
            type: 'Feature',
            style:
              'background-image: url(https://picsum.photos/id/870/50/50/); width: 50px; height: 50px',
            geometry: {
              type: 'Point',
              coordinates: [longitude, latitude],
            },
            properties: {
              icon: 'bus',
              title: busStop.name,
              name: busStop.name,
              details: busStop.details || '',
              busServices: busStop.busServices || [],
              type: 'busStop',
              description: `
            <div class="p-4">
              <h3 class="font-bold">${busStop.name}</h3>
              ${busStop.details ? `<p><span class="font-semibold">Landmark:</span> ${busStop.details}</p>` : ''}
              ${
                busStop.busServices && busStop.busServices.length > 0
                  ? `<p><span class="font-semibold">Bus Services:</span> ${busStop.busServices.join(', ')}</p>`
                  : ''
              }
            </div>
          `,
            },
          };
        }
        return null;
      })
      .filter(Boolean) as GeoJSON.Feature[];

    // Update the schools source
    this.amenitiesSource = {
      type: 'FeatureCollection',
      features,
    };
  }

  /**
   * Add school markers to the map
   */
  private addSchoolMarkers(schools: any[]): void {
    // Clear existing school markers
    this.clearNearbyMarkers();

    const features = schools
      .map((school) => {
        if (school.location?.coordinates?.length === 2) {
          const [longitude, latitude] = school.location.coordinates;

          return {
            type: 'Feature',
            geometry: {
              type: 'Point',
              coordinates: [longitude, latitude],
            },
            properties: {
              icon: 'school',
              title: school.name,
              schoolName: school.schoolName,
              address: school.address,
              postal: school.postal,
              telephoneNumber: school.telephoneNumber || 'N/A',
              email: school.email || 'N/A',
              town: school.town,
              schoolLevel: school.schoolLevel,
              schoolType: school.schoolType,
              type: 'school',
              description: `
            <div class="p-2 max-w-md text-sm text-gray-800">
              <h3 class="text-lg text-center font-bold text-blue-700 mb-1">${school.schoolName}</h3>
              <div class="space-y-1">
                <p><span class="font-semibold py-1">Address:</span> ${school.address}</p>
                <p><span class="font-semibold py-1">Postal Code:</span> ${school.postal}</p>
                <p><span class="font-semibold py-1">Telephone:</span> ${school.telephoneNumber || 'N/A'}</p>
                <p>
                  <span class="font-semibold py-1">Email:</span>
                  <a href="mailto:${school.email}" class="text-blue-600 underline">${school.email || 'N/A'}</a>
                </p>
                <p><span class="font-semibold py-1">Town:</span> ${school.town}</p>
                <p><span class="font-semibold py-1">Level:</span> ${school.schoolLevel}</p>
                <p><span class="font-semibold py-1">Type:</span> ${school.schoolType}</p>
              </div>
            </div>
          `,
            },
          };
        }
        return null;
      })
      .filter(Boolean) as GeoJSON.Feature[];

    // Update the schools source
    this.amenitiesSource = {
      type: 'FeatureCollection',
      features,
    };
  }
  /**
   * Draw a radius circle around the user's location
   */
  private drawRadiusCircle(center: [number, number], radiusInKm: number): void {
    this.radiusCircleSource = {
      type: 'FeatureCollection',
      features: [],
    };
    const points = 64;
    const km = radiusInKm;
    const earthRadius = 6371;
    const [centerLng, centerLat] = center;
    const centerLatRad = (centerLat * Math.PI) / 180;
    const centerLngRad = (centerLng * Math.PI) / 180;

    // Create points for the circle
    const coordinates = [];
    for (let i = 0; i <= points; i++) {
      const angle = (i * 2 * Math.PI) / points;
      const dx = (km / earthRadius) * Math.cos(angle);
      const dy = (km / earthRadius) * Math.sin(angle);

      const latRad = centerLatRad + dy;
      const lngRad = centerLngRad + dx / Math.cos(centerLatRad);

      const lat = (latRad * 180) / Math.PI;
      const lng = (lngRad * 180) / Math.PI;
      coordinates.push([lng, lat]);
    }

    // Create a polygon feature for the circle
    const circleFeature: GeoJSON.Feature = {
      type: 'Feature',
      geometry: {
        type: 'Polygon',
        coordinates: [coordinates],
      },
      properties: {
        radius: radiusInKm,
        center: center,
      },
    };

    // Update the radius circle source
    this.radiusCircleSource = {
      type: 'FeatureCollection',
      features: [circleFeature],
    };
  }
  /**
   * Clear nearby markers of a specific type
   */
  private clearNearbyMarkers(): void {
    this.amenitiesSource = {
      type: 'FeatureCollection',
      features: [],
    };

    // Clear radius circle if no nearby options are active
    const anyActive = Object.values(this.nearbyOptions).some((value) => value);
    if (!anyActive) {
      this.radiusCircleSource = {
        type: 'FeatureCollection',
        features: [],
      };
    }
  }

  updateLocationRadius(radius: number, type: 'userLocation' | 'searchLocation'): void {
    this.selectedRadius = radius;

    const nearbyKeysToUpdate = [
      'schools',
      'hawker',
      'busStops',
      'mrt',
      'salesListings',
      'hdbData',
      'healthcare',
    ];

    for (const key of nearbyKeysToUpdate) {
      if (this.nearbyOptions[key]) {
        this.handleNearbyOptionChange(key, true, type);
      }
    }
  }

  getAmenitiesPopUp(event: any): void {
    // Extract the feature data from the event
    const feature = event.features[0];

    if (!feature) {
      console.error('No feature found in event');
      return;
    }

    console.log('FEATURE', feature);

    // Extract properties from the feature
    const properties = feature.properties || {};
    const amenityType = properties.type || 'unknown';

    // Create a structured object based on amenity type
    let amenityData: any = {
      type: amenityType,
      name:
        properties.name ||
        properties.schoolName ||
        properties.stationName ||
        properties.buildingName ||
        'Unknown',
      coordinates: feature.geometry.coordinates,
    };

    // Add type-specific properties
    switch (amenityType) {
      case 'school':
        amenityData = {
          ...amenityData,
          address: properties.address || '',
          postal: properties.postal || '',
          telephoneNumber: properties.telephoneNumber || '',
          email: properties.email || '',
          town: properties.town || '',
          schoolLevel: properties.schoolLevel || '',
          schoolType: properties.schoolType || '',
          category: 'Education',
        };
        break;

      case 'mrt':
        amenityData = {
          ...amenityData,
          buildingName: properties.buildingName || '',
          alphaNumericCode: properties.alphaNumericCode || '',
          address: properties.address || '',
          lines: properties.lines || [],
          interchange: properties.interchange || false,
          category: 'Transportation',
        };
        break;

      case 'healthcare':
        amenityData = {
          ...amenityData,
          buildingName: properties.buildingName || '',
          address: properties.address || '',
          postal: properties.postal || '',
          facilityType: properties.facilityType || 'Healthcare Facility',
          services: properties.services || [],
          category: 'Healthcare',
        };
        break;

      case 'busStop':
        amenityData = {
          ...amenityData,
          details: properties.details || '',
          busServices: properties.busServices || [],
          category: 'Transportation',
        };
        break;

      case 'hawker':
        amenityData = {
          ...amenityData,
          description: properties.description || '',
          address: properties.blockHouseNumber
            ? `${properties.blockHouseNumber} ${properties.streetName || ''}`
            : properties.address || '',
          postalCode: properties.postalCode || '',
          stallCount: properties.cookedFoodStalls || '',
          photoUrl: properties.photoUrl || '',
          category: 'Food & Dining',
        };
        break;

      default:
        amenityData = {
          ...amenityData,
          address: properties.address || '',
          description: properties.description || '',
          category: 'Point of Interest',
        };
    }

    // Store the amenity data for the popup
    this.amenitiesPopUpContent = amenityData;

    // Create a GeoJSON feature for the popup
    const popupFeature: GeoJSON.Feature<GeoJSON.Point> = {
      type: 'Feature',
      geometry: {
        type: 'Point',
        coordinates: feature.geometry.coordinates,
      },
      properties: {},
    };

    // Set the popup feature and show it
    this.amenitiesFeature = popupFeature;
    this.showAmenitiesPopup = true;
  }
  getMRTLineColor(line: string): string {
    console.log(line);
    const lineColors: Record<string, string> = {
      NS: '#e12323', // North-South Line (Red)
      EW: '#009645', // East-West Line (Green)
      CG: '#009645', // East-West Line - Changi Airport Branch (Green)
      NE: '#9900aa', // North-East Line (Purple)
      CC: '#fa9e0d', // Circle Line (Orange)
      CE: '#fa9e0d', // Circle Line Extension (Orange)
      DT: '#005ec4', // Downtown Line (Blue)
      TE: '#9D5714', // Thomson-East Coast Line (Brown)
      JR: '#0099aa', // Jurong Region Line (Teal)
      CR: '#97C616', // Cross Island Line (Lime Green)
      BP: '#888888', // Bukit Panjang LRT (Light Grey)
      SK: '#888888', // Sengkang LRT (Light Grey)
      PG: '#888888', // Punggol LRT (Light Grey)
      // Default color if line is not recognized
      default: '#888888',
    };

    // Return the color for the line, or the default color if not found
    return lineColors[line] || lineColors['default'];
  }
  toggleMoreInformationPopup(): void {
    this.showMoreInfoPopUp = !this.showMoreInfoPopUp;
  }
  toggleTransactionChart(): void {
    this.showTransactionChart = !this.showTransactionChart;
    this.chartsInitialized = false; // Reset chart initialization flag
  }

  // Close property details panel
  closePropertyDetails(): void {
    this.showPropertyDetails = false;
    this.selectedProperty = null;
    this.propertyTransactionData = null;
    this.showTransactionChart = false;
    this.selectedMarkerCoordinates = null;

    // Reset marker colors
    this.updateMarkerColors();

    // Destroy charts if they exist
    this.destroyCharts();

    // Resize the map
    if (this.map) {
      setTimeout(() => {
        this.fitMapToMarkers([this.selectedProperty.geometry.coordinates]);
      }, 100);
    }
  }

  // Implement AfterViewChecked to initialize charts after the view is rendered
  ngAfterViewChecked(): void {
    if (
      this.showTransactionChart &&
      !this.chartsInitialized &&
      this.medianPriceChartRef &&
      this.avgPsfChartRef
    ) {
      this.initializeCharts();
    }
  }

  // Initialize charts
  private initializeCharts(): void {
    if (
      !this.medianPriceChartRef ||
      !this.avgPsfChartRef ||
      !this.transactionMedianPrices ||
      !this.transactionAveragePSF ||
      this.transactionMedianPrices.length === 0
    ) {
      return;
    }

    // Destroy existing charts if they exist
    this.destroyCharts();

    // Extract data for charts
    const labels = this.transactionMedianPrices.map((entry) => entry[0]);
    const medianPrices = this.transactionMedianPrices.map((entry) => entry[1]);
    const avgPSFs = this.transactionAveragePSF.map((entry) => entry[1]);

    // Create median price chart
    this.medianPriceChart = new Chart(this.medianPriceChartRef.nativeElement, {
      type: 'line',
      data: {
        labels,
        datasets: [
          {
            label: 'Median Price',
            data: medianPrices,
            fill: false,
            tension: 0.2,
            borderColor: '#4f46e5',
            backgroundColor: 'rgba(79, 70, 229, 0.2)',
          },
        ],
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: false,
            ticks: {
              callback: (value) => {
                return '$' + value.toLocaleString();
              },
            },
          },
        },
      },
    });

    // Create average PSF chart
    this.avgPsfChart = new Chart(this.avgPsfChartRef.nativeElement, {
      type: 'line',
      data: {
        labels,
        datasets: [
          {
            label: 'Average PSF',
            data: avgPSFs,
            fill: false,
            tension: 0.2,
            borderColor: '#10b981',
            backgroundColor: 'rgba(16, 185, 129, 0.2)',
          },
        ],
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: false,
            ticks: {
              callback: (value) => {
                return '$' + value.toLocaleString();
              },
            },
          },
        },
      },
    });

    this.chartsInitialized = true;
  }

  // Destroy charts
  private destroyCharts(): void {
    if (this.medianPriceChart) {
      this.medianPriceChart.destroy();
      this.medianPriceChart = undefined;
    }

    if (this.avgPsfChart) {
      this.avgPsfChart.destroy();
      this.avgPsfChart = undefined;
    }

    this.chartsInitialized = false;
  }
  private updateMarkerColors(): void {
    // Update HDB markers
    if (this.hdbSourceData && this.hdbSourceData.features) {
      console.log('Updating marker colors', this.selectedMarkerCoordinates);
      this.hdbSourceData.features = this.hdbSourceData.features.map((feature) => {
        if (
          this.selectedMarkerCoordinates &&
          feature.geometry.coordinates[0] === this.selectedMarkerCoordinates[0] &&
          feature.geometry.coordinates[1] === this.selectedMarkerCoordinates[1]
        ) {
          // Highlight the selected marker
          return {
            ...feature,
            properties: {
              ...feature.properties,
              color: '#FF0000', // Bright red for selected marker
              selectedMarker: true,
            },
          };
        } else {
          // Reset color for other markers
          return {
            ...feature,
            properties: {
              ...feature.properties,
              color: feature.properties.color || '#3FB1CE',
              selectedMarker: false,
            },
          };
        }
      });
    }

    // Update Condo markers
    if (this.condoSourceData && this.condoSourceData.features) {
      this.condoSourceData.features = this.condoSourceData.features.map((feature) => {
        if (
          this.selectedMarkerCoordinates &&
          feature.geometry.coordinates[0] === this.selectedMarkerCoordinates[0] &&
          feature.geometry.coordinates[1] === this.selectedMarkerCoordinates[1]
        ) {
          // Highlight the selected marker
          return {
            ...feature,
            properties: {
              ...feature.properties,
              color: '#FF0000', // Bright red for selected marker
              selectedMarker: true,
            },
          };
        } else {
          // Reset color for other markers
          return {
            ...feature,
            properties: {
              ...feature.properties,
              color: feature.properties.originalColor || feature.properties.color || '#FF5722',
              selectedMarker: false,
            },
          };
        }
      });
    }
  }

  shouldShowCancelButton(): boolean {
    // alert("FUNCTION CALLED");
    if (
      this.amenitiesSource?.features?.length > 0 ||
      this.userLocation !== null ||
      this.searchLocation !== null ||
      this.condoSourceData?.features?.length > 0 ||
      (this.hdbSourceData?.features?.length > 0 && this.hdbSourceData.features.length > 1)
    ) {
      console.log('Update UI to show cancel button');
      return true;
    } else {
      return false;
    }
  }

  /**
   * Clears all markers from the map
   */
  clearAllMarkers(): void {
    // Clear amenities markers
    this.clearNearbyMarkers();

    // Clear radius circle
    this.radiusCircleSource = {
      type: 'FeatureCollection',
      features: [],
    };

    // Clear user location
    if (this.userLocation) {
      this.userLocationSource = {
        type: 'FeatureCollection',
        features: [],
      };
      this.userLocation = null;
      this.showUserLocationPopup = false;
    }

    this.hdbSourceData = {
      type: 'FeatureCollection',
      features: [],
    };

    this.condoSourceData = {
      type: 'FeatureCollection',
      features: [],
    };

    // Clear search location
    if (this.searchLocation) {
      this.searchLocationSource = {
        type: 'FeatureCollection',
        features: [],
      };
      this.searchLocation = null;
      this.showSearchLocationPopup = false;
      this.showMoreInfoPopUp = false;
    }

    // Clear selected property
    this.selectedPropertyFeature = null;
    this.selectedPropertyName = '';
    this.selectedMarkerCoordinates = null;

    // Clear HDB and condo markers
    this.clearMarkers();

    // Reset nearby options
    Object.keys(this.nearbyOptions).forEach((key) => {
      this.nearbyOptions[key as keyof typeof this.nearbyOptions] = false;
    });
    // Reset map zoom and center to default Singapore view
    if (this.map) {
      this.map.flyTo({
        center: [103.8198, 1.3521], // Singapore coordinates
        zoom: 11,
        essential: true,
        duration: 1000, // Animation duration in milliseconds
      });
    }
    this.#snackBarService.info('All markers cleared');
  }
  goToListingPage(): void {
    if (this.salesProperty) {
      this.#router.navigate(['/buyer-seller/preview'], {
        state: { data: this.salesProperty },
      });
    }
  }
}
