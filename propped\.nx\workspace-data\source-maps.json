{"packages/api": {"root": ["packages/api/project.json", "nx/core/project-json"], "targets": ["packages/api/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint": ["packages/api/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.cache": ["packages/api/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.options": ["packages/api/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.inputs": ["packages/api/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.outputs": ["packages/api/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata": ["packages/api/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.executor": ["packages/api/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.options.cwd": ["packages/api/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.options.command": ["packages/api/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.technologies": ["packages/api/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.technologies.0": ["packages/api/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.description": ["packages/api/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.help": ["packages/api/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.help.command": ["packages/api/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.help.example": ["packages/api/eslint.config.mjs", "@nx/eslint/plugin"], "name": ["packages/api/project.json", "nx/core/project-json"], "$schema": ["packages/api/project.json", "nx/core/project-json"], "sourceRoot": ["packages/api/project.json", "nx/core/project-json"], "projectType": ["packages/api/project.json", "nx/core/project-json"], "tags": ["packages/api/project.json", "nx/core/project-json"], "targets.build": ["packages/api/project.json", "nx/core/project-json"], "targets.build.executor": ["packages/api/project.json", "nx/core/project-json"], "targets.build.outputs": ["packages/api/project.json", "nx/core/project-json"], "targets.build.defaultConfiguration": ["packages/api/project.json", "nx/core/project-json"], "targets.build.options": ["packages/api/project.json", "nx/core/project-json"], "targets.build.configurations": ["packages/api/project.json", "nx/core/project-json"], "targets.build.options.platform": ["packages/api/project.json", "nx/core/project-json"], "targets.build.options.outputPath": ["packages/api/project.json", "nx/core/project-json"], "targets.build.options.format": ["packages/api/project.json", "nx/core/project-json"], "targets.build.options.bundle": ["packages/api/project.json", "nx/core/project-json"], "targets.build.options.main": ["packages/api/project.json", "nx/core/project-json"], "targets.build.options.tsConfig": ["packages/api/project.json", "nx/core/project-json"], "targets.build.options.assets": ["packages/api/project.json", "nx/core/project-json"], "targets.build.options.generatePackageJson": ["packages/api/project.json", "nx/core/project-json"], "targets.build.options.esbuildOptions": ["packages/api/project.json", "nx/core/project-json"], "targets.build.configurations.development": ["packages/api/project.json", "nx/core/project-json"], "targets.build.configurations.production": ["packages/api/project.json", "nx/core/project-json"], "targets.build.configurations.production.esbuildOptions": ["packages/api/project.json", "nx/core/project-json"], "targets.serve": ["packages/api/project.json", "nx/core/project-json"], "targets.serve.executor": ["packages/api/project.json", "nx/core/project-json"], "targets.serve.defaultConfiguration": ["packages/api/project.json", "nx/core/project-json"], "targets.serve.options": ["packages/api/project.json", "nx/core/project-json"], "targets.serve.configurations": ["packages/api/project.json", "nx/core/project-json"], "targets.serve.options.buildTarget": ["packages/api/project.json", "nx/core/project-json"], "targets.serve.configurations.development": ["packages/api/project.json", "nx/core/project-json"], "targets.serve.configurations.development.buildTarget": ["packages/api/project.json", "nx/core/project-json"], "targets.serve.configurations.production": ["packages/api/project.json", "nx/core/project-json"], "targets.serve.configurations.production.buildTarget": ["packages/api/project.json", "nx/core/project-json"], "targets.lint": ["packages/api/project.json", "nx/core/project-json"], "targets.lint.executor": ["packages/api/project.json", "nx/core/project-json"], "targets.lint.cache": ["nx.json", "nx/target-defaults"], "targets.lint.inputs": ["nx.json", "nx/target-defaults"], "targets.lint.parallelism": ["nx.json", "nx/target-defaults"]}, "packages/shared": {"root": ["packages/shared/project.json", "nx/core/project-json"], "targets": ["packages/shared/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint": ["packages/shared/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.cache": ["packages/shared/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.options": ["packages/shared/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.inputs": ["packages/shared/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.outputs": ["packages/shared/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata": ["packages/shared/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.executor": ["packages/shared/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.options.cwd": ["packages/shared/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.options.command": ["packages/shared/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.technologies": ["packages/shared/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.technologies.0": ["packages/shared/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.description": ["packages/shared/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.help": ["packages/shared/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.help.command": ["packages/shared/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.help.example": ["packages/shared/eslint.config.mjs", "@nx/eslint/plugin"], "name": ["packages/shared/project.json", "nx/core/project-json"], "$schema": ["packages/shared/project.json", "nx/core/project-json"], "sourceRoot": ["packages/shared/project.json", "nx/core/project-json"], "projectType": ["packages/shared/project.json", "nx/core/project-json"], "tags": ["packages/shared/project.json", "nx/core/project-json"], "tags.scope:lib": ["packages/shared/project.json", "nx/core/project-json"], "targets.lint": ["packages/shared/project.json", "nx/core/project-json"], "targets.lint.executor": ["packages/shared/project.json", "nx/core/project-json"], "targets.lint.outputs": ["packages/shared/project.json", "nx/core/project-json"], "targets.lint.options": ["packages/shared/project.json", "nx/core/project-json"], "targets.lint.options.lintFilePatterns": ["packages/shared/project.json", "nx/core/project-json"], "targets.test": ["packages/shared/project.json", "nx/core/project-json"], "targets.test.executor": ["packages/shared/project.json", "nx/core/project-json"], "targets.test.outputs": ["packages/shared/project.json", "nx/core/project-json"], "targets.test.options": ["packages/shared/project.json", "nx/core/project-json"], "targets.test.configurations": ["packages/shared/project.json", "nx/core/project-json"], "targets.test.options.jestConfig": ["packages/shared/project.json", "nx/core/project-json"], "targets.test.options.passWithNoTests": ["packages/shared/project.json", "nx/core/project-json"], "targets.test.configurations.ci": ["packages/shared/project.json", "nx/core/project-json"], "targets.test.configurations.ci.ci": ["packages/shared/project.json", "nx/core/project-json"], "targets.test.configurations.ci.codeCoverage": ["packages/shared/project.json", "nx/core/project-json"]}, "packages/web": {"root": ["packages/web/project.json", "nx/core/project-json"], "targets": ["packages/web/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint": ["packages/web/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.cache": ["packages/web/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.options": ["packages/web/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.inputs": ["packages/web/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.outputs": ["packages/web/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata": ["packages/web/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.executor": ["packages/web/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.options.cwd": ["packages/web/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.options.command": ["packages/web/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.technologies": ["packages/web/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.technologies.0": ["packages/web/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.description": ["packages/web/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.help": ["packages/web/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.help.command": ["packages/web/eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.help.example": ["packages/web/eslint.config.mjs", "@nx/eslint/plugin"], "name": ["packages/web/project.json", "nx/core/project-json"], "$schema": ["packages/web/project.json", "nx/core/project-json"], "projectType": ["packages/web/project.json", "nx/core/project-json"], "prefix": ["packages/web/project.json", "nx/core/project-json"], "sourceRoot": ["packages/web/project.json", "nx/core/project-json"], "tags": ["packages/web/project.json", "nx/core/project-json"], "targets.build": ["packages/web/project.json", "nx/core/project-json"], "targets.build.executor": ["packages/web/project.json", "nx/core/project-json"], "targets.build.outputs": ["packages/web/project.json", "nx/core/project-json"], "targets.build.options": ["packages/web/project.json", "nx/core/project-json"], "targets.build.configurations": ["packages/web/project.json", "nx/core/project-json"], "targets.build.defaultConfiguration": ["packages/web/project.json", "nx/core/project-json"], "targets.build.options.outputPath": ["packages/web/project.json", "nx/core/project-json"], "targets.build.options.index": ["packages/web/project.json", "nx/core/project-json"], "targets.build.options.browser": ["packages/web/project.json", "nx/core/project-json"], "targets.build.options.polyfills": ["packages/web/project.json", "nx/core/project-json"], "targets.build.options.tsConfig": ["packages/web/project.json", "nx/core/project-json"], "targets.build.options.inlineStyleLanguage": ["packages/web/project.json", "nx/core/project-json"], "targets.build.options.assets": ["packages/web/project.json", "nx/core/project-json"], "targets.build.options.styles": ["packages/web/project.json", "nx/core/project-json"], "targets.build.options.scripts": ["packages/web/project.json", "nx/core/project-json"], "targets.build.configurations.prod": ["packages/web/project.json", "nx/core/project-json"], "targets.build.configurations.prod.budgets": ["packages/web/project.json", "nx/core/project-json"], "targets.build.configurations.prod.outputHashing": ["packages/web/project.json", "nx/core/project-json"], "targets.build.configurations.prod.fileReplacements": ["packages/web/project.json", "nx/core/project-json"], "targets.build.configurations.test": ["packages/web/project.json", "nx/core/project-json"], "targets.build.configurations.test.budgets": ["packages/web/project.json", "nx/core/project-json"], "targets.build.configurations.test.outputHashing": ["packages/web/project.json", "nx/core/project-json"], "targets.build.configurations.test.fileReplacements": ["packages/web/project.json", "nx/core/project-json"], "targets.build.configurations.development": ["packages/web/project.json", "nx/core/project-json"], "targets.build.configurations.development.optimization": ["packages/web/project.json", "nx/core/project-json"], "targets.build.configurations.development.extractLicenses": ["packages/web/project.json", "nx/core/project-json"], "targets.build.configurations.development.sourceMap": ["packages/web/project.json", "nx/core/project-json"], "targets.build.configurations.development.assets": ["packages/web/project.json", "nx/core/project-json"], "targets.serve": ["packages/web/project.json", "nx/core/project-json"], "targets.serve.executor": ["packages/web/project.json", "nx/core/project-json"], "targets.serve.configurations": ["packages/web/project.json", "nx/core/project-json"], "targets.serve.defaultConfiguration": ["packages/web/project.json", "nx/core/project-json"], "targets.serve.options": ["packages/web/project.json", "nx/core/project-json"], "targets.serve.options.proxyConfig": ["packages/web/project.json", "nx/core/project-json"], "targets.serve.configurations.production": ["packages/web/project.json", "nx/core/project-json"], "targets.serve.configurations.production.buildTarget": ["packages/web/project.json", "nx/core/project-json"], "targets.serve.configurations.development": ["packages/web/project.json", "nx/core/project-json"], "targets.serve.configurations.development.buildTarget": ["packages/web/project.json", "nx/core/project-json"], "targets.extract-i18n": ["packages/web/project.json", "nx/core/project-json"], "targets.extract-i18n.executor": ["packages/web/project.json", "nx/core/project-json"], "targets.extract-i18n.options": ["packages/web/project.json", "nx/core/project-json"], "targets.extract-i18n.options.buildTarget": ["packages/web/project.json", "nx/core/project-json"], "targets.lint": ["packages/web/project.json", "nx/core/project-json"], "targets.lint.executor": ["packages/web/project.json", "nx/core/project-json"], "targets.serve-static": ["packages/web/project.json", "nx/core/project-json"], "targets.serve-static.executor": ["packages/web/project.json", "nx/core/project-json"], "targets.serve-static.options": ["packages/web/project.json", "nx/core/project-json"], "targets.serve-static.options.buildTarget": ["packages/web/project.json", "nx/core/project-json"], "targets.serve-static.options.staticFilePath": ["packages/web/project.json", "nx/core/project-json"], "targets.serve-static.options.spa": ["packages/web/project.json", "nx/core/project-json"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.lint.cache": ["nx.json", "nx/target-defaults"], "targets.lint.inputs": ["nx.json", "nx/target-defaults"], "targets.lint.parallelism": ["nx.json", "nx/target-defaults"]}}