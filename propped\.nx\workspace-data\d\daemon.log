[NX Daemon Server] - 2025-06-17T07:36:08.848Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\7ca78b1b0c7c2317476f\d.sock
[NX Daemon Server] - 2025-06-17T07:36:08.901Z - [WATCHER]: Subscribed to changes within: C:\Users\<USER>\Desktop\New folder\propped (native)
[NX Daemon Server] - 2025-06-17T07:36:08.908Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-06-17T07:36:08.910Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-06-17T07:36:08.919Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-06-17T07:36:08.923Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-06-17T07:36:10.209Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\Desktop\New folder\propped\node_modules\.pnpm\nx@20.4.4_@swc-node+registe_a4ae593b794f0df45b1ca168e84917b5\node_modules\nx\src\plugins\package-json' 1208.6895999999997ms
[NX Daemon Server] - 2025-06-17T07:36:10.235Z - Time taken for 'loadDefaultNxPlugins' 1243.0729ms
[NX Daemon Server] - 2025-06-17T07:36:12.168Z - Time taken for 'Load Nx Plugin: @nx/eslint/plugin' 3201.8442ms
[NX Daemon Server] - 2025-06-17T07:36:13.140Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-06-17T07:36:13.140Z - [REQUEST]: 
[NX Daemon Server] - 2025-06-17T07:36:13.140Z - [REQUEST]: 
[NX Daemon Server] - 2025-06-17T07:36:13.163Z - Time taken for 'loadSpecifiedNxPlugins' 4208.6828ms
[NX Daemon Server] - 2025-06-17T07:36:13.169Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-06-17T07:36:13.170Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-06-17T07:36:13.171Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-06-17T07:36:13.177Z - [REQUEST]: Responding to the client. handleGlob
[NX Daemon Server] - 2025-06-17T07:36:13.183Z - Done responding to the client handleGlob
[NX Daemon Server] - 2025-06-17T07:36:13.183Z - Handled GLOB. Handling time: 4. Response time: 6.
[NX Daemon Server] - 2025-06-17T07:36:14.605Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-06-17T07:36:14.606Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-06-17T07:36:14.606Z - Handled HASH_GLOB. Handling time: 7. Response time: 1.
[NX Daemon Server] - 2025-06-17T07:36:14.612Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-06-17T07:36:14.613Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-06-17T07:36:14.613Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-06-17T07:36:14.639Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-06-17T07:36:14.640Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-06-17T07:36:14.640Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-06-17T07:36:22.153Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-06-17T07:36:22.154Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-06-17T07:36:22.154Z - Handled HASH_GLOB. Handling time: 4. Response time: 1.
[NX Daemon Server] - 2025-06-17T07:36:24.106Z - Time taken for 'build-project-configs' 10897.5086ms
[NX Daemon Server] - 2025-06-17T07:36:25.366Z - [SYNC]: collect registered sync generators
[NX Daemon Server] - 2025-06-17T07:36:25.369Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-06-17T07:36:25.371Z - Time taken for 'total for creating and serializing project graph' 16445.2045ms
[NX Daemon Server] - 2025-06-17T07:36:25.377Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-06-17T07:36:25.377Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 16446. Response time: 8.
[NX Daemon Server] - 2025-06-17T07:36:25.414Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX Daemon Server] - 2025-06-17T07:36:25.414Z - Time taken for 'preTasksExecution' 1.5864999999976135ms
[NX Daemon Server] - 2025-06-17T07:36:25.415Z - Done responding to the client handleRunPreTasksExecution
[NX Daemon Server] - 2025-06-17T07:36:25.415Z - Handled PRE_TASKS_EXECUTION. Handling time: 2. Response time: 1.
[NX Daemon Server] - 2025-06-17T07:36:25.613Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-06-17T07:36:25.614Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-06-17T07:36:25.614Z - Handled HASH_TASKS. Handling time: 39. Response time: 1.
[NX Daemon Server] - 2025-06-17T07:36:25.647Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-06-17T07:36:25.648Z - Done responding to the client handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-06-17T07:36:25.648Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 4. Response time: 1.
[NX Daemon Server] - 2025-06-17T07:36:27.683Z - Established a connection. Number of open connections: 3
[NX Daemon Server] - 2025-06-17T07:36:27.684Z - Closed a connection. Number of open connections: 2
[NX Daemon Server] - 2025-06-17T07:36:27.686Z - Established a connection. Number of open connections: 3
[NX Daemon Server] - 2025-06-17T07:36:27.691Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-06-17T07:36:27.692Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-06-17T07:36:27.695Z - Time taken for 'total for creating and serializing project graph' 0.8136999999987893ms
[NX Daemon Server] - 2025-06-17T07:36:27.699Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-06-17T07:36:27.699Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 7.
[NX Daemon Server] - 2025-06-17T07:36:56.747Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-06-17T07:39:32.948Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-06-17T07:39:32.965Z - [WATCHER]: packages/web/src/environments/envi was modified
[NX Daemon Server] - 2025-06-17T07:39:33.083Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-06-17T07:39:33.083Z - [REQUEST]: packages/web/src/environments/envi
[NX Daemon Server] - 2025-06-17T07:39:33.084Z - [REQUEST]: 
[NX Daemon Server] - 2025-06-17T07:39:33.176Z - [REQUEST]: Responding to the client. handleGlob
[NX Daemon Server] - 2025-06-17T07:39:33.178Z - Time taken for 'hash changed files from watcher' 6.463500000012573ms
[NX Daemon Server] - 2025-06-17T07:39:33.179Z - Done responding to the client handleGlob
[NX Daemon Server] - 2025-06-17T07:39:33.179Z - Handled GLOB. Handling time: 6. Response time: 3.
[NX Daemon Server] - 2025-06-17T07:39:33.195Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-06-17T07:39:33.196Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-06-17T07:39:33.196Z - Handled HASH_GLOB. Handling time: 8. Response time: 1.
[NX Daemon Server] - 2025-06-17T07:39:33.210Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-06-17T07:39:33.239Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-06-17T07:39:33.240Z - Handled HASH_GLOB. Handling time: 9. Response time: 30.
[NX Daemon Server] - 2025-06-17T07:39:33.615Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-06-17T07:39:33.615Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-06-17T07:39:33.618Z - Handled HASH_GLOB. Handling time: 4. Response time: 3.
[NX Daemon Server] - 2025-06-17T07:39:33.624Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-06-17T07:39:33.624Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-06-17T07:39:33.625Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
[NX Daemon Server] - 2025-06-17T07:39:33.800Z - Time taken for 'build-project-configs' 657.6117000000086ms
[NX Daemon Server] - 2025-06-17T07:39:34.107Z - [SYNC]: collect registered sync generators
[NX Daemon Server] - 2025-06-17T07:39:34.109Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX Daemon Server] - 2025-06-17T07:39:34.109Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX Daemon Server] - 2025-06-17T07:39:34.109Z - Time taken for 'total execution time for createProjectGraph()' 232.73700000002282ms
[NX Daemon Server] - 2025-06-17T07:39:43.186Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-06-17T07:39:43.190Z - [WATCHER]: packages/web/src/environments/environment.eg copy.ts was modified
[NX Daemon Server] - 2025-06-17T07:39:43.407Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-06-17T07:39:43.408Z - [REQUEST]: packages/web/src/environments/environment.eg copy.ts
[NX Daemon Server] - 2025-06-17T07:39:43.408Z - [REQUEST]: 
[NX Daemon Server] - 2025-06-17T07:39:43.447Z - [REQUEST]: Responding to the client. handleGlob
[NX Daemon Server] - 2025-06-17T07:39:43.447Z - Time taken for 'hash changed files from watcher' 1.6803000000072643ms
[NX Daemon Server] - 2025-06-17T07:39:43.450Z - Done responding to the client handleGlob
[NX Daemon Server] - 2025-06-17T07:39:43.450Z - Handled GLOB. Handling time: 3. Response time: 3.
[NX Daemon Server] - 2025-06-17T07:39:43.455Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-06-17T07:39:43.458Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-06-17T07:39:43.458Z - Handled HASH_GLOB. Handling time: 2. Response time: 3.
[NX Daemon Server] - 2025-06-17T07:39:43.464Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-06-17T07:39:43.465Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-06-17T07:39:43.466Z - Handled HASH_GLOB. Handling time: 2. Response time: 2.
[NX Daemon Server] - 2025-06-17T07:39:43.722Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-06-17T07:39:43.724Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-06-17T07:39:43.724Z - Handled HASH_GLOB. Handling time: 24. Response time: 2.
[NX Daemon Server] - 2025-06-17T07:39:43.735Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-06-17T07:39:43.737Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-06-17T07:39:43.737Z - Handled HASH_GLOB. Handling time: 6. Response time: 2.
[NX Daemon Server] - 2025-06-17T07:39:43.908Z - Time taken for 'build-project-configs' 471.32280000002356ms
[NX Daemon Server] - 2025-06-17T07:39:44.242Z - [SYNC]: collect registered sync generators
[NX Daemon Server] - 2025-06-17T07:39:44.244Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX Daemon Server] - 2025-06-17T07:39:44.244Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX Daemon Server] - 2025-06-17T07:39:44.244Z - Time taken for 'total execution time for createProjectGraph()' 311.81630000000587ms
[NX Daemon Server] - 2025-06-17T07:39:53.473Z - [WATCHER]: packages/web/src/environments/environment.eg copy.ts was deleted
[NX Daemon Server] - 2025-06-17T07:39:53.473Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-06-17T07:39:53.924Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-06-17T07:39:53.924Z - [REQUEST]: 
[NX Daemon Server] - 2025-06-17T07:39:53.925Z - [REQUEST]: packages/web/src/environments/environment.eg copy.ts
[NX Daemon Server] - 2025-06-17T07:39:54.154Z - [REQUEST]: Responding to the client. handleGlob
[NX Daemon Server] - 2025-06-17T07:39:54.156Z - Time taken for 'hash changed files from watcher' 1.5132999999914318ms
[NX Daemon Server] - 2025-06-17T07:39:54.157Z - Done responding to the client handleGlob
[NX Daemon Server] - 2025-06-17T07:39:54.157Z - Handled GLOB. Handling time: 8. Response time: 3.
[NX Daemon Server] - 2025-06-17T07:39:54.166Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-06-17T07:39:54.167Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-06-17T07:39:54.167Z - Handled HASH_GLOB. Handling time: 4. Response time: 1.
[NX Daemon Server] - 2025-06-17T07:39:54.172Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-06-17T07:39:54.404Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-06-17T07:39:54.404Z - Handled HASH_GLOB. Handling time: 2. Response time: 232.
[NX Daemon Server] - 2025-06-17T07:39:54.470Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-06-17T07:39:54.471Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-06-17T07:39:54.471Z - Handled HASH_GLOB. Handling time: 57. Response time: 1.
[NX Daemon Server] - 2025-06-17T07:39:54.480Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-06-17T07:39:54.481Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-06-17T07:39:54.481Z - Handled HASH_GLOB. Handling time: 7. Response time: 1.
[NX Daemon Server] - 2025-06-17T07:39:54.539Z - Time taken for 'build-project-configs' 366.4238000000187ms
[NX Daemon Server] - 2025-06-17T07:39:54.970Z - [SYNC]: collect registered sync generators
[NX Daemon Server] - 2025-06-17T07:39:54.972Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX Daemon Server] - 2025-06-17T07:39:54.973Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX Daemon Server] - 2025-06-17T07:39:54.973Z - Time taken for 'total execution time for createProjectGraph()' 124.62209999997867ms
[NX Daemon Server] - 2025-06-17T07:39:59.233Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-06-17T07:39:59.235Z - [WATCHER]: packages/web/src/environments/envi was deleted
[NX Daemon Server] - 2025-06-17T07:40:00.063Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-06-17T07:40:00.063Z - [REQUEST]: 
[NX Daemon Server] - 2025-06-17T07:40:00.063Z - [REQUEST]: packages/web/src/environments/envi
[NX Daemon Server] - 2025-06-17T07:40:00.486Z - [REQUEST]: Responding to the client. handleGlob
[NX Daemon Server] - 2025-06-17T07:40:00.487Z - Time taken for 'hash changed files from watcher' 17.721799999999348ms
[NX Daemon Server] - 2025-06-17T07:40:00.502Z - Done responding to the client handleGlob
[NX Daemon Server] - 2025-06-17T07:40:00.502Z - Handled GLOB. Handling time: 63. Response time: 16.
[NX Daemon Server] - 2025-06-17T07:40:00.556Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-06-17T07:40:00.558Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-06-17T07:40:00.558Z - Handled HASH_GLOB. Handling time: 51. Response time: 2.
[NX Daemon Server] - 2025-06-17T07:40:00.602Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-06-17T07:40:00.603Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-06-17T07:40:00.603Z - Handled HASH_GLOB. Handling time: 39. Response time: 1.
[NX Daemon Server] - 2025-06-17T07:40:00.628Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-06-17T07:40:00.629Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-06-17T07:40:00.630Z - Handled HASH_GLOB. Handling time: 18. Response time: 2.
[NX Daemon Server] - 2025-06-17T07:40:00.673Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-06-17T07:40:00.673Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-06-17T07:40:00.673Z - Handled HASH_GLOB. Handling time: 42. Response time: 0.
[NX Daemon Server] - 2025-06-17T07:40:00.702Z - Time taken for 'build-project-configs' 279.632500000007ms
[NX Daemon Server] - 2025-06-17T07:40:00.839Z - [SYNC]: collect registered sync generators
[NX Daemon Server] - 2025-06-17T07:40:00.841Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX Daemon Server] - 2025-06-17T07:40:00.842Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX Daemon Server] - 2025-06-17T07:40:00.843Z - Time taken for 'total execution time for createProjectGraph()' 101.70380000001751ms
[NX Daemon Server] - 2025-06-17T07:40:09.846Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-06-17T07:40:39.190Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-06-17T07:40:39.191Z - [WATCHER]: packages/api/src/environments/environment.eg copy.ts was modified
[NX Daemon Server] - 2025-06-17T07:40:40.798Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-06-17T07:40:40.798Z - [REQUEST]: packages/api/src/environments/environment.eg copy.ts
[NX Daemon Server] - 2025-06-17T07:40:40.798Z - [REQUEST]: 
[NX Daemon Server] - 2025-06-17T07:40:40.817Z - [REQUEST]: Responding to the client. handleGlob
[NX Daemon Server] - 2025-06-17T07:40:40.818Z - Time taken for 'hash changed files from watcher' 0.852599999983795ms
[NX Daemon Server] - 2025-06-17T07:40:40.820Z - Done responding to the client handleGlob
[NX Daemon Server] - 2025-06-17T07:40:40.820Z - Handled GLOB. Handling time: 6. Response time: 3.
[NX Daemon Server] - 2025-06-17T07:40:40.823Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-06-17T07:40:40.824Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-06-17T07:40:40.824Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-06-17T07:40:40.829Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-06-17T07:40:40.830Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-06-17T07:40:40.830Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX Daemon Server] - 2025-06-17T07:40:40.938Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-06-17T07:40:40.939Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-06-17T07:40:40.939Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-06-17T07:40:40.988Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-06-17T07:40:40.988Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-06-17T07:40:40.989Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX Daemon Server] - 2025-06-17T07:40:41.006Z - Time taken for 'build-project-configs' 191.84330000000773ms
[NX Daemon Server] - 2025-06-17T07:40:41.113Z - [SYNC]: collect registered sync generators
[NX Daemon Server] - 2025-06-17T07:40:41.114Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX Daemon Server] - 2025-06-17T07:40:41.114Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX Daemon Server] - 2025-06-17T07:40:41.114Z - Time taken for 'total execution time for createProjectGraph()' 98.20389999996405ms
[NX Daemon Server] - 2025-06-17T07:40:46.710Z - [WATCHER]: packages/api/src/environments/environment.eg copy.ts was deleted
[NX Daemon Server] - 2025-06-17T07:40:46.711Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-06-17T07:40:49.916Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-06-17T07:40:49.917Z - [REQUEST]: 
[NX Daemon Server] - 2025-06-17T07:40:49.917Z - [REQUEST]: packages/api/src/environments/environment.eg copy.ts
[NX Daemon Server] - 2025-06-17T07:40:49.950Z - [REQUEST]: Responding to the client. handleGlob
[NX Daemon Server] - 2025-06-17T07:40:49.951Z - Time taken for 'hash changed files from watcher' 2.517200000002049ms
[NX Daemon Server] - 2025-06-17T07:40:49.952Z - Done responding to the client handleGlob
[NX Daemon Server] - 2025-06-17T07:40:49.952Z - Handled GLOB. Handling time: 3. Response time: 2.
[NX Daemon Server] - 2025-06-17T07:40:49.957Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-06-17T07:40:49.958Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-06-17T07:40:49.959Z - Handled HASH_GLOB. Handling time: 1. Response time: 2.
[NX Daemon Server] - 2025-06-17T07:40:49.963Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-06-17T07:40:49.964Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-06-17T07:40:49.964Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX Daemon Server] - 2025-06-17T07:40:49.972Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-06-17T07:40:49.973Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-06-17T07:40:49.973Z - Handled HASH_GLOB. Handling time: 6. Response time: 1.
[NX Daemon Server] - 2025-06-17T07:40:49.977Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-06-17T07:40:49.978Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-06-17T07:40:49.978Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-06-17T07:40:50.012Z - Time taken for 'build-project-configs' 72.5557999999728ms
[NX Daemon Server] - 2025-06-17T07:40:50.070Z - [SYNC]: collect registered sync generators
[NX Daemon Server] - 2025-06-17T07:40:50.071Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX Daemon Server] - 2025-06-17T07:40:50.071Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX Daemon Server] - 2025-06-17T07:40:50.071Z - Time taken for 'total execution time for createProjectGraph()' 43.11089999997057ms
[NX Daemon Server] - 2025-06-17T07:41:02.432Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-06-17T07:41:20.677Z - Closed a connection. Number of open connections: 2
[NX Daemon Server] - 2025-06-17T07:41:20.683Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-06-17T07:41:25.119Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-06-17T07:41:25.120Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-06-17T07:41:25.122Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-06-17T07:41:25.130Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-06-17T07:41:25.132Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-06-17T07:41:25.134Z - Time taken for 'total for creating and serializing project graph' 1.2790000000386499ms
[NX Daemon Server] - 2025-06-17T07:41:25.139Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-06-17T07:41:25.139Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 7.
[NX Daemon Server] - 2025-06-17T07:41:25.170Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX Daemon Server] - 2025-06-17T07:41:25.170Z - Time taken for 'preTasksExecution' 0.6595000000088476ms
[NX Daemon Server] - 2025-06-17T07:41:25.171Z - Done responding to the client handleRunPreTasksExecution
[NX Daemon Server] - 2025-06-17T07:41:25.171Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-06-17T07:41:25.421Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-06-17T07:41:25.424Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-06-17T07:41:25.424Z - Handled HASH_TASKS. Handling time: 74. Response time: 3.
[NX Daemon Server] - 2025-06-17T07:41:25.458Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-06-17T07:41:25.458Z - Done responding to the client handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-06-17T07:41:25.458Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-06-17T07:41:27.190Z - Established a connection. Number of open connections: 3
[NX Daemon Server] - 2025-06-17T07:41:27.191Z - Closed a connection. Number of open connections: 2
[NX Daemon Server] - 2025-06-17T07:41:27.192Z - Established a connection. Number of open connections: 3
[NX Daemon Server] - 2025-06-17T07:41:27.196Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-06-17T07:41:27.197Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-06-17T07:41:27.198Z - Time taken for 'total for creating and serializing project graph' 0.7580999999772757ms
[NX Daemon Server] - 2025-06-17T07:41:27.201Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-06-17T07:41:27.201Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 4.
[NX Daemon Server] - 2025-06-17T07:41:46.901Z - Closed a connection. Number of open connections: 2
[NX Daemon Server] - 2025-06-17T07:41:46.904Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-06-17T07:42:01.731Z - [WATCHER]: _tmp_14460_f15f348f11f785c7f1f48774c955cdde was deleted
[NX Daemon Server] - 2025-06-17T07:42:01.732Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-06-17T07:42:01.840Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-06-17T07:42:01.840Z - [REQUEST]: 
[NX Daemon Server] - 2025-06-17T07:42:01.840Z - [REQUEST]: _tmp_14460_f15f348f11f785c7f1f48774c955cdde
[NX Daemon Server] - 2025-06-17T07:42:01.850Z - Time taken for 'hash changed files from watcher' 1.352599999983795ms
[NX Daemon Server] - 2025-06-17T07:42:01.853Z - [REQUEST]: Responding to the client. handleGlob
[NX Daemon Server] - 2025-06-17T07:42:01.854Z - Done responding to the client handleGlob
[NX Daemon Server] - 2025-06-17T07:42:01.854Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX Daemon Server] - 2025-06-17T07:42:01.856Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-06-17T07:42:01.857Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-06-17T07:42:01.857Z - Handled HASH_GLOB. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-06-17T07:42:01.859Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-06-17T07:42:01.859Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-06-17T07:42:01.860Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-06-17T07:42:01.862Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-06-17T07:42:01.863Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-06-17T07:42:01.863Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-06-17T07:42:01.865Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-06-17T07:42:01.866Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-06-17T07:42:01.866Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-06-17T07:42:01.888Z - Time taken for 'build-project-configs' 34.552000000025146ms
[NX Daemon Server] - 2025-06-17T07:42:01.942Z - [SYNC]: collect registered sync generators
[NX Daemon Server] - 2025-06-17T07:42:01.943Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX Daemon Server] - 2025-06-17T07:42:01.943Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX Daemon Server] - 2025-06-17T07:42:01.944Z - Time taken for 'total execution time for createProjectGraph()' 41.90670000005048ms
[NX Daemon Server] - 2025-06-17T07:44:03.877Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-06-17T07:44:03.879Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-06-17T07:44:03.881Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-06-17T07:44:03.896Z - [REQUEST]: Responding to the client. Shutdown initiated
[NX Daemon Server] - 2025-06-17T07:44:03.896Z - Done responding to the client Shutdown initiated
[NX Daemon Server] - 2025-06-17T07:44:03.897Z - Handled FORCE_SHUTDOWN. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-06-17T07:44:03.899Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-06-17T07:44:03.899Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-06-17T07:44:03.900Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Desktop\New folder\propped (sources)
[NX Daemon Server] - 2025-06-17T07:44:03.900Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Desktop\New folder\propped (outputs)
[NX Daemon Server] - 2025-06-17T07:44:03.922Z - Server stopped because: "Request to shutdown"
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:37.268Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\7ca78b1b0c7c2317476f\d.sock
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:37.274Z - [WATCHER]: Subscribed to changes within: C:\Users\<USER>\Desktop\New folder\propped (native)
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:37.284Z - Established a connection. Number of open connections: 1
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:37.287Z - Closed a connection. Number of open connections: 0
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:37.288Z - Established a connection. Number of open connections: 1
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:37.291Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:38.064Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\Desktop\New folder\propped\node_modules\.pnpm\nx@21.0.3_@swc-node+registe_3ea8e76b521e497598bf8132b28aac73\node_modules\nx\src\plugins\project-json\build-nodes\project-json' 763.8684000000001ms
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:38.067Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\Desktop\New folder\propped\node_modules\.pnpm\nx@21.0.3_@swc-node+registe_3ea8e76b521e497598bf8132b28aac73\node_modules\nx\src\plugins\package-json' 768.0169999999999ms
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:38.133Z - Time taken for 'loadDefaultNxPlugins' 834.0727ms
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:39.021Z - Time taken for 'Load Nx Plugin: @nx/eslint/plugin' 1725.6417000000001ms
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:39.992Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:39.992Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:39.992Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:40.007Z - Time taken for 'loadSpecifiedNxPlugins' 2697.5910000000003ms
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:40.011Z - Established a connection. Number of open connections: 2
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:40.012Z - Closed a connection. Number of open connections: 1
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:40.013Z - Established a connection. Number of open connections: 2
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:40.017Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:40.020Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:40.020Z - Handled GLOB. Handling time: 2. Response time: 3.
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:40.027Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:40.036Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:40.036Z - Handled HASH_GLOB. Handling time: 3. Response time: 9.
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:40.373Z - Time taken for 'build-project-configs' 349.8952999999997ms
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:40.428Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:40.429Z - [REQUEST]: Responding to the client. project-graph
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:40.430Z - Time taken for 'total for creating and serializing project graph' 3138.447ms
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:40.432Z - Done responding to the client project-graph
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:40.432Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 3138. Response time: 3.
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:40.444Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:40.445Z - Time taken for 'preTasksExecution' 0.8396000000002459ms
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:40.445Z - Done responding to the client handleRunPreTasksExecution
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:40.445Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:40.599Z - [REQUEST]: Responding to the client. handleHashTasks
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:40.600Z - Done responding to the client handleHashTasks
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:40.600Z - Handled HASH_TASKS. Handling time: 46. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:40.626Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:40.627Z - Done responding to the client handleGetEstimatedTaskTimings
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:40.627Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 4. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:43.523Z - Established a connection. Number of open connections: 3
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:43.524Z - Closed a connection. Number of open connections: 2
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:43.526Z - Established a connection. Number of open connections: 3
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:43.530Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:43.531Z - [REQUEST]: Responding to the client. project-graph
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:43.533Z - Time taken for 'total for creating and serializing project graph' 0.6880000000001019ms
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:43.536Z - Done responding to the client project-graph
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:43.536Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 5.
[NX v21.0.3 Daemon Server] - 2025-06-17T07:44:46.056Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T07:45:08.551Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T07:45:10.696Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T07:45:10.755Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T07:45:36.609Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T07:45:36.748Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T07:45:36.835Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T07:45:36.905Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T07:45:37.084Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T07:45:37.138Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T07:45:37.247Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T07:45:37.342Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T07:45:37.504Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T07:45:37.569Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T07:45:37.677Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T07:45:37.889Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T07:45:37.956Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T07:45:38.175Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T07:45:38.309Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T07:45:38.359Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T07:45:38.421Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:17.050Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:17.052Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:17.160Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:17.160Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:17.160Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:17.184Z - Time taken for 'hash changed files from watcher' 2.8240000000223517ms
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:17.210Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:17.211Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:17.211Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:17.222Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:17.223Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:17.223Z - Handled HASH_GLOB. Handling time: 6. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:27.685Z - Time taken for 'build-project-configs' 10495.210999999894ms
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:27.776Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:27.778Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:27.778Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:27.778Z - Time taken for 'total execution time for createProjectGraph()' 74.91029999987222ms
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:37.974Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:37.975Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:38.189Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:38.189Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:38.189Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:38.230Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:38.232Z - Time taken for 'hash changed files from watcher' 2.480299999937415ms
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:38.235Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:38.235Z - Handled GLOB. Handling time: 5. Response time: 5.
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:38.256Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:38.259Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:38.259Z - Handled HASH_GLOB. Handling time: 11. Response time: 3.
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:38.442Z - Time taken for 'build-project-configs' 234.3514000000432ms
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:38.531Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:38.532Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:38.532Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:38.532Z - Time taken for 'total execution time for createProjectGraph()' 71.13199999998324ms
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:47.753Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:47.755Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:48.158Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:48.158Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:48.158Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:48.166Z - Time taken for 'hash changed files from watcher' 0.6878000001888722ms
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:48.169Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:48.170Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:48.170Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:48.176Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:48.177Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:48.177Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:48.376Z - Time taken for 'build-project-configs' 206.24120000004768ms
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:48.465Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:48.466Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:48.466Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:48.466Z - Time taken for 'total execution time for createProjectGraph()' 65.74419999984093ms
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:51.350Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T08:12:59.618Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:11.786Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:11.787Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:12.595Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:12.595Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:12.595Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:12.601Z - Time taken for 'hash changed files from watcher' 0.6684999999124557ms
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:12.604Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:12.605Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:12.605Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:12.610Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:12.611Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:12.611Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:12.747Z - Time taken for 'build-project-configs' 134.9527999998536ms
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:12.860Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:12.861Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:12.861Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:12.861Z - Time taken for 'total execution time for createProjectGraph()' 81.38259999989532ms
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:23.748Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:23.749Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:25.356Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:25.356Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:25.356Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:25.363Z - Time taken for 'hash changed files from watcher' 0.5630000000819564ms
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:25.366Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:25.367Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:25.367Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:25.372Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:25.373Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:25.373Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:25.506Z - Time taken for 'build-project-configs' 134.27490000007674ms
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:25.582Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:25.583Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:25.583Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:25.583Z - Time taken for 'total execution time for createProjectGraph()' 63.258099999977276ms
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:29.455Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:44.053Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:44.055Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:47.269Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:47.270Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:47.270Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:47.279Z - Time taken for 'hash changed files from watcher' 0.9314000001177192ms
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:47.286Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:47.287Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:47.287Z - Handled GLOB. Handling time: 5. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:47.292Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:47.292Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:47.293Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:47.352Z - Time taken for 'build-project-configs' 58.742599999997765ms
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:47.454Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:47.455Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:47.456Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:47.456Z - Time taken for 'total execution time for createProjectGraph()' 93.80389999994077ms
[NX v21.0.3 Daemon Server] - 2025-06-17T08:13:49.901Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T08:14:02.103Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T08:14:02.108Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T08:14:07.576Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T08:14:08.532Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T08:14:08.533Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts
[NX v21.0.3 Daemon Server] - 2025-06-17T08:14:08.533Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T08:14:08.604Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T08:14:08.604Z - Time taken for 'hash changed files from watcher' 6.147899999981746ms
[NX v21.0.3 Daemon Server] - 2025-06-17T08:14:08.606Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T08:14:08.606Z - Handled GLOB. Handling time: 2. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-17T08:14:08.611Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T08:14:08.612Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T08:14:08.612Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T08:14:08.897Z - Time taken for 'build-project-configs' 279.80409999983385ms
[NX v21.0.3 Daemon Server] - 2025-06-17T08:14:09.028Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T08:14:09.030Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T08:14:09.031Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T08:14:09.031Z - Time taken for 'total execution time for createProjectGraph()' 106.66760000004433ms
[NX v21.0.3 Daemon Server] - 2025-06-17T08:20:57.970Z - Closed a connection. Number of open connections: 2
[NX v21.0.3 Daemon Server] - 2025-06-17T08:20:58.011Z - Closed a connection. Number of open connections: 1
[NX v21.0.3 Daemon Server] - 2025-06-17T10:52:16.931Z - Established a connection. Number of open connections: 2
[NX v21.0.3 Daemon Server] - 2025-06-17T10:52:16.941Z - Closed a connection. Number of open connections: 1
[NX v21.0.3 Daemon Server] - 2025-06-17T10:52:16.942Z - Established a connection. Number of open connections: 2
[NX v21.0.3 Daemon Server] - 2025-06-17T10:52:16.957Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.0.3 Daemon Server] - 2025-06-17T10:52:16.960Z - [REQUEST]: Responding to the client. project-graph
[NX v21.0.3 Daemon Server] - 2025-06-17T10:52:16.963Z - Time taken for 'total for creating and serializing project graph' 2.946399999782443ms
[NX v21.0.3 Daemon Server] - 2025-06-17T10:52:16.967Z - Done responding to the client project-graph
[NX v21.0.3 Daemon Server] - 2025-06-17T10:52:16.968Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 3. Response time: 8.
[NX v21.0.3 Daemon Server] - 2025-06-17T10:52:16.998Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.0.3 Daemon Server] - 2025-06-17T10:52:16.999Z - Time taken for 'preTasksExecution' 1.0189999993890524ms
[NX v21.0.3 Daemon Server] - 2025-06-17T10:52:16.999Z - Done responding to the client handleRunPreTasksExecution
[NX v21.0.3 Daemon Server] - 2025-06-17T10:52:17.000Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T10:52:17.267Z - [REQUEST]: Responding to the client. handleHashTasks
[NX v21.0.3 Daemon Server] - 2025-06-17T10:52:17.273Z - Done responding to the client handleHashTasks
[NX v21.0.3 Daemon Server] - 2025-06-17T10:52:17.273Z - Handled HASH_TASKS. Handling time: 91. Response time: 6.
[NX v21.0.3 Daemon Server] - 2025-06-17T10:52:17.316Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX v21.0.3 Daemon Server] - 2025-06-17T10:52:17.317Z - Done responding to the client handleGetEstimatedTaskTimings
[NX v21.0.3 Daemon Server] - 2025-06-17T10:52:17.317Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 0. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T10:52:21.538Z - Established a connection. Number of open connections: 3
[NX v21.0.3 Daemon Server] - 2025-06-17T10:52:21.543Z - Closed a connection. Number of open connections: 2
[NX v21.0.3 Daemon Server] - 2025-06-17T10:52:21.544Z - Established a connection. Number of open connections: 3
[NX v21.0.3 Daemon Server] - 2025-06-17T10:52:21.546Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.0.3 Daemon Server] - 2025-06-17T10:52:21.547Z - [REQUEST]: Responding to the client. project-graph
[NX v21.0.3 Daemon Server] - 2025-06-17T10:52:21.549Z - Time taken for 'total for creating and serializing project graph' 0.8370999991893768ms
[NX v21.0.3 Daemon Server] - 2025-06-17T10:52:21.561Z - Done responding to the client project-graph
[NX v21.0.3 Daemon Server] - 2025-06-17T10:52:21.561Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 14.
[NX v21.0.3 Daemon Server] - 2025-06-17T10:52:48.912Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:17:28.197Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:17:28.223Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:17:28.343Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T12:17:28.344Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T12:17:28.344Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T12:17:28.390Z - Time taken for 'hash changed files from watcher' 5.65229999832809ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:17:28.519Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:17:28.521Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:17:28.522Z - Handled GLOB. Handling time: 6. Response time: 3.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:17:28.534Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:17:28.535Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:17:28.535Z - Handled HASH_GLOB. Handling time: 5. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:17:28.600Z - Time taken for 'build-project-configs' 219.73750000074506ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:17:28.747Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:17:28.749Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:17:28.750Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:17:28.751Z - Time taken for 'total execution time for createProjectGraph()' 103.86849999986589ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:19:43.318Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:19:43.325Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:19:43.540Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T12:19:43.541Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T12:19:43.541Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T12:19:43.565Z - Time taken for 'hash changed files from watcher' 2.7640000004321337ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:19:43.573Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:19:43.575Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:19:43.575Z - Handled GLOB. Handling time: 3. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:19:43.594Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:19:43.595Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:19:43.595Z - Handled HASH_GLOB. Handling time: 9. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:19:43.996Z - Time taken for 'build-project-configs' 434.77240000106394ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:19:44.103Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:19:44.104Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:19:44.105Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:19:44.105Z - Time taken for 'total execution time for createProjectGraph()' 77.81610000133514ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:19:52.977Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:19:52.980Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:19:53.393Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T12:19:53.393Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T12:19:53.393Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T12:19:53.407Z - Time taken for 'hash changed files from watcher' 1.2626000009477139ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:19:53.411Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:19:53.412Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:19:53.412Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:19:53.419Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:19:53.420Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:19:53.420Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:19:53.477Z - Time taken for 'build-project-configs' 64.65340000018477ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:19:53.585Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:19:53.587Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:19:53.587Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:19:53.587Z - Time taken for 'total execution time for createProjectGraph()' 81.18260000087321ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:07.490Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:07.495Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:08.299Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:08.299Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:08.299Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:08.314Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:08.314Z - Time taken for 'hash changed files from watcher' 2.2719999998807907ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:08.315Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:08.315Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:08.321Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:08.321Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:08.321Z - Handled HASH_GLOB. Handling time: 4. Response time: 0.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:08.520Z - Time taken for 'build-project-configs' 200.3073999993503ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:08.655Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:08.657Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:08.657Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:08.657Z - Time taken for 'total execution time for createProjectGraph()' 111.90409999899566ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:10.064Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:10.067Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:11.671Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:11.672Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:11.672Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:11.697Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:11.697Z - Time taken for 'hash changed files from watcher' 1.9047999996691942ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:11.699Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:11.699Z - Handled GLOB. Handling time: 3. Response time: 3.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:11.711Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:11.712Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:11.712Z - Handled HASH_GLOB. Handling time: 9. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:11.925Z - Time taken for 'build-project-configs' 234.6890000011772ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:12.026Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:12.028Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:12.028Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:12.028Z - Time taken for 'total execution time for createProjectGraph()' 67.37609999999404ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:12.056Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:12.057Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:15.270Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:15.271Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:15.271Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:15.286Z - Time taken for 'hash changed files from watcher' 2.000400001183152ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:15.294Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:15.296Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:15.296Z - Handled GLOB. Handling time: 4. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:15.305Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:15.306Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:15.306Z - Handled HASH_GLOB. Handling time: 5. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:15.566Z - Time taken for 'build-project-configs' 270.9955000001937ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:15.737Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:15.738Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:15.739Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:15.739Z - Time taken for 'total execution time for createProjectGraph()' 118.16559999994934ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:20:37.303Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:01.962Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:01.962Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:08.368Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:08.368Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:08.368Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:08.387Z - Time taken for 'hash changed files from watcher' 1.674099998548627ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:08.395Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:08.397Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:08.397Z - Handled GLOB. Handling time: 4. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:08.404Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:08.405Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:08.405Z - Handled HASH_GLOB. Handling time: 4. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:08.464Z - Time taken for 'build-project-configs' 62.88630000129342ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:08.575Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:08.577Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:08.577Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:08.577Z - Time taken for 'total execution time for createProjectGraph()' 95.32660000026226ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:36.717Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:36.724Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:38.764Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:38.766Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:41.506Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:41.511Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:43.139Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:43.140Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:43.140Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:43.164Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:43.164Z - Time taken for 'hash changed files from watcher' 1.310699999332428ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:43.168Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:43.169Z - Handled GLOB. Handling time: 3. Response time: 5.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:43.180Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:43.181Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:43.182Z - Handled HASH_GLOB. Handling time: 6. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:43.240Z - Time taken for 'build-project-configs' 80.4394000004977ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:43.375Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:43.376Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:43.377Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:43.377Z - Time taken for 'total execution time for createProjectGraph()' 106.67380000092089ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:21:54.448Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:22:09.436Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:23:38.111Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:23:38.111Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:23:40.945Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:23:40.949Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:23:44.524Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T12:23:44.524Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T12:23:44.524Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T12:23:44.543Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:23:44.543Z - Time taken for 'hash changed files from watcher' 1.1213000006973743ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:23:44.544Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:23:44.544Z - Handled GLOB. Handling time: 4. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:23:44.549Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:23:44.550Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:23:44.550Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:23:44.692Z - Time taken for 'build-project-configs' 155.7752999998629ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:23:44.764Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:23:44.765Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:23:44.765Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:23:44.766Z - Time taken for 'total execution time for createProjectGraph()' 55.82450000010431ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:23:59.241Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:23:59.241Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:24:02.183Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:24:02.183Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:24:04.003Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:24:05.650Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T12:24:05.650Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts
[NX v21.0.3 Daemon Server] - 2025-06-17T12:24:05.650Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T12:24:05.658Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:24:05.658Z - Time taken for 'hash changed files from watcher' 0.5448000002652407ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:24:05.659Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:24:05.659Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:24:05.664Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:24:05.665Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:24:05.665Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:24:05.774Z - Time taken for 'build-project-configs' 113.5420000012964ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:24:05.850Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:24:05.851Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:24:05.851Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:24:05.851Z - Time taken for 'total execution time for createProjectGraph()' 56.49430000036955ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:24:08.924Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:07.359Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:07.360Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:13.536Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:13.771Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:13.771Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:13.771Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:13.778Z - Time taken for 'hash changed files from watcher' 0.79279999807477ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:13.783Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:13.787Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:13.787Z - Handled GLOB. Handling time: 2. Response time: 4.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:13.793Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:13.793Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:13.793Z - Handled HASH_GLOB. Handling time: 2. Response time: 0.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:13.967Z - Time taken for 'build-project-configs' 177.09560000151396ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:14.089Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:14.090Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:14.090Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:14.090Z - Time taken for 'total execution time for createProjectGraph()' 84.64860000088811ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:14.117Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:14.120Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:20.533Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:20.533Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:20.533Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:20.545Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:20.546Z - Time taken for 'hash changed files from watcher' 0.9074999988079071ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:20.547Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:20.547Z - Handled GLOB. Handling time: 1. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:20.550Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:20.551Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:20.551Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:20.584Z - Time taken for 'build-project-configs' 33.696200001984835ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:20.639Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:20.640Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:20.640Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:20.641Z - Time taken for 'total execution time for createProjectGraph()' 45.31379999965429ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:23.354Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:28.550Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:38.310Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:38.369Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:38.509Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:38.618Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:38.675Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:38.737Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:38.844Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:38.915Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:39.001Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:39.080Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:39.199Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:39.269Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:39.331Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:39.402Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:39.463Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:39.526Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:39.813Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:25:39.869Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:11.174Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:11.174Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:16.608Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:17.577Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:17.577Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:17.577Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:17.678Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:17.679Z - Time taken for 'hash changed files from watcher' 1.661200001835823ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:17.680Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:17.680Z - Handled GLOB. Handling time: 4. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:17.696Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:17.710Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:17.711Z - Handled HASH_GLOB. Handling time: 13. Response time: 15.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:17.739Z - Time taken for 'build-project-configs' 91.64619999751449ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:17.871Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:18.022Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:18.023Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:18.023Z - Time taken for 'total execution time for createProjectGraph()' 94.31879999861121ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:21.336Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:21.337Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:25.336Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:25.337Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:26.950Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:27.752Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:27.752Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts,packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:27.753Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:27.769Z - Time taken for 'hash changed files from watcher' 8.52980000153184ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:27.785Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:27.787Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:27.787Z - Handled GLOB. Handling time: 8. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:27.805Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:27.820Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:27.820Z - Handled HASH_GLOB. Handling time: 13. Response time: 16.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:28.151Z - Time taken for 'build-project-configs' 375.63949999958277ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:28.379Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:28.396Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:28.399Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:28.403Z - Time taken for 'total execution time for createProjectGraph()' 112.90609999746084ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:33.563Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:48.360Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:48.361Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:50.658Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:50.661Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:51.947Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:51.948Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:53.207Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:54.776Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:54.776Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:54.776Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:54.786Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:54.786Z - Time taken for 'hash changed files from watcher' 1.0291999988257885ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:54.787Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:54.787Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:54.793Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:54.793Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:54.793Z - Handled HASH_GLOB. Handling time: 4. Response time: 0.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:54.926Z - Time taken for 'build-project-configs' 139.00200000032783ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:54.989Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:54.990Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:54.990Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:54.991Z - Time taken for 'total execution time for createProjectGraph()' 50.58900000154972ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:26:59.552Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:27:07.260Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:27:07.260Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:27:10.796Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:27:10.858Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:27:11.090Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:27:11.335Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:27:11.400Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:27:11.613Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:27:11.838Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:27:12.037Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:27:12.240Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:27:12.383Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:27:12.505Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:27:12.599Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:27:12.675Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:27:12.736Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:27:13.676Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T12:27:13.676Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts
[NX v21.0.3 Daemon Server] - 2025-06-17T12:27:13.676Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T12:27:13.702Z - Time taken for 'hash changed files from watcher' 2.594700001180172ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:27:13.707Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:27:13.708Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:27:13.708Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:27:13.715Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:27:13.716Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:27:13.716Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:27:14.064Z - Time taken for 'build-project-configs' 344.2237999998033ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:27:14.186Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:27:14.188Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:27:14.188Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:27:14.189Z - Time taken for 'total execution time for createProjectGraph()' 103.64649999886751ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:27:16.707Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:28:00.039Z - Closed a connection. Number of open connections: 2
[NX v21.0.3 Daemon Server] - 2025-06-17T12:28:00.581Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX v21.0.3 Daemon Server] - 2025-06-17T12:28:00.581Z - Done responding to the client recordOutputsHash
[NX v21.0.3 Daemon Server] - 2025-06-17T12:28:00.581Z - Handled RECORD_OUTPUTS_HASH. Handling time: 3. Response time: 0.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:28:00.611Z - [REQUEST]: Responding to the client. handleRecordTaskRuns
[NX v21.0.3 Daemon Server] - 2025-06-17T12:28:00.612Z - Done responding to the client handleRecordTaskRuns
[NX v21.0.3 Daemon Server] - 2025-06-17T12:28:00.612Z - Handled RECORD_TASK_RUNS. Handling time: 5. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:28:00.620Z - [REQUEST]: Responding to the client. handleGetFlakyTasks
[NX v21.0.3 Daemon Server] - 2025-06-17T12:28:00.621Z - Done responding to the client handleGetFlakyTasks
[NX v21.0.3 Daemon Server] - 2025-06-17T12:28:00.621Z - Handled GET_FLAKY_TASKS. Handling time: 7. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:28:01.418Z - [REQUEST]: Responding to the client. handleRunPostTasksExecution
[NX v21.0.3 Daemon Server] - 2025-06-17T12:28:01.418Z - Time taken for 'postTasksExecution' 0.5723000019788742ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:28:01.419Z - Done responding to the client handleRunPostTasksExecution
[NX v21.0.3 Daemon Server] - 2025-06-17T12:28:01.419Z - Handled POST_TASKS_EXECUTION. Handling time: 1. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:28:01.429Z - Closed a connection. Number of open connections: 1
[NX v21.0.3 Daemon Server] - 2025-06-17T12:38:57.316Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:38:57.316Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:39:03.733Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T12:39:03.733Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T12:39:03.733Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T12:39:03.747Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:39:03.747Z - Time taken for 'hash changed files from watcher' 1.01970000192523ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:39:03.748Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:39:03.749Z - Handled GLOB. Handling time: 1. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:39:03.754Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:39:03.754Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:39:03.754Z - Handled HASH_GLOB. Handling time: 3. Response time: 0.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:39:03.893Z - Time taken for 'build-project-configs' 145.59740000218153ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:39:03.950Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:39:03.951Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:39:03.951Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:39:03.951Z - Time taken for 'total execution time for createProjectGraph()' 47.027300000190735ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:40:48.020Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:40:48.023Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:40:54.440Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T12:40:54.440Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T12:40:54.440Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T12:40:54.461Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:40:54.462Z - Time taken for 'hash changed files from watcher' 1.2175999991595745ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:40:54.463Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:40:54.464Z - Handled GLOB. Handling time: 1. Response time: 3.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:40:54.477Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:40:54.485Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:40:54.485Z - Handled HASH_GLOB. Handling time: 10. Response time: 8.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:40:54.602Z - Time taken for 'build-project-configs' 150.62599999830127ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:40:54.656Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:40:54.657Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:40:54.657Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:40:54.657Z - Time taken for 'total execution time for createProjectGraph()' 43.134799998253584ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:41:10.516Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:41:10.517Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:41:16.918Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T12:41:16.918Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T12:41:16.918Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T12:41:16.933Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:41:16.933Z - Time taken for 'hash changed files from watcher' 0.6939999982714653ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:41:16.934Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:41:16.934Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:41:16.937Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:41:16.937Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:41:16.937Z - Handled HASH_GLOB. Handling time: 2. Response time: 0.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:41:17.061Z - Time taken for 'build-project-configs' 128.80519999936223ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:41:17.122Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:41:17.123Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:41:17.123Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:41:17.123Z - Time taken for 'total execution time for createProjectGraph()' 50.491800002753735ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:19.761Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:19.779Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:21.012Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:21.012Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:25.607Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:25.609Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:26.187Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:26.187Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:26.187Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:26.199Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:26.200Z - Time taken for 'hash changed files from watcher' 0.8623000010848045ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:26.201Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:26.201Z - Handled GLOB. Handling time: 1. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:26.205Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:26.206Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:26.206Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:26.339Z - Time taken for 'build-project-configs' 140.9320000000298ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:26.396Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:26.397Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:26.397Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:26.397Z - Time taken for 'total execution time for createProjectGraph()' 44.32589999958873ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:26.749Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:26.749Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:33.159Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:33.159Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:33.159Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:33.167Z - Time taken for 'hash changed files from watcher' 0.9060999974608421ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:33.170Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:33.171Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:33.171Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:33.177Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:33.178Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:33.179Z - Handled HASH_GLOB. Handling time: 3. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:33.341Z - Time taken for 'build-project-configs' 169.10289999842644ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:33.429Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:33.430Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:33.430Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:33.430Z - Time taken for 'total execution time for createProjectGraph()' 62.950799997895956ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:54.556Z - Established a connection. Number of open connections: 2
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:54.556Z - Closed a connection. Number of open connections: 1
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:54.557Z - Established a connection. Number of open connections: 2
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:54.560Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:54.561Z - [REQUEST]: Responding to the client. project-graph
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:54.562Z - Time taken for 'total for creating and serializing project graph' 0.8080000020563602ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:54.565Z - Done responding to the client project-graph
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:54.565Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 4.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:54.580Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:54.581Z - Time taken for 'preTasksExecution' 0.6083000004291534ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:54.581Z - Done responding to the client handleRunPreTasksExecution
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:54.581Z - Handled PRE_TASKS_EXECUTION. Handling time: 0. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:54.800Z - [REQUEST]: Responding to the client. handleHashTasks
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:54.801Z - Done responding to the client handleHashTasks
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:54.801Z - Handled HASH_TASKS. Handling time: 26. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:54.829Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:54.829Z - Done responding to the client handleGetEstimatedTaskTimings
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:54.829Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 1. Response time: 0.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:57.463Z - Established a connection. Number of open connections: 3
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:57.464Z - Closed a connection. Number of open connections: 2
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:57.465Z - Established a connection. Number of open connections: 3
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:57.467Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:57.468Z - [REQUEST]: Responding to the client. project-graph
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:57.469Z - Time taken for 'total for creating and serializing project graph' 0.48270000144839287ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:57.471Z - Done responding to the client project-graph
[NX v21.0.3 Daemon Server] - 2025-06-17T12:43:57.471Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 3.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:44:20.532Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:44:22.469Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:44:22.529Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:44:22.714Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:44:45.471Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:44:45.641Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:44:45.719Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:44:45.806Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:44:45.869Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:44:45.943Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:44:46.121Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:44:46.191Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:44:46.356Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:44:46.438Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:44:46.620Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:44:46.720Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:44:46.879Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:44:46.948Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:44:47.011Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:45:58.947Z - Closed a connection. Number of open connections: 2
[NX v21.0.3 Daemon Server] - 2025-06-17T12:45:58.957Z - Closed a connection. Number of open connections: 1
[NX v21.0.3 Daemon Server] - 2025-06-17T12:46:20.046Z - Established a connection. Number of open connections: 2
[NX v21.0.3 Daemon Server] - 2025-06-17T12:46:20.047Z - Closed a connection. Number of open connections: 1
[NX v21.0.3 Daemon Server] - 2025-06-17T12:46:20.049Z - Established a connection. Number of open connections: 2
[NX v21.0.3 Daemon Server] - 2025-06-17T12:46:20.053Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.0.3 Daemon Server] - 2025-06-17T12:46:20.055Z - [REQUEST]: Responding to the client. project-graph
[NX v21.0.3 Daemon Server] - 2025-06-17T12:46:20.057Z - Time taken for 'total for creating and serializing project graph' 1.0328999981284142ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:46:20.063Z - Done responding to the client project-graph
[NX v21.0.3 Daemon Server] - 2025-06-17T12:46:20.063Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 8.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:46:20.074Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.0.3 Daemon Server] - 2025-06-17T12:46:20.074Z - Time taken for 'preTasksExecution' 0.4510999992489815ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:46:20.075Z - Done responding to the client handleRunPreTasksExecution
[NX v21.0.3 Daemon Server] - 2025-06-17T12:46:20.075Z - Handled PRE_TASKS_EXECUTION. Handling time: 0. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:46:20.153Z - [REQUEST]: Responding to the client. handleHashTasks
[NX v21.0.3 Daemon Server] - 2025-06-17T12:46:20.154Z - Done responding to the client handleHashTasks
[NX v21.0.3 Daemon Server] - 2025-06-17T12:46:20.154Z - Handled HASH_TASKS. Handling time: 22. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:46:20.187Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX v21.0.3 Daemon Server] - 2025-06-17T12:46:20.187Z - Done responding to the client handleGetEstimatedTaskTimings
[NX v21.0.3 Daemon Server] - 2025-06-17T12:46:20.187Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 1. Response time: 0.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:46:21.122Z - Established a connection. Number of open connections: 3
[NX v21.0.3 Daemon Server] - 2025-06-17T12:46:21.122Z - Closed a connection. Number of open connections: 2
[NX v21.0.3 Daemon Server] - 2025-06-17T12:46:21.123Z - Established a connection. Number of open connections: 3
[NX v21.0.3 Daemon Server] - 2025-06-17T12:46:21.126Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.0.3 Daemon Server] - 2025-06-17T12:46:21.126Z - [REQUEST]: Responding to the client. project-graph
[NX v21.0.3 Daemon Server] - 2025-06-17T12:46:21.128Z - Time taken for 'total for creating and serializing project graph' 0.5318999998271465ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:46:21.130Z - Done responding to the client project-graph
[NX v21.0.3 Daemon Server] - 2025-06-17T12:46:21.130Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 4.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:46:42.848Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:05.236Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:05.240Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:05.345Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:05.345Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:05.345Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:05.355Z - Time taken for 'hash changed files from watcher' 0.873599998652935ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:05.359Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:05.360Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:05.360Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:05.366Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:05.366Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:05.366Z - Handled HASH_GLOB. Handling time: 4. Response time: 0.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:05.476Z - Time taken for 'build-project-configs' 112.94920000061393ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:05.551Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:05.552Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:05.552Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:05.552Z - Time taken for 'total execution time for createProjectGraph()' 68.32870000228286ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:16.810Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:51.465Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:51.466Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:51.670Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:51.670Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:51.670Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:51.693Z - Time taken for 'hash changed files from watcher' 1.886899996548891ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:51.703Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:51.705Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:51.705Z - Handled GLOB. Handling time: 6. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:51.714Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:51.716Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:51.716Z - Handled HASH_GLOB. Handling time: 5. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:51.861Z - Time taken for 'build-project-configs' 174.1338999979198ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:51.925Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:51.925Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:51.926Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:52:51.926Z - Time taken for 'total execution time for createProjectGraph()' 50.797599997371435ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:53:33.874Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:53:33.875Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:53:34.281Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T12:53:34.281Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T12:53:34.281Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T12:53:34.291Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:53:34.291Z - Time taken for 'hash changed files from watcher' 0.9387999996542931ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:53:34.292Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:53:34.292Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:53:34.298Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:53:34.299Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:53:34.299Z - Handled HASH_GLOB. Handling time: 5. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:53:34.469Z - Time taken for 'build-project-configs' 174.79569999873638ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:53:34.545Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:53:34.546Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:53:34.546Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:53:34.546Z - Time taken for 'total execution time for createProjectGraph()' 62.11490000039339ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:54:28.639Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:54:28.640Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:54:29.456Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T12:54:29.456Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T12:54:29.456Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T12:54:29.470Z - Time taken for 'hash changed files from watcher' 0.8995000012218952ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:54:29.477Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:54:29.478Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:54:29.478Z - Handled GLOB. Handling time: 4. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:54:29.483Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:54:29.485Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:54:29.485Z - Handled HASH_GLOB. Handling time: 2. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:54:29.727Z - Time taken for 'build-project-configs' 252.47060000151396ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:54:29.799Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:54:29.800Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:54:29.800Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:54:29.800Z - Time taken for 'total execution time for createProjectGraph()' 62.41950000077486ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:55:35.832Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:55:35.832Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:55:37.446Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T12:55:37.446Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T12:55:37.446Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T12:55:37.454Z - Time taken for 'hash changed files from watcher' 0.5439999997615814ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:55:37.458Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:55:37.459Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:55:37.459Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:55:37.464Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:55:37.465Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:55:37.465Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:55:37.606Z - Time taken for 'build-project-configs' 146.35710000246763ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:55:37.677Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:55:37.678Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:55:37.678Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:55:37.679Z - Time taken for 'total execution time for createProjectGraph()' 52.49230000004172ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:57:20.202Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:57:20.207Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:57:23.413Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T12:57:23.413Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T12:57:23.413Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T12:57:23.422Z - Time taken for 'hash changed files from watcher' 1.3536000028252602ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:57:23.427Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:57:23.430Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:57:23.430Z - Handled GLOB. Handling time: 3. Response time: 3.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:57:23.436Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:57:23.437Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:57:23.437Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:57:23.676Z - Time taken for 'build-project-configs' 243.0997000001371ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:57:23.786Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:57:23.787Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:57:23.788Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:57:23.788Z - Time taken for 'total execution time for createProjectGraph()' 89.43780000135303ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:59:02.379Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T12:59:02.380Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T12:59:08.791Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T12:59:08.791Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T12:59:08.791Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T12:59:08.800Z - Time taken for 'hash changed files from watcher' 1.2148000001907349ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:59:08.805Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:59:08.806Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:59:08.806Z - Handled GLOB. Handling time: 3. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:59:08.812Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:59:08.813Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T12:59:08.813Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T12:59:09.216Z - Time taken for 'build-project-configs' 346.08590000122786ms
[NX v21.0.3 Daemon Server] - 2025-06-17T12:59:09.402Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:59:09.403Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:59:09.403Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T12:59:09.403Z - Time taken for 'total execution time for createProjectGraph()' 200.09149999916553ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:07:38.212Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:07:38.217Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:07:44.622Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:07:44.623Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:07:44.623Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:07:44.650Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:07:44.651Z - Time taken for 'hash changed files from watcher' 1.7282999977469444ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:07:44.653Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:07:44.653Z - Handled GLOB. Handling time: 2. Response time: 3.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:07:44.662Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:07:44.663Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:07:44.663Z - Handled HASH_GLOB. Handling time: 6. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:07:45.285Z - Time taken for 'build-project-configs' 612.7703999988735ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:07:45.517Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:07:45.519Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:07:45.521Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:07:45.521Z - Time taken for 'total execution time for createProjectGraph()' 194.2449999973178ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:11.733Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:11.734Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:15.461Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:15.478Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:18.146Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:18.147Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:18.147Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:18.171Z - Time taken for 'hash changed files from watcher' 2.2135999985039234ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:18.180Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:18.183Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:18.183Z - Handled GLOB. Handling time: 5. Response time: 3.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:18.217Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:18.229Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:18.229Z - Handled HASH_GLOB. Handling time: 31. Response time: 12.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:18.618Z - Time taken for 'build-project-configs' 440.8889999985695ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:18.719Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:18.721Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:18.721Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:18.721Z - Time taken for 'total execution time for createProjectGraph()' 77.59630000218749ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:20.326Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:20.327Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:26.745Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:26.745Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:26.746Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:26.772Z - Time taken for 'hash changed files from watcher' 3.562600001692772ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:26.795Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:26.799Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:26.799Z - Handled GLOB. Handling time: 8. Response time: 4.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:26.814Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:26.821Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:26.822Z - Handled HASH_GLOB. Handling time: 11. Response time: 8.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:27.162Z - Time taken for 'build-project-configs' 358.0595999993384ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:27.324Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:27.325Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:27.326Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:18:27.326Z - Time taken for 'total execution time for createProjectGraph()' 156.24289999902248ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:00.504Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:00.504Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:06.913Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:06.913Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:06.913Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:06.928Z - Time taken for 'hash changed files from watcher' 1.939699999988079ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:06.938Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:06.940Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:06.940Z - Handled GLOB. Handling time: 4. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:06.946Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:06.947Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:06.947Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:07.052Z - Time taken for 'build-project-configs' 123.4400000013411ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:07.125Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:07.125Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:07.125Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:07.126Z - Time taken for 'total execution time for createProjectGraph()' 60.39190000295639ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:12.753Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:12.753Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:15.327Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:15.327Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:19.162Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:19.162Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:19.162Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:19.178Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:19.178Z - Time taken for 'hash changed files from watcher' 1.0434999987483025ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:19.179Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:19.179Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:19.185Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:19.186Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:19.186Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:19.312Z - Time taken for 'build-project-configs' 137.71640000119805ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:19.377Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:19.378Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:19.378Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:19:19.378Z - Time taken for 'total execution time for createProjectGraph()' 49.08800000324845ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:20:31.782Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:20:31.783Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:20:38.190Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:20:38.190Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:20:38.190Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:20:38.196Z - Time taken for 'hash changed files from watcher' 0.6303999982774258ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:20:38.199Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:20:38.199Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:20:38.199Z - Handled GLOB. Handling time: 2. Response time: 0.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:20:38.203Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:20:38.204Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:20:38.204Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:20:38.310Z - Time taken for 'build-project-configs' 110.61830000206828ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:20:38.377Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:20:38.377Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:20:38.378Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:20:38.378Z - Time taken for 'total execution time for createProjectGraph()' 48.75950000062585ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:06.059Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:06.059Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:09.298Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:09.300Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:11.810Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:11.811Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:12.466Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:12.466Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:12.466Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:12.488Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:12.489Z - Time taken for 'hash changed files from watcher' 0.6426999978721142ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:12.489Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:12.489Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:12.499Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:12.503Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:12.503Z - Handled HASH_GLOB. Handling time: 6. Response time: 4.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:12.527Z - Time taken for 'build-project-configs' 48.48470000177622ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:12.596Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:12.597Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:12.597Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:12.597Z - Time taken for 'total execution time for createProjectGraph()' 54.054899998009205ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:14.314Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:14.314Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:20.716Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:20.716Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:20.716Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:20.721Z - Time taken for 'hash changed files from watcher' 0.9396000020205975ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:20.724Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:20.724Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:20.725Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:20.729Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:20.729Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:20.729Z - Handled HASH_GLOB. Handling time: 3. Response time: 0.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:20.756Z - Time taken for 'build-project-configs' 28.990600001066923ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:20.824Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:20.825Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:20.825Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:23:20.825Z - Time taken for 'total execution time for createProjectGraph()' 53.50450000166893ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:25:37.415Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:25:37.416Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:25:40.835Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:25:40.836Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:25:42.342Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:25:42.342Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:25:43.830Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:25:43.830Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:25:43.830Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:25:43.835Z - Time taken for 'hash changed files from watcher' 0.6059000007808208ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:25:43.838Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:25:43.840Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:25:43.840Z - Handled GLOB. Handling time: 1. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:25:43.845Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:25:43.845Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:25:43.845Z - Handled HASH_GLOB. Handling time: 3. Response time: 0.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:25:43.962Z - Time taken for 'build-project-configs' 121.57539999857545ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:25:44.027Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:25:44.028Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:25:44.028Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:25:44.028Z - Time taken for 'total execution time for createProjectGraph()' 48.79799999669194ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:25:59.947Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:25:59.948Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:03.694Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:03.694Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:06.357Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:06.357Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:06.357Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:06.380Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:06.382Z - Time taken for 'hash changed files from watcher' 1.4114000014960766ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:06.383Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:06.383Z - Handled GLOB. Handling time: 3. Response time: 3.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:06.388Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:06.389Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:06.389Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:06.694Z - Time taken for 'build-project-configs' 302.52730000019073ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:06.795Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:06.796Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:06.797Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:06.797Z - Time taken for 'total execution time for createProjectGraph()' 84.15420000255108ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:07.289Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:07.296Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:13.699Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:13.699Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:13.699Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:13.706Z - Time taken for 'hash changed files from watcher' 0.5903000012040138ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:13.710Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:13.711Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:13.711Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:13.717Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:13.717Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:13.717Z - Handled HASH_GLOB. Handling time: 4. Response time: 0.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:13.912Z - Time taken for 'build-project-configs' 195.79160000011325ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:13.998Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:13.999Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:13.999Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:14.000Z - Time taken for 'total execution time for createProjectGraph()' 69.64110000059009ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:35.754Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:35.755Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:42.167Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:42.167Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:42.167Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:42.180Z - Time taken for 'hash changed files from watcher' 1.3888000026345253ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:42.186Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:42.187Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:42.187Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:42.194Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:42.194Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:42.194Z - Handled HASH_GLOB. Handling time: 4. Response time: 0.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:42.306Z - Time taken for 'build-project-configs' 123.37889999896288ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:42.359Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:42.360Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:42.360Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:26:42.360Z - Time taken for 'total execution time for createProjectGraph()' 42.264299999922514ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:41.386Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:41.387Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:43.089Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:43.089Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:47.802Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:47.802Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:47.802Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:47.814Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:47.815Z - Time taken for 'hash changed files from watcher' 0.9173000007867813ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:47.816Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:47.816Z - Handled GLOB. Handling time: 1. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:47.820Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:47.820Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:47.820Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:47.842Z - Time taken for 'build-project-configs' 29.38069999963045ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:47.899Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:47.900Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:47.900Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:47.900Z - Time taken for 'total execution time for createProjectGraph()' 41.05350000038743ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:50.761Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:50.761Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:57.165Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:57.165Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:57.165Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:57.173Z - Time taken for 'hash changed files from watcher' 1.1367000006139278ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:57.177Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:57.178Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:57.178Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:57.182Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:57.183Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:57.183Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:57.314Z - Time taken for 'build-project-configs' 134.160000000149ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:57.385Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:57.386Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:57.386Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:29:57.386Z - Time taken for 'total execution time for createProjectGraph()' 56.17749999836087ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:04.163Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:04.164Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:10.575Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:10.576Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:10.576Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:10.587Z - Time taken for 'hash changed files from watcher' 1.0903999991714954ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:10.592Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:10.593Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:10.593Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:10.600Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:10.601Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:10.601Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:10.707Z - Time taken for 'build-project-configs' 118.65479999780655ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:10.764Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:10.764Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:10.765Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:10.765Z - Time taken for 'total execution time for createProjectGraph()' 43.63580000028014ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:14.052Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:14.070Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:20.476Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:20.476Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:20.476Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:20.481Z - Time taken for 'hash changed files from watcher' 0.5758000016212463ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:20.484Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:20.484Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:20.484Z - Handled GLOB. Handling time: 1. Response time: 0.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:20.488Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:20.488Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:20.488Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:20.584Z - Time taken for 'build-project-configs' 100.02580000087619ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:20.635Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:20.636Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:20.636Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:20.636Z - Time taken for 'total execution time for createProjectGraph()' 35.11309999972582ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:46.703Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:46.704Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:53.110Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:53.110Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:53.110Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:53.128Z - Time taken for 'hash changed files from watcher' 1.8936000019311905ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:53.131Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:53.132Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:53.132Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:53.138Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:53.139Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:53.141Z - Handled HASH_GLOB. Handling time: 4. Response time: 3.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:53.257Z - Time taken for 'build-project-configs' 136.277499999851ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:53.315Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:53.315Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:53.316Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:30:53.316Z - Time taken for 'total execution time for createProjectGraph()' 40.20170000195503ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:15.365Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:15.367Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:21.782Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:21.782Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:21.782Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:21.796Z - Time taken for 'hash changed files from watcher' 2.528099998831749ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:21.801Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:21.803Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:21.803Z - Handled GLOB. Handling time: 1. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:21.807Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:21.808Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:21.808Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:21.918Z - Time taken for 'build-project-configs' 122.68699999898672ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:21.988Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:21.989Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:21.989Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:21.989Z - Time taken for 'total execution time for createProjectGraph()' 56.34400000050664ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:22.573Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:22.573Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:24.876Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:24.877Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:28.474Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:28.475Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:28.978Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:28.978Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:28.978Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:28.995Z - Time taken for 'hash changed files from watcher' 1.0502999983727932ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:29.004Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:29.005Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:29.005Z - Handled GLOB. Handling time: 6. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:29.029Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:29.042Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:29.042Z - Handled HASH_GLOB. Handling time: 20. Response time: 13.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:29.325Z - Time taken for 'build-project-configs' 326.5909000001848ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:29.429Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:29.431Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:29.431Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:29.431Z - Time taken for 'total execution time for createProjectGraph()' 74.78469999879599ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:32.355Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:32.358Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:38.774Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:38.774Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:38.774Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:38.793Z - Time taken for 'hash changed files from watcher' 1.1326999999582767ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:38.798Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:38.800Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:38.800Z - Handled GLOB. Handling time: 2. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:38.808Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:38.809Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:38.809Z - Handled HASH_GLOB. Handling time: 4. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:39.017Z - Time taken for 'build-project-configs' 223.76209999993443ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:39.107Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:39.108Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:39.109Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:39.109Z - Time taken for 'total execution time for createProjectGraph()' 72.61409999802709ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:54.671Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:31:54.672Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:01.075Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:01.075Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:01.075Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:01.088Z - Time taken for 'hash changed files from watcher' 1.6620000004768372ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:01.090Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:01.091Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:01.091Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:01.094Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:01.095Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:01.095Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:01.215Z - Time taken for 'build-project-configs' 126.47390000149608ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:01.273Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:01.274Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:01.274Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:01.274Z - Time taken for 'total execution time for createProjectGraph()' 45.969900000840425ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:05.912Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:05.913Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:10.374Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:10.374Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:12.327Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:12.327Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:12.327Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:12.333Z - Time taken for 'hash changed files from watcher' 0.6659000031650066ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:12.336Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:12.337Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:12.337Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:12.340Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:12.341Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:12.341Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:12.481Z - Time taken for 'build-project-configs' 142.55169999971986ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:12.552Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:12.553Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:12.553Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:12.553Z - Time taken for 'total execution time for createProjectGraph()' 54.46969999745488ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:21.302Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:21.304Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:22.411Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:22.418Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:24.921Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:24.922Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:27.721Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:27.721Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:27.721Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:27.736Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:27.736Z - Time taken for 'hash changed files from watcher' 1.0879999995231628ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:27.737Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:27.738Z - Handled GLOB. Handling time: 2. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:27.743Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:27.744Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:27.744Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:27.903Z - Time taken for 'build-project-configs' 169.6713999994099ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:27.965Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:27.966Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:27.966Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:27.966Z - Time taken for 'total execution time for createProjectGraph()' 45.79500000178814ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:29.260Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:29.262Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:35.676Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:35.676Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:35.676Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:35.685Z - Time taken for 'hash changed files from watcher' 0.9522000029683113ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:35.689Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:35.690Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:35.690Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:35.695Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:35.695Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:35.695Z - Handled HASH_GLOB. Handling time: 3. Response time: 0.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:35.880Z - Time taken for 'build-project-configs' 180.06410000100732ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:35.967Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:35.969Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:35.969Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:32:35.969Z - Time taken for 'total execution time for createProjectGraph()' 76.72240000218153ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:33:25.519Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:33:25.519Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:33:27.249Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:33:27.249Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:33:31.923Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:33:31.923Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:33:31.923Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:33:31.935Z - Time taken for 'hash changed files from watcher' 1.3948999978601933ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:33:31.939Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:33:31.940Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:33:31.940Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:33:31.945Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:33:31.946Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:33:31.946Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:33:32.081Z - Time taken for 'build-project-configs' 138.44069999828935ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:33:32.142Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:33:32.142Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:33:32.143Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:33:32.143Z - Time taken for 'total execution time for createProjectGraph()' 52.14069999754429ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:34:03.752Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:34:03.752Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:34:06.163Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:34:06.166Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:34:10.166Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:34:10.166Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:34:10.166Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:34:10.194Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:34:10.201Z - Time taken for 'hash changed files from watcher' 1.1121999993920326ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:34:10.204Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:34:10.204Z - Handled GLOB. Handling time: 6. Response time: 10.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:34:10.211Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:34:10.226Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:34:10.226Z - Handled HASH_GLOB. Handling time: 5. Response time: 15.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:34:10.393Z - Time taken for 'build-project-configs' 215.51919999718666ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:34:10.444Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:34:10.445Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:34:10.445Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:34:10.445Z - Time taken for 'total execution time for createProjectGraph()' 38.02710000053048ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:34:55.190Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:34:55.191Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:34:57.452Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:34:57.454Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:01.602Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:01.602Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:01.603Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:01.625Z - Time taken for 'hash changed files from watcher' 0.9538000002503395ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:01.658Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:01.687Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:01.687Z - Handled GLOB. Handling time: 19. Response time: 29.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:01.700Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:01.701Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:01.702Z - Handled HASH_GLOB. Handling time: 11. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:01.735Z - Time taken for 'build-project-configs' 99.1667999997735ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:01.824Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:01.825Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:01.826Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:01.826Z - Time taken for 'total execution time for createProjectGraph()' 78.0257000029087ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:08.938Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:08.938Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:10.877Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:10.878Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:13.401Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:13.402Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:15.353Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:15.353Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:15.353Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:15.368Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:15.369Z - Time taken for 'hash changed files from watcher' 1.0703000016510487ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:15.370Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:15.370Z - Handled GLOB. Handling time: 1. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:15.374Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:15.375Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:15.375Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:15.584Z - Time taken for 'build-project-configs' 192.32490000128746ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:15.705Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:15.707Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:15.707Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:35:15.707Z - Time taken for 'total execution time for createProjectGraph()' 115.86510000005364ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:40:12.446Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:40:12.449Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:40:18.852Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:40:18.852Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:40:18.852Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:40:18.860Z - Time taken for 'hash changed files from watcher' 1.012499999254942ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:40:18.868Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:40:18.869Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:40:18.869Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:40:18.874Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:40:18.875Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:40:18.875Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:40:19.180Z - Time taken for 'build-project-configs' 298.1119999997318ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:40:19.388Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:40:19.390Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:40:19.390Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:40:19.391Z - Time taken for 'total execution time for createProjectGraph()' 165.83520000055432ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:41:02.399Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:41:02.407Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:41:04.206Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:41:04.207Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:41:08.814Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:41:08.814Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:41:08.814Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:41:08.820Z - Time taken for 'hash changed files from watcher' 1.0122000016272068ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:41:08.823Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:41:08.824Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:41:08.824Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:41:08.828Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:41:08.828Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:41:08.829Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:41:08.944Z - Time taken for 'build-project-configs' 119.00569999963045ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:41:09.010Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:41:09.011Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:41:09.011Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:41:09.012Z - Time taken for 'total execution time for createProjectGraph()' 50.1625999994576ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:43:44.488Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:43:44.488Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:43:45.881Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:43:45.883Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:43:50.895Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:43:50.895Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:43:50.895Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:43:50.914Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:43:50.915Z - Time taken for 'hash changed files from watcher' 1.3432000018656254ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:43:50.917Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:43:50.917Z - Handled GLOB. Handling time: 3. Response time: 3.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:43:50.925Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:43:50.926Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:43:50.926Z - Handled HASH_GLOB. Handling time: 6. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:43:50.969Z - Time taken for 'build-project-configs' 58.32379999756813ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:43:51.122Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:43:51.128Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:43:51.128Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:43:51.139Z - Time taken for 'total execution time for createProjectGraph()' 85.62150000035763ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:44:05.930Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:44:05.932Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:44:12.333Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:44:12.333Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:44:12.334Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:44:12.341Z - Time taken for 'hash changed files from watcher' 1.420399997383356ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:44:12.347Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:44:12.350Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:44:12.350Z - Handled GLOB. Handling time: 2. Response time: 3.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:44:12.359Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:44:12.360Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:44:12.360Z - Handled HASH_GLOB. Handling time: 4. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:44:12.558Z - Time taken for 'build-project-configs' 202.05539999902248ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:44:12.667Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:44:12.669Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:44:12.670Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:44:12.670Z - Time taken for 'total execution time for createProjectGraph()' 89.19920000061393ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:44:51.045Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:44:51.046Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:44:57.450Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:44:57.451Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:44:57.451Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:44:57.476Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:44:57.476Z - Time taken for 'hash changed files from watcher' 1.9325000010430813ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:44:57.477Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:44:57.478Z - Handled GLOB. Handling time: 2. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:44:57.485Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:44:57.486Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:44:57.486Z - Handled HASH_GLOB. Handling time: 4. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:44:57.842Z - Time taken for 'build-project-configs' 365.9145999997854ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:44:57.973Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:44:57.975Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:44:57.976Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:44:57.976Z - Time taken for 'total execution time for createProjectGraph()' 90.43340000137687ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:23.936Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:23.939Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:27.474Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:27.475Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:28.542Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:28.544Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:30.350Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:30.350Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:30.350Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:30.368Z - Time taken for 'hash changed files from watcher' 1.2252000011503696ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:30.377Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:30.379Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:30.379Z - Handled GLOB. Handling time: 3. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:30.389Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:30.390Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:30.390Z - Handled HASH_GLOB. Handling time: 4. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:30.634Z - Time taken for 'build-project-configs' 266.9882999993861ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:30.713Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:30.714Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:30.714Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:30.715Z - Time taken for 'total execution time for createProjectGraph()' 62.12629999965429ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:46.734Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:46.734Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:53.142Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:53.142Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:53.143Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:53.156Z - Time taken for 'hash changed files from watcher' 1.5725000016391277ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:53.162Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:53.163Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:53.163Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:53.169Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:53.170Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:53.170Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:53.453Z - Time taken for 'build-project-configs' 282.6701999977231ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:53.581Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:53.583Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:53.583Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:46:53.583Z - Time taken for 'total execution time for createProjectGraph()' 104.7453000023961ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:47:04.264Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-17T13:47:04.264Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-17T13:47:10.676Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-17T13:47:10.676Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html
[NX v21.0.3 Daemon Server] - 2025-06-17T13:47:10.676Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-17T13:47:10.689Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:47:10.690Z - Time taken for 'hash changed files from watcher' 0.9149999991059303ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:47:10.691Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:47:10.691Z - Handled GLOB. Handling time: 2. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:47:10.702Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:47:10.702Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-17T13:47:10.703Z - Handled HASH_GLOB. Handling time: 5. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-17T13:47:10.940Z - Time taken for 'build-project-configs' 253.09299999848008ms
[NX v21.0.3 Daemon Server] - 2025-06-17T13:47:11.050Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:47:11.052Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:47:11.052Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-17T13:47:11.052Z - Time taken for 'total execution time for createProjectGraph()' 67.3049000017345ms
[NX v21.0.3 Daemon Server] - 2025-06-17T16:47:10.684Z - Closed a connection. Number of open connections: 2
[NX v21.0.3 Daemon Server] - 2025-06-17T16:47:10.688Z - Closed a connection. Number of open connections: 1
[NX v21.0.3 Daemon Server] - 2025-06-17T16:47:10.688Z - Closed a connection. Number of open connections: 0
[NX v21.0.3 Daemon Server] - 2025-06-17T16:47:10.690Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Desktop\New folder\propped (sources)
[NX v21.0.3 Daemon Server] - 2025-06-17T16:47:10.691Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Desktop\New folder\propped (outputs)
[NX v21.0.3 Daemon Server] - 2025-06-17T16:47:10.703Z - Server stopped because: "10800000ms of inactivity"
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:04.216Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\7ca78b1b0c7c2317476f\d.sock
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:04.246Z - [WATCHER]: Subscribed to changes within: C:\Users\<USER>\Desktop\New folder\propped (native)
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:04.250Z - Established a connection. Number of open connections: 1
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:04.251Z - Established a connection. Number of open connections: 2
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:04.254Z - Closed a connection. Number of open connections: 1
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:04.256Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:05.151Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\Desktop\New folder\propped\node_modules\.pnpm\nx@21.0.3_@swc-node+registe_3ea8e76b521e497598bf8132b28aac73\node_modules\nx\src\plugins\package-json' 870.8742ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:05.191Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\Desktop\New folder\propped\node_modules\.pnpm\nx@21.0.3_@swc-node+registe_3ea8e76b521e497598bf8132b28aac73\node_modules\nx\src\plugins\project-json\build-nodes\project-json' 902.1176000000002ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:05.312Z - Time taken for 'loadDefaultNxPlugins' 1035.6246ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:06.568Z - Time taken for 'Load Nx Plugin: @nx/eslint/plugin' 2299.1483ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:07.252Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:07.252Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:07.252Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:07.271Z - Time taken for 'loadSpecifiedNxPlugins' 2988.2621ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:07.277Z - Established a connection. Number of open connections: 2
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:07.278Z - Closed a connection. Number of open connections: 1
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:07.278Z - Established a connection. Number of open connections: 2
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:07.283Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:07.284Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:07.284Z - Handled GLOB. Handling time: 3. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:07.295Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:07.295Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:07.295Z - Handled HASH_GLOB. Handling time: 5. Response time: 0.
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:14.647Z - Time taken for 'build-project-configs' 7333.9833ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:14.798Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:14.800Z - [REQUEST]: Responding to the client. project-graph
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:14.804Z - Time taken for 'total for creating and serializing project graph' 10543.594500000001ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:14.810Z - Done responding to the client project-graph
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:14.810Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 10544. Response time: 10.
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:14.832Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:14.832Z - Time taken for 'preTasksExecution' 1.0641000000014174ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:14.832Z - Done responding to the client handleRunPreTasksExecution
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:14.832Z - Handled PRE_TASKS_EXECUTION. Handling time: 2. Response time: 0.
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:15.019Z - [REQUEST]: Responding to the client. handleHashTasks
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:15.020Z - Done responding to the client handleHashTasks
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:15.020Z - Handled HASH_TASKS. Handling time: 59. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:15.070Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:15.071Z - Done responding to the client handleGetEstimatedTaskTimings
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:15.071Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 7. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:16.927Z - Established a connection. Number of open connections: 3
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:16.928Z - Closed a connection. Number of open connections: 2
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:16.929Z - Established a connection. Number of open connections: 3
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:16.932Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:16.933Z - [REQUEST]: Responding to the client. project-graph
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:16.935Z - Time taken for 'total for creating and serializing project graph' 0.5797000000002299ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:16.938Z - Done responding to the client project-graph
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:16.939Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 6.
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:44.659Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:46.634Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:46.686Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:18:46.904Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:19:03.987Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:19:04.045Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:19:04.151Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:19:04.219Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:19:04.404Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:19:04.514Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:19:04.664Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:19:04.727Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:19:04.864Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:19:04.944Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:19:05.194Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:19:05.275Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:19:05.606Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:19:05.672Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:19:05.804Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:19:05.855Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:19:05.921Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:49:58.160Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T06:49:58.162Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:49:58.290Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T06:49:58.291Z - [REQUEST]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.html
[NX v21.0.3 Daemon Server] - 2025-06-26T06:49:58.291Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T06:49:58.307Z - Time taken for 'hash changed files from watcher' 3.158500000135973ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:49:58.359Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:49:58.360Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:49:58.360Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T06:49:58.366Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:49:58.366Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:49:58.367Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T06:49:58.514Z - Time taken for 'build-project-configs' 199.24109999998473ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:49:58.598Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:49:58.600Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:49:58.600Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:49:58.601Z - Time taken for 'total execution time for createProjectGraph()' 56.19429999985732ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:50:11.376Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:50:11.378Z - [WATCHER]: 1 file(s) created or restored, 0 file(s) modified, 1 file(s) deleted
[NX v21.0.3 Daemon Server] - 2025-06-26T06:50:11.483Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T06:50:11.483Z - [REQUEST]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.html
[NX v21.0.3 Daemon Server] - 2025-06-26T06:50:11.483Z - [REQUEST]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.html
[NX v21.0.3 Daemon Server] - 2025-06-26T06:50:11.490Z - Time taken for 'hash changed files from watcher' 0.79480000003241ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:50:11.494Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:50:11.495Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:50:11.495Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T06:50:11.500Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:50:11.500Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:50:11.500Z - Handled HASH_GLOB. Handling time: 2. Response time: 0.
[NX v21.0.3 Daemon Server] - 2025-06-26T06:50:11.618Z - Time taken for 'build-project-configs' 125.02989999996498ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:50:11.694Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:50:11.696Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:50:11.696Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:50:11.696Z - Time taken for 'total execution time for createProjectGraph()' 51.778000000165775ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:50:24.243Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:50:24.244Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T06:50:24.460Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T06:50:24.460Z - [REQUEST]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts
[NX v21.0.3 Daemon Server] - 2025-06-26T06:50:24.461Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T06:50:24.469Z - Time taken for 'hash changed files from watcher' 0.8738000001758337ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:50:24.480Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:50:24.481Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:50:24.481Z - Handled GLOB. Handling time: 9. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T06:50:24.487Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:50:24.488Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:50:24.488Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T06:50:24.627Z - Time taken for 'build-project-configs' 154.03969999984838ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:50:24.701Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:50:24.702Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:50:24.702Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:50:24.702Z - Time taken for 'total execution time for createProjectGraph()' 59.91770000010729ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:10.461Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/buyer-seller.routes.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:10.471Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:10.870Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:10.870Z - [REQUEST]: packages/web/src/app/pages/buyer-seller/buyer-seller.routes.ts
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:10.870Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:10.880Z - Time taken for 'hash changed files from watcher' 0.937900000018999ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:10.886Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:10.887Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:10.887Z - Handled GLOB. Handling time: 4. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:10.900Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:10.901Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:10.901Z - Handled HASH_GLOB. Handling time: 10. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:11.116Z - Time taken for 'build-project-configs' 227.52940000011586ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:11.228Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:11.229Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:11.230Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:11.230Z - Time taken for 'total execution time for createProjectGraph()' 91.89529999997467ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:12.190Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:12.191Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/buyer-seller.routes.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:12.996Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:12.996Z - [REQUEST]: packages/web/src/app/pages/buyer-seller/buyer-seller.routes.ts
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:12.996Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:13.006Z - Time taken for 'hash changed files from watcher' 0.7175000000279397ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:13.010Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:13.011Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:13.011Z - Handled GLOB. Handling time: 1. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:13.016Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:13.016Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:13.016Z - Handled HASH_GLOB. Handling time: 3. Response time: 0.
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:13.131Z - Time taken for 'build-project-configs' 123.3355000000447ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:13.193Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:13.194Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:13.194Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:13.194Z - Time taken for 'total execution time for createProjectGraph()' 47.095499999821186ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:13.645Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:13.646Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/buyer-seller.routes.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:14.979Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:14.985Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/buyer-seller.routes.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:15.257Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:15.257Z - [REQUEST]: packages/web/src/app/pages/buyer-seller/buyer-seller.routes.ts
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:15.257Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:15.282Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:15.283Z - Time taken for 'hash changed files from watcher' 1.282599999802187ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:15.284Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:15.284Z - Handled GLOB. Handling time: 5. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:15.292Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:15.292Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:15.292Z - Handled HASH_GLOB. Handling time: 6. Response time: 0.
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:15.324Z - Time taken for 'build-project-configs' 49.63840000005439ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:15.404Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:15.405Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:15.405Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:15.405Z - Time taken for 'total execution time for createProjectGraph()' 60.89889999991283ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:16.700Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/buyer-seller.routes.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:16.700Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:19.910Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:19.910Z - [REQUEST]: packages/web/src/app/pages/buyer-seller/buyer-seller.routes.ts
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:19.911Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:19.918Z - Time taken for 'hash changed files from watcher' 0.754600000102073ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:19.922Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:19.923Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:19.924Z - Handled GLOB. Handling time: 1. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:19.928Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:19.928Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:19.929Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:20.098Z - Time taken for 'build-project-configs' 170.59400000004098ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:20.195Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:20.196Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:20.196Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:20.196Z - Time taken for 'total execution time for createProjectGraph()' 75.67530000000261ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:28.781Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:38.412Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:38.413Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/buyer-seller.routes.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:41.282Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/buyer-seller.routes.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:41.282Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:43.397Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:44.820Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:44.821Z - [REQUEST]: packages/web/src/app/pages/buyer-seller/buyer-seller.routes.ts
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:44.821Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:44.834Z - Time taken for 'hash changed files from watcher' 1.385899999877438ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:44.859Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:44.860Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:44.860Z - Handled GLOB. Handling time: 19. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:44.880Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:44.890Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:44.890Z - Handled HASH_GLOB. Handling time: 17. Response time: 10.
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:45.216Z - Time taken for 'build-project-configs' 355.04750000010245ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:45.341Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:45.343Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:45.343Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:45.343Z - Time taken for 'total execution time for createProjectGraph()' 113.07539999997243ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:46.034Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:46.036Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/buyer-seller.routes.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:49.974Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:52.450Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:52.450Z - [REQUEST]: packages/web/src/app/pages/buyer-seller/buyer-seller.routes.ts
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:52.450Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:52.457Z - Time taken for 'hash changed files from watcher' 0.8114000000059605ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:52.460Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:52.461Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:52.461Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:52.466Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:52.466Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:52.466Z - Handled HASH_GLOB. Handling time: 3. Response time: 0.
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:52.494Z - Time taken for 'build-project-configs' 31.969600000185892ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:52.557Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:52.558Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:52.558Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:51:52.558Z - Time taken for 'total execution time for createProjectGraph()' 46.66319999983534ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:55:56.499Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:55:56.501Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T06:56:02.907Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T06:56:02.907Z - [REQUEST]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts
[NX v21.0.3 Daemon Server] - 2025-06-26T06:56:02.907Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T06:56:02.915Z - Time taken for 'hash changed files from watcher' 1.4387000002898276ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:56:02.920Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:56:02.921Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:56:02.921Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T06:56:02.926Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:56:02.927Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:56:02.927Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T06:56:03.095Z - Time taken for 'build-project-configs' 176.57319999998435ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:56:03.179Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:56:03.180Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:56:03.180Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:56:03.181Z - Time taken for 'total execution time for createProjectGraph()' 61.592999999877065ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:56:04.737Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:21.387Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:21.388Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:23.794Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:23.800Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:25.088Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:25.090Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:27.429Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:27.430Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:27.709Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:27.810Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:27.810Z - [REQUEST]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts,packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.html
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:27.810Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:27.820Z - Time taken for 'hash changed files from watcher' 7.568199999630451ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:27.824Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:27.825Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:27.825Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:27.830Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:27.830Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:27.831Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:27.996Z - Time taken for 'build-project-configs' 170.93720000004396ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:28.059Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:28.060Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:28.060Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:28.061Z - Time taken for 'total execution time for createProjectGraph()' 51.9336000001058ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:28.958Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:28.973Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:34.254Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:34.269Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:35.366Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:35.366Z - [REQUEST]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:35.366Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:35.373Z - Time taken for 'hash changed files from watcher' 0.6877000001259148ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:35.376Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:35.377Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:35.377Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:35.381Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:35.382Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:35.382Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:35.547Z - Time taken for 'build-project-configs' 159.35470000002533ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:35.636Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:35.637Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:35.637Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:35.637Z - Time taken for 'total execution time for createProjectGraph()' 80.40800000028685ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:38.670Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:42.358Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:42.358Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:45.223Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:48.775Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:48.775Z - [REQUEST]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:48.775Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:48.796Z - Time taken for 'hash changed files from watcher' 1.348800000268966ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:48.800Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:48.802Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:48.802Z - Handled GLOB. Handling time: 2. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:48.813Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:48.814Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:48.814Z - Handled HASH_GLOB. Handling time: 8. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:48.856Z - Time taken for 'build-project-configs' 60.61929999990389ms
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:48.958Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:48.959Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:48.960Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T06:57:48.960Z - Time taken for 'total execution time for createProjectGraph()' 87.05110000027344ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:06:19.558Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T07:06:19.559Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:06:24.351Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:06:25.973Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T07:06:25.973Z - [REQUEST]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts
[NX v21.0.3 Daemon Server] - 2025-06-26T07:06:25.973Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T07:06:25.983Z - Time taken for 'hash changed files from watcher' 3.225899999961257ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:06:25.988Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:06:25.989Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:06:25.989Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:06:25.994Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:06:25.995Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:06:25.995Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:06:26.167Z - Time taken for 'build-project-configs' 178.38609999977052ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:06:26.244Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:06:26.246Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:06:26.246Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:06:26.246Z - Time taken for 'total execution time for createProjectGraph()' 58.135300000198185ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:10:38.973Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/buyer-seller.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T07:10:38.973Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:10:45.390Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T07:10:45.412Z - [REQUEST]: packages/web/src/app/pages/buyer-seller/buyer-seller.component.ts
[NX v21.0.3 Daemon Server] - 2025-06-26T07:10:45.413Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T07:10:45.504Z - Time taken for 'hash changed files from watcher' 1.4725000001490116ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:10:45.603Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:10:45.614Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:10:45.615Z - Handled GLOB. Handling time: 96. Response time: 12.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:10:45.796Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:10:45.797Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:10:45.798Z - Handled HASH_GLOB. Handling time: 179. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:10:45.902Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:10:46.329Z - Time taken for 'build-project-configs' 817.2587000001222ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:10:46.555Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:10:46.556Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:10:46.556Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:10:46.556Z - Time taken for 'total execution time for createProjectGraph()' 150.7190999998711ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:11:45.425Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/buyer-seller.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T07:11:45.425Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:11:46.989Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:11:46.990Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/buyer-seller.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T07:11:51.354Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:11:51.839Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T07:11:51.839Z - [REQUEST]: packages/web/src/app/pages/buyer-seller/buyer-seller.component.ts
[NX v21.0.3 Daemon Server] - 2025-06-26T07:11:51.839Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T07:11:51.859Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:11:51.859Z - Time taken for 'hash changed files from watcher' 2.250200000125915ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:11:51.860Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:11:51.861Z - Handled GLOB. Handling time: 2. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:11:51.872Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:11:51.873Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:11:51.873Z - Handled HASH_GLOB. Handling time: 8. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:11:52.254Z - Time taken for 'build-project-configs' 395.7431000000797ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:11:52.393Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:11:52.395Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:11:52.396Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:11:52.396Z - Time taken for 'total execution time for createProjectGraph()' 101.96540000010282ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:12:19.146Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/buyer-seller.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T07:12:19.147Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:12:24.698Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:12:25.555Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T07:12:25.555Z - [REQUEST]: packages/web/src/app/pages/buyer-seller/buyer-seller.component.ts
[NX v21.0.3 Daemon Server] - 2025-06-26T07:12:25.555Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T07:12:25.565Z - Time taken for 'hash changed files from watcher' 1.2379999998956919ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:12:25.570Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:12:25.572Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:12:25.572Z - Handled GLOB. Handling time: 1. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:12:25.577Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:12:25.578Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:12:25.578Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:12:25.876Z - Time taken for 'build-project-configs' 299.7594999996945ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:12:26.043Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:12:26.045Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:12:26.045Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:12:26.045Z - Time taken for 'total execution time for createProjectGraph()' 120.88280000025406ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:15:41.522Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:15:41.524Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T07:15:47.937Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T07:15:47.937Z - [REQUEST]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.html
[NX v21.0.3 Daemon Server] - 2025-06-26T07:15:47.937Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T07:15:47.945Z - Time taken for 'hash changed files from watcher' 0.9167000004090369ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:15:47.949Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:15:47.950Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:15:47.950Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:15:47.955Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:15:47.955Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:15:47.955Z - Handled HASH_GLOB. Handling time: 3. Response time: 0.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:15:48.065Z - Time taken for 'build-project-configs' 117.75839999970049ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:15:48.126Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:15:48.127Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:15:48.128Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:15:48.128Z - Time taken for 'total execution time for createProjectGraph()' 46.92460000002757ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:16:04.468Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T07:16:04.468Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:16:10.158Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:16:10.874Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T07:16:10.874Z - [REQUEST]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.html
[NX v21.0.3 Daemon Server] - 2025-06-26T07:16:10.874Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T07:16:10.894Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:16:10.894Z - Time taken for 'hash changed files from watcher' 1.1096999999135733ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:16:10.895Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:16:10.895Z - Handled GLOB. Handling time: 8. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:16:10.903Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:16:10.904Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:16:10.904Z - Handled HASH_GLOB. Handling time: 6. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:16:11.115Z - Time taken for 'build-project-configs' 204.3748999997042ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:16:11.228Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:16:11.231Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:16:11.232Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:16:11.232Z - Time taken for 'total execution time for createProjectGraph()' 96.84429999999702ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:17:33.707Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:17:33.708Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T07:17:40.122Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T07:17:40.122Z - [REQUEST]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.html
[NX v21.0.3 Daemon Server] - 2025-06-26T07:17:40.122Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T07:17:40.130Z - Time taken for 'hash changed files from watcher' 0.6647000000812113ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:17:40.136Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:17:40.137Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:17:40.138Z - Handled GLOB. Handling time: 1. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:17:40.143Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:17:40.143Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:17:40.143Z - Handled HASH_GLOB. Handling time: 4. Response time: 0.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:17:40.193Z - Time taken for 'build-project-configs' 50.93330000014976ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:17:40.286Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:17:40.287Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:17:40.287Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:17:40.287Z - Time taken for 'total execution time for createProjectGraph()' 83.50380000006407ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:17:49.806Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T07:17:49.807Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:17:56.211Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T07:17:56.212Z - [REQUEST]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.html
[NX v21.0.3 Daemon Server] - 2025-06-26T07:17:56.212Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T07:17:56.240Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:17:56.241Z - Time taken for 'hash changed files from watcher' 2.6307000000961125ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:17:56.243Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:17:56.245Z - Handled GLOB. Handling time: 4. Response time: 5.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:17:56.253Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:17:56.254Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:17:56.254Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:17:56.387Z - Time taken for 'build-project-configs' 162.51970000006258ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:17:56.452Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:17:56.453Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:17:56.453Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:17:56.453Z - Time taken for 'total execution time for createProjectGraph()' 46.91190000018105ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:19:34.939Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:19:34.940Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T07:19:40.223Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:19:41.357Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T07:19:41.357Z - [REQUEST]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts
[NX v21.0.3 Daemon Server] - 2025-06-26T07:19:41.357Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T07:19:41.375Z - Time taken for 'hash changed files from watcher' 1.4272000002674758ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:19:41.382Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:19:41.383Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:19:41.385Z - Handled GLOB. Handling time: 2. Response time: 3.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:19:41.395Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:19:41.396Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:19:41.396Z - Handled HASH_GLOB. Handling time: 5. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:19:41.712Z - Time taken for 'build-project-configs' 335.72910000011325ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:19:41.841Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:19:41.842Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:19:41.843Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:19:41.843Z - Time taken for 'total execution time for createProjectGraph()' 96.66589999990538ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:43.878Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:43.879Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:49.210Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:50.297Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:50.298Z - [REQUEST]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:50.299Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:50.314Z - Time taken for 'hash changed files from watcher' 2.4506000000983477ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:50.322Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:50.324Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:50.324Z - Handled GLOB. Handling time: 5. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:50.330Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:50.330Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:50.331Z - Handled HASH_GLOB. Handling time: 4. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:50.557Z - Time taken for 'build-project-configs' 238.21849999995902ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:50.674Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:50.675Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:50.675Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:50.675Z - Time taken for 'total execution time for createProjectGraph()' 74.6988000003621ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:52.415Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:52.417Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:53.545Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:53.546Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:58.829Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:58.829Z - [REQUEST]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:58.829Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:58.835Z - Time taken for 'hash changed files from watcher' 1.0157000003382564ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:58.839Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:58.840Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:58.840Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:58.844Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:58.845Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:58.845Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:58.972Z - Time taken for 'build-project-configs' 132.46540000010282ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:59.047Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:59.048Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:59.048Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:59.048Z - Time taken for 'total execution time for createProjectGraph()' 58.920299999881536ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:23:59.246Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:09.964Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:09.966Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:12.254Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:12.255Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:15.136Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:15.136Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:16.382Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:16.382Z - [REQUEST]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:16.382Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:16.394Z - Time taken for 'hash changed files from watcher' 1.3114000000059605ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:16.399Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:16.401Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:16.401Z - Handled GLOB. Handling time: 3. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:16.408Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:16.408Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:16.409Z - Handled HASH_GLOB. Handling time: 4. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:16.801Z - Time taken for 'build-project-configs' 405.3963000001386ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:16.872Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:16.873Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:16.873Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:16.874Z - Time taken for 'total execution time for createProjectGraph()' 53.02309999987483ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:17.673Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:26.643Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:26.643Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:30.520Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:30.522Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:32.593Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:33.046Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:33.046Z - [REQUEST]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:33.046Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:33.059Z - Time taken for 'hash changed files from watcher' 1.1094999997876585ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:33.069Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:33.070Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:33.070Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:33.075Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:33.075Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:33.075Z - Handled HASH_GLOB. Handling time: 3. Response time: 0.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:33.222Z - Time taken for 'build-project-configs' 160.60719999996945ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:33.317Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:33.318Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:33.319Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:24:33.319Z - Time taken for 'total execution time for createProjectGraph()' 79.16859999997541ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:05.124Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:05.125Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:10.140Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:10.140Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:10.262Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:11.530Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:11.530Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:11.530Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:11.537Z - Time taken for 'hash changed files from watcher' 0.7257999996654689ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:11.544Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:11.545Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:11.545Z - Handled GLOB. Handling time: 3. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:11.552Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:11.552Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:11.552Z - Handled HASH_GLOB. Handling time: 4. Response time: 0.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:11.768Z - Time taken for 'build-project-configs' 222.52009999984875ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:11.897Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:11.899Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:11.899Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:11.900Z - Time taken for 'total execution time for createProjectGraph()' 94.40950000006706ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:20.177Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:21.963Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:21.966Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:23.987Z - [WATCHER]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:23.988Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:28.371Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:28.371Z - [REQUEST]: packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:28.371Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:28.390Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:28.390Z - Time taken for 'hash changed files from watcher' 1.5175999999046326ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:28.391Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:28.391Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:28.397Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:28.398Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:28.398Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:28.457Z - Time taken for 'build-project-configs' 59.89189999969676ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:28.595Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:28.597Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:28.597Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:28.598Z - Time taken for 'total execution time for createProjectGraph()' 114.9417000003159ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:25:32.697Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:38.461Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:38.471Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:41.955Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:41.956Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:44.653Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:44.891Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:44.891Z - [REQUEST]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:44.891Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:44.899Z - Time taken for 'hash changed files from watcher' 0.6670000003650784ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:44.909Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:44.909Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:44.910Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:44.917Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:44.917Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:44.917Z - Handled HASH_GLOB. Handling time: 5. Response time: 0.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:45.099Z - Time taken for 'build-project-configs' 195.66459999978542ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:45.193Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:45.194Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:45.194Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:45.194Z - Time taken for 'total execution time for createProjectGraph()' 61.448600000701845ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:45.379Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:45.380Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:48.279Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:48.281Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:50.061Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:50.064Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:51.803Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:51.803Z - [REQUEST]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:51.804Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:51.824Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:51.825Z - Time taken for 'hash changed files from watcher' 3.33669999986887ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:51.827Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:51.828Z - Handled GLOB. Handling time: 3. Response time: 4.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:51.848Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:51.849Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:51.849Z - Handled HASH_GLOB. Handling time: 18. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:52.127Z - Time taken for 'build-project-configs' 300.7263000002131ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:52.253Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:52.254Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:52.254Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:52.255Z - Time taken for 'total execution time for createProjectGraph()' 97.3925000000745ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:53.578Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:53.586Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T07:42:58.917Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:43:00.001Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T07:43:00.001Z - [REQUEST]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts
[NX v21.0.3 Daemon Server] - 2025-06-26T07:43:00.001Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T07:43:00.028Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:43:00.033Z - Time taken for 'hash changed files from watcher' 1.330200000666082ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:43:00.034Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:43:00.034Z - Handled GLOB. Handling time: 6. Response time: 6.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:43:00.044Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:43:00.047Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:43:00.047Z - Handled HASH_GLOB. Handling time: 7. Response time: 3.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:43:00.433Z - Time taken for 'build-project-configs' 403.3979000002146ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:43:00.622Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:43:00.623Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:43:00.624Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:43:00.624Z - Time taken for 'total execution time for createProjectGraph()' 132.48629999998957ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:46:36.927Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T07:46:36.927Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:46:43.330Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T07:46:43.330Z - [REQUEST]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.html
[NX v21.0.3 Daemon Server] - 2025-06-26T07:46:43.330Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T07:46:43.344Z - Time taken for 'hash changed files from watcher' 1.6100000003352761ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:46:43.350Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:46:43.351Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:46:43.351Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:46:43.356Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:46:43.357Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:46:43.357Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:46:43.456Z - Time taken for 'build-project-configs' 114.26600000075996ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:46:43.506Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:46:43.507Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:46:43.507Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:46:43.507Z - Time taken for 'total execution time for createProjectGraph()' 38.17210000008345ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:12.251Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:12.254Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.html was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:18.668Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:18.668Z - [REQUEST]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.html
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:18.668Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:18.676Z - Time taken for 'hash changed files from watcher' 0.6326999999582767ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:18.680Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:18.682Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:18.683Z - Handled GLOB. Handling time: 2. Response time: 3.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:18.686Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:18.687Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:18.687Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:18.812Z - Time taken for 'build-project-configs' 133.45100000035018ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:18.880Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:18.881Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:18.881Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:18.881Z - Time taken for 'total execution time for createProjectGraph()' 55.20920000039041ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:30.777Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:30.783Z - [WATCHER]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts was modified
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:37.195Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:37.195Z - [REQUEST]: packages/web/src/app/pages/buyer-seller/seller-sales-listing/my-sales-listing.component.ts
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:37.195Z - [REQUEST]: 
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:37.212Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:37.213Z - Time taken for 'hash changed files from watcher' 0.8728000000119209ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:37.214Z - Done responding to the client handleGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:37.214Z - Handled GLOB. Handling time: 2. Response time: 2.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:37.220Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:37.220Z - Done responding to the client handleHashMultiGlob
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:37.221Z - Handled HASH_GLOB. Handling time: 4. Response time: 1.
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:37.406Z - Time taken for 'build-project-configs' 199.807699999772ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:37.497Z - [SYNC]: collect registered sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:37.499Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:37.499Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:37.499Z - Time taken for 'total execution time for createProjectGraph()' 71.56110000051558ms
[NX v21.0.3 Daemon Server] - 2025-06-26T07:47:37.734Z - [WATCHER]: Processing file changes in outputs
[NX v21.0.3 Daemon Server] - 2025-06-26T07:51:54.786Z - Closed a connection. Number of open connections: 2
[NX v21.0.3 Daemon Server] - 2025-06-26T07:51:54.827Z - Closed a connection. Number of open connections: 1
[NX v21.0.3 Daemon Server] - 2025-06-26T10:47:37.340Z - Closed a connection. Number of open connections: 0
[NX v21.0.3 Daemon Server] - 2025-06-26T10:47:37.349Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Desktop\New folder\propped (sources)
[NX v21.0.3 Daemon Server] - 2025-06-26T10:47:37.350Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Desktop\New folder\propped (outputs)
[NX v21.0.3 Daemon Server] - 2025-06-26T10:47:37.393Z - Server stopped because: "10800000ms of inactivity"
