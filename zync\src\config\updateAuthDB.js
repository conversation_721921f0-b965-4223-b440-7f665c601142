const pool = require('./connection');

/**
 * Update database schema to support the new authentication flow
 * This script will modify existing tables and add new ones as needed
 */
const updateAuthDatabase = async () => {
  let connection;
  try {
    connection = await pool.getConnection();
    console.log('🔄 Starting authentication database update...');

    // 1. Update users table to match the new authentication flow requirements
    console.log('📝 Updating users table structure...');
    
    // Add new columns if they don't exist
    const alterUserQueries = [
      // Add username column if it doesn't exist
      `ALTER TABLE users ADD COLUMN IF NOT EXISTS username VARCHAR(100) UNIQUE`,
      
      // Add meta column for 2FA settings (JSON format)
      `ALTER TABLE users ADD COLUMN IF NOT EXISTS meta JSON DEFAULT ('{"TFARequire": false}')`,
      
      // Add secret column for TOTP secrets (JSON format)
      `ALTER TABLE users ADD COLUMN IF NOT EXISTS secret JSON DEFAULT ('{}')`,
      
      // Rename columns to match new schema
      `ALTER TABLE users CHANGE COLUMN is_mail_verified is_email_verified TINYINT(1) DEFAULT 0`,
      
      // Update OTP column names to match new schema
      `ALTER TABLE users CHANGE COLUMN otp otp_code VARCHAR(10)`,
      
      // Add role column with default USER role
      `ALTER TABLE users ADD COLUMN IF NOT EXISTS role VARCHAR(50) DEFAULT 'USER'`,
      
      // Add isActive column
      `ALTER TABLE users ADD COLUMN IF NOT EXISTS isActive TINYINT(1) DEFAULT 1`
    ];

    for (const query of alterUserQueries) {
      try {
        await connection.execute(query);
        console.log(`✅ Executed: ${query.substring(0, 50)}...`);
      } catch (error) {
        if (!error.message.includes('Duplicate column name') && !error.message.includes('already exists')) {
          console.log(`⚠️  Query failed (might be expected): ${error.message}`);
        }
      }
    }

    // 2. Create userTokenTable for OTP management
    console.log('📝 Creating userTokenTable...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS userTokenTable (
        email VARCHAR(255) NOT NULL PRIMARY KEY,
        token VARCHAR(6) NOT NULL,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_email (email),
        INDEX idx_token (token)
      )
    `);
    console.log('✅ UserTokenTable created/verified.');

    // 3. Update existing tokens table to match new requirements
    console.log('📝 Updating tokens table...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS tokens (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        token TEXT NOT NULL,
        device_type VARCHAR(20) DEFAULT 'web',
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_user_id (user_id),
        INDEX idx_token_hash (token(255)),
        INDEX idx_expires_at (expires_at)
      )
    `);
    console.log('✅ Tokens table updated.');

    // 4. Create roles table if it doesn't exist
    console.log('📝 Creating roles table...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS roles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(50) UNIQUE NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // Insert default roles
    await connection.execute(`
      INSERT IGNORE INTO roles (name, description) VALUES 
      ('USER', 'Default user role'),
      ('ADMIN', 'Administrator role'),
      ('MODERATOR', 'Moderator role')
    `);
    console.log('✅ Roles table created with default roles.');

    // 5. Create user_roles junction table
    console.log('📝 Creating user_roles table...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS user_roles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        role_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user_role (user_id, role_id)
      )
    `);
    console.log('✅ User_roles table created.');

    // 6. Update existing data to match new schema
    console.log('📝 Updating existing user data...');
    
    // Set default meta and secret for existing users
    await connection.execute(`
      UPDATE users 
      SET meta = JSON_OBJECT('TFARequire', false),
          secret = JSON_OBJECT()
      WHERE meta IS NULL OR secret IS NULL
    `);

    // Assign default USER role to existing users
    await connection.execute(`
      INSERT IGNORE INTO user_roles (user_id, role_id)
      SELECT u.id, r.id 
      FROM users u 
      CROSS JOIN roles r 
      WHERE r.name = 'USER' 
      AND u.id NOT IN (SELECT user_id FROM user_roles)
    `);

    console.log('✅ Existing data updated.');

    console.log('🎉 Authentication database update completed successfully!');
    
  } catch (error) {
    console.error('❌ Authentication database update failed:', error.message);
    throw error;
  } finally {
    if (connection) {
      connection.release();
    }
  }
};

module.exports = updateAuthDatabase;
