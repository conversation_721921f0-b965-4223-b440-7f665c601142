import { ScrollingModule } from '@angular/cdk/scrolling';
import type { OnInit } from '@angular/core';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Injectable,
  inject,
} from '@angular/core';
import type { FormGroup } from '@angular/forms';
import { FormArray, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { lastValueFrom } from 'rxjs';
import { EnsureSingleInstance } from '../../common/test-root-service';
import { FormModalService } from './form-modal.service';
import type { SilverField } from './silver-field.component';
import {
  SilverFieldComponent,
  SilverFieldService,
  SilverFieldTypes,
} from './silver-field.component';

export interface ModalDataI {
  heading: string;
  btn1Name?: string;
  btn2Name?: string;
}

export interface MaterialResult_I {
  action: boolean;
  value: any[];
}

@Injectable({ providedIn: 'root' })
export class MatrixModalService {
  readonly #dialog = inject(MatDialog);

  constructor() {
    EnsureSingleInstance(this);
  }

  public async open(data: ModalDataI, options = {}) {
    const dialogRef = this.#dialog.open(MatrixModalComponent, {
      width: '400px',
      data: { data },
      ...options,
    });
    return (await lastValueFrom(dialogRef.afterClosed())) as MaterialResult_I;
  }
}

@Component({
  selector: 'silver-matrix-modal',
  imports: [
    FormsModule,
    MatButtonModule,
    MatDialogModule,
    MatFormFieldModule,
    MatIconModule,
    ReactiveFormsModule,
    SilverFieldComponent,
    ScrollingModule,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `<div mat-dialog-title style="font-size: 1.2rem" class="!px-4 !pb-3">
      {{ modalData.heading }}
    </div>
    <mat-dialog-content class="mat-typography !p-0 !text-inherit">
      <button class="btn btn-primary" (click)="add()">Add</button>
      <form class="flex flex-col grow" [formGroup]="formArray">
        <div class="overflow-x-auto">
          <table class="table table-zebra whitespace-nowrap table-pin-rows table-pin-cols">
            <thead>
              <tr>
                @for (col of columns; track col) {
                  <th>{{ col }}</th>
                }
                <th>edit</th>
              </tr>
            </thead>
            <tbody>
              @for (row of formRows; track row; let i = $index) {
                <tr>
                  @for (item of row; track item; let j = $index) {
                    @if (inline) {
                      <td>
                        <app-silver-field
                          class="flex flex-col"
                          [class]="item.silverClass || ''"
                          [field]="item"
                          [ctrlName]="item.key"
                          [form]="formGroups[i]"
                        />
                      </td>
                    } @else {
                      <td (click)="edit(row, formGroups[i])">
                        {{ formGroups[i].controls[item.key].value }}
                      </td>
                    }
                  }
                  <th>
                    <mat-icon
                      matTooltip="Cancel"
                      class="cursor-pointer text-error !overflow-visible"
                      (click)="remove(i)"
                      >close</mat-icon
                    >
                  </th>
                </tr>
              }
            </tbody>
          </table>
        </div>
      </form>
    </mat-dialog-content>
    <mat-dialog-actions align="end">
      <button mat-button [mat-dialog-close]="false" color="warn">
        {{ btn1Name }}
      </button>
      <button mat-button color="accent" (click)="submit()">
        {{ btn2Name }}
      </button>
    </mat-dialog-actions>`,
})
export class MatrixModalComponent implements OnInit {
  // readonly #dialogRef: MatDialogRef<MatrixModalComponent> = inject(MatDialogRef);
  readonly #cdRef = inject(ChangeDetectorRef);
  readonly #silverFieldService = inject(SilverFieldService);
  readonly #formModalService = inject(FormModalService);
  readonly #data: { data: ModalDataI; ref: any } = inject(MAT_DIALOG_DATA);
  public formGroup!: FormGroup<any>;
  public modalData!: ModalDataI;
  public btn1Name!: string;
  public btn2Name!: string;
  public value!: any[];

  public columns = ['name', 'desc', 'type'];
  public formRows: SilverField[][] = [];
  public formGroups: FormGroup<any>[] = [];
  public formArray = new FormArray<any>([]);

  public inline = false;

  public ngOnInit(): void {
    this.modalData = this.#data.data;
    this.btn1Name = this.modalData.btn1Name ?? 'Cancel';
    this.btn2Name = this.modalData.btn2Name ?? 'Confirm';
  }

  public submit() {
    console.log(this.formArray.value);
    // const res = { action: true, value: '' };
    // this.#dialogRef.close(res);
  }

  public add() {
    const columns = this.columns;
    const formFields = [];
    for (const key of columns) {
      formFields.push({
        controlType: SilverFieldTypes.TEXT,
        label: key,
        placeholder: key,
        type: 'text',
        key: key,
        value: '',
        valid: { required: true },
      });
    }
    this.formRows.push(formFields);
    const formGroup = this.#silverFieldService.fieldsToForm(formFields);
    this.formGroups.push(formGroup);
    this.formArray.push(formGroup);
  }

  async edit(form: SilverField[], formGroup: FormGroup<any>) {
    for (const field of form) field.value = formGroup.controls[field.key].value;
    const result = await this.#formModalService.open({ heading: 'Edit', form });
    if (!result?.action) return;
    formGroup.patchValue(result.value);
    this.#cdRef.detectChanges();
  }

  remove(ix: number) {
    this.formArray.removeAt(ix);
    this.formGroups.splice(ix, 1);
    this.formRows.splice(ix, 1);
    this.#cdRef.detectChanges();
  }

  identity(_: number, item: any) {
    return item;
  }
}
