@use '@angular/material' as mat;

$dark-theme: mat.define-theme(
  (
    color: (
      theme-type: dark,
      primary: mat.$rose-palette,
      tertiary: mat.$orange-palette,
    ),
    typography: (
      brand-family: 'Inter',
    ),
  )
);

$light-theme: mat.define-theme(
  (
    color: (
      theme-type: light,
      primary: mat.$chartreuse-palette,
      tertiary: mat.$orange-palette,
    ),
    typography: (
      brand-family: 'DM Sans',
    ),
  )
);

html {
  @include mat.all-component-themes($dark-theme);
  &[data-theme='light'] {
    @include mat.all-component-colors($light-theme);
  }
}

.mat-mdc-snack-bar-container {
  --mdc-snackbar-container-color: auto !important;
  --mdc-snackbar-supporting-text-color: auto !important;
  --mat-snack-bar-button-color: auto !important;
}

.mat-mdc-list-base {
  --mdc-list-list-item-label-text-color: auto !important;
  --mdc-list-list-item-hover-label-text-color: auto !important;
}

:root {
  --mat-sidenav-content-background-color: inherit !important;
  --mat-sidenav-container-background-color: oklch(var(--b1)) !important;
  --mdc-filled-text-field-container-color: inherit !important;
  --mat-menu-container-color: oklch(var(--b1)) !important;
  --mat-select-panel-background-color: oklch(var(--b1)) !important;
  --mat-select-panel-2background-color: oklch(var(--b1)) !important;
  --mat-datepicker-calendar-container-background-color: oklch(var(--b1)) !important;
  --mat-bottom-sheet-container-background-color: oklch(var(--b1)) !important;
  --mat-dialog-container-max-width: 80vw !important;
  --animate-duration: 0.4s;
  --mdc-dialog-container-shape: var(--rounded-box, 0.5rem) !important;
  --mdc-dialog-supporting-text-font: inherit !important;
}

:not(:root):fullscreen::backdrop {
  background: oklch(var(--b1)) !important;
}

.mat-mdc-tooltip {
  --mdc-plain-tooltip-container-color: oklch(var(--b1)) !important;
  --mdc-plain-tooltip-supporting-text-color: auto !important;
}

.mat-mdc-dialog-container {
  --mdc-dialog-container-color: oklch(var(--b1)) !important;
}

.mat-mdc-form-field-error {
  font-size: 12px;
}

.mat-mdc-dialog-container .mdc-dialog__content {
  --mdc-dialog-supporting-text-color: auto !important;
}

.mat-mdc-select {
  display: inline-block;
  width: 100%;
  outline: none;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  color: var(--mat-select-enabled-trigger-text-color);
  font-family: var(--mat-select-trigger-text-font);
  line-height: var(--mat-select-trigger-text-line-height);
  font-size: var(--mat-select-trigger-text-size);
  font-weight: var(--mat-select-trigger-text-weight);
  letter-spacing: var(--mat-select-trigger-text-tracking);
}

.mat-drawer-container {
  color: unset !important;
}

.mat-drawer {
  color: unset !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.mat-form-field-disabled {
  opacity: 0.7 !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.mat-form-field-invalid
  .mat-mdc-text-field-wrapper {
  --tw-border-opacity: 1 !important;
  border-color: oklch(var(--er) / var(--tw-border-opacity, 1)) !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.mat-form-field-invalid
  .mat-mdc-text-field-wrapper:where(.dark, .dark *) {
  --tw-border-opacity: 1 !important;
  border-color: oklch(var(--er) / var(--tw-border-opacity, 1)) !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.mat-form-field-invalid
  .mat-mdc-select
  .mat-mdc-select-placeholder {
  --tw-text-opacity: 1 !important;
  color: oklch(var(--er) / var(--tw-text-opacity, 1)) !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill:hover .mat-mdc-form-field-focus-overlay,
.mat-mdc-form-field.mat-form-field-appearance-fill.mat-focused .mat-mdc-form-field-focus-overlay {
  opacity: 0 !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.mat-focused:not(.mat-form-field-invalid)
  .mat-mdc-text-field-wrapper {
  --tw-border-opacity: 1 !important;
  border-color: oklch(var(--p) / var(--tw-border-opacity, 1)) !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.mat-focused:not(.mat-form-field-invalid)
  .mat-mdc-text-field-wrapper:where(.dark, .dark *) {
  --tw-border-opacity: 1 !important;
  border-color: oklch(var(--p) / var(--tw-border-opacity, 1)) !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.mat-mdc-form-field-type-mat-native-select
  .mat-mdc-form-field-infix
  select {
  top: auto;
  margin-top: 0;
  margin-bottom: 0;
  padding-top: 0;
  padding-right: 18px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%2364748B' viewBox='0 0 24 24'%3E%3Cpath d='M7 10l5 5 5-5H7z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right -7px center;
  background-size: 24px;
}

.dark
  .mat-mdc-form-field.mat-form-field-appearance-fill.mat-mdc-form-field-type-mat-native-select
  .mat-mdc-form-field-infix
  select {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%2397a6ba' viewBox='0 0 24 24'%3E%3Cpath d='M7 10l5 5 5-5H7z'/%3E%3C/svg%3E");
}

.mat-mdc-form-field.mat-form-field-appearance-fill.mat-mdc-form-field-type-mat-native-select
  .mat-mdc-form-field-infix:after {
  display: none;
}

.mat-mdc-form-field.mat-form-field-appearance-fill .mat-mdc-text-field-wrapper {
  padding: 0;
  border-radius: 0.5rem;
  border-width: 1px;
  border-style: solid;
  --tw-border-opacity: 0.2 !important;
  border-color: oklch(var(--bc) / var(--tw-border-opacity, 0.2)) !important;
  // border-color: rgb(203 213 225 / var(--tw-border-opacity, 1)) !important;

  --tw-bg-opacity: 1 !important;
  // background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1)) !important;

  background-color: oklch(var(--b2) / var(--tw-bg-opacity, 1)) !important;

  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05) !important;
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color) !important;
  box-shadow:
    var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper:where(.dark, .dark *) {
  --tw-border-opacity: 1 !important;
  border-color: rgb(100 116 139 / var(--tw-border-opacity, 1)) !important;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1)) !important;
  --tw-bg-opacity: 0.05 !important
;
}

.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper:not(.mdc-text-field--no-label) {
  margin-top: 24px;
  overflow: visible;
}

.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-focus-overlay {
  border-radius: 6px;
}

.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex {
  position: relative;
  display: flex;
  align-items: stretch;
  border-radius: 6px;
  padding: 0 16px;
}

.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix {
  padding: 0 !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  > .mat-icon,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  > .mat-icon {
  margin-right: 12px;
  padding: 0 !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  > .mat-mdc-icon-button,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  > .mat-mdc-icon-button {
  margin: 0 4px 0 -10px;
}

.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  > .mat-mdc-select,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  > .mat-mdc-select {
  margin-right: 10px;
}

.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  > .mat-datepicker-toggle,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  > .mat-datepicker-toggle {
  margin-left: -8px;
}

.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix {
  padding: 0 !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  > .mat-icon,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  > .mat-icon {
  margin-left: 12px;
  padding: 0 !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  > .mat-mdc-icon-button,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  > .mat-mdc-icon-button {
  margin: 0 -10px 0 4px;
}

.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  > .mat-mdc-select,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  > .mat-mdc-select {
  margin-left: 10px;
}

.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  > .mat-datepicker-toggle,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  > .mat-datepicker-toggle {
  margin-right: -8px;
}

.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  --tw-text-opacity: 1 !important;
  color: rgba(var(--fuse-text-hint-rgb), var(--tw-text-opacity)) !important;
}

.mat-toolbar.mat-primary
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix,
.mat-toolbar.mat-primary
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix,
.mat-toolbar.mat-primary
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix,
.mat-toolbar.mat-primary
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix {
  color: oklch(var(--pc) / var(--tw-text-opacity, 1)) !important;
  --tw-text-opacity: 0.38 !important
;
}

.mat-toolbar.mat-accent
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix,
.mat-toolbar.mat-accent
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix,
.mat-toolbar.mat-accent
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix,
.mat-toolbar.mat-accent
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix {
  color: oklch(var(--ac) / var(--tw-text-opacity, 1)) !important;
  --tw-text-opacity: 0.38 !important
;
}

.mat-toolbar.mat-warn
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix,
.mat-toolbar.mat-warn
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix,
.mat-toolbar.mat-warn
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix,
.mat-toolbar.mat-warn
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix {
  color: oklch(var(--wac) / var(--tw-text-opacity, 1)) !important;
  --tw-text-opacity: 0.38 !important
;
}

.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  .mat-mdc-icon-button,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  .mat-mdc-icon-button,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  .mat-mdc-icon-button,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  .mat-mdc-icon-button {
  width: 40px;
  min-width: 40px;
  height: 40px;
  min-height: 40px;
}

.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  .mat-icon,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  .mat-mdc-icon-button:not([disabled]),
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  .mat-mdc-select-value,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  .mat-icon,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  .mat-mdc-icon-button:not([disabled]),
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  .mat-mdc-select-value,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  .mat-icon,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  .mat-mdc-icon-button:not([disabled]),
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  .mat-mdc-select-value,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  .mat-icon,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  .mat-mdc-icon-button:not([disabled]),
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  .mat-mdc-select-value {
  --tw-text-opacity: 1;
  color: rgba(var(--fuse-text-hint-rgb), var(--tw-text-opacity));
}

.mat-toolbar.mat-primary
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  .mat-icon,
.mat-toolbar.mat-primary
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  .mat-mdc-icon-button:not([disabled]),
.mat-toolbar.mat-primary
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  .mat-mdc-select-value,
.mat-toolbar.mat-primary
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  .mat-icon,
.mat-toolbar.mat-primary
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  .mat-mdc-icon-button:not([disabled]),
.mat-toolbar.mat-primary
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  .mat-mdc-select-value,
.mat-toolbar.mat-primary
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  .mat-icon,
.mat-toolbar.mat-primary
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  .mat-mdc-icon-button:not([disabled]),
.mat-toolbar.mat-primary
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  .mat-mdc-select-value,
.mat-toolbar.mat-primary
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  .mat-icon,
.mat-toolbar.mat-primary
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  .mat-mdc-icon-button:not([disabled]),
.mat-toolbar.mat-primary
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  .mat-mdc-select-value {
  color: oklch(var(--pc) / var(--tw-text-opacity, 1)) !important;
  --tw-text-opacity: 0.38 !important
;
}

.mat-toolbar.mat-accent
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  .mat-icon,
.mat-toolbar.mat-accent
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  .mat-mdc-icon-button:not([disabled]),
.mat-toolbar.mat-accent
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  .mat-mdc-select-value,
.mat-toolbar.mat-accent
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  .mat-icon,
.mat-toolbar.mat-accent
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  .mat-mdc-icon-button:not([disabled]),
.mat-toolbar.mat-accent
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  .mat-mdc-select-value,
.mat-toolbar.mat-accent
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  .mat-icon,
.mat-toolbar.mat-accent
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  .mat-mdc-icon-button:not([disabled]),
.mat-toolbar.mat-accent
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  .mat-mdc-select-value,
.mat-toolbar.mat-accent
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  .mat-icon,
.mat-toolbar.mat-accent
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  .mat-mdc-icon-button:not([disabled]),
.mat-toolbar.mat-accent
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  .mat-mdc-select-value {
  color: oklch(var(--ac) / var(--tw-text-opacity, 1)) !important;
  --tw-text-opacity: 0.38 !important
;
}

.mat-toolbar.mat-warn
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  .mat-icon,
.mat-toolbar.mat-warn
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  .mat-mdc-icon-button:not([disabled]),
.mat-toolbar.mat-warn
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  .mat-mdc-select-value,
.mat-toolbar.mat-warn
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  .mat-icon,
.mat-toolbar.mat-warn
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  .mat-mdc-icon-button:not([disabled]),
.mat-toolbar.mat-warn
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  .mat-mdc-select-value,
.mat-toolbar.mat-warn
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  .mat-icon,
.mat-toolbar.mat-warn
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  .mat-mdc-icon-button:not([disabled]),
.mat-toolbar.mat-warn
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  .mat-mdc-select-value,
.mat-toolbar.mat-warn
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  .mat-icon,
.mat-toolbar.mat-warn
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  .mat-mdc-icon-button:not([disabled]),
.mat-toolbar.mat-warn
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  .mat-mdc-select-value {
  color: oklch(var(--wac) / var(--tw-text-opacity, 1)) !important;
  --tw-text-opacity: 0.38 !important
;
}

.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  .mat-datepicker-toggle-default-icon,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  .mat-datepicker-toggle-default-icon,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  .mat-datepicker-toggle-default-icon,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  .mat-datepicker-toggle-default-icon {
  width: 1.5rem;
  height: 1.5rem;
  min-width: 1.5rem;
  min-height: 1.5rem;
  font-size: 1.5rem;
  line-height: 1.5rem;
}

.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  .mat-datepicker-toggle-default-icon
  svg,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  .mat-datepicker-toggle-default-icon
  svg,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  .mat-datepicker-toggle-default-icon
  svg,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  .mat-datepicker-toggle-default-icon
  svg {
  width: 1.5rem;
  height: 1.5rem;
}

.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  .mat-mdc-select,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  .mat-mdc-select,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  .mat-mdc-select,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  .mat-mdc-select {
  display: flex;
  align-items: center;
}

.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  .mat-mdc-select:focus
  .mat-mdc-select-trigger
  .mat-mdc-select-value,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  .mat-mdc-select:focus
  .mat-mdc-select-trigger
  .mat-mdc-select-value,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  .mat-mdc-select:focus
  .mat-mdc-select-trigger
  .mat-mdc-select-value,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  .mat-mdc-select:focus
  .mat-mdc-select-trigger
  .mat-mdc-select-value {
  --tw-text-opacity: 1 !important;
  color: oklch(var(--p) / var(--tw-text-opacity, 1)) !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  .mat-mdc-select:focus
  .mat-mdc-select-trigger
  .mat-mdc-select-arrow-wrapper
  .mat-mdc-select-arrow,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  .mat-mdc-select:focus
  .mat-mdc-select-trigger
  .mat-mdc-select-arrow-wrapper
  .mat-mdc-select-arrow,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  .mat-mdc-select:focus
  .mat-mdc-select-trigger
  .mat-mdc-select-arrow-wrapper
  .mat-mdc-select-arrow,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  .mat-mdc-select:focus
  .mat-mdc-select-trigger
  .mat-mdc-select-arrow-wrapper
  .mat-mdc-select-arrow {
  border-top-color: oklch(var(--p)) !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  .mat-mdc-select
  .mat-mdc-select-trigger,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  .mat-mdc-select
  .mat-mdc-select-trigger,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  .mat-mdc-select
  .mat-mdc-select-trigger,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  .mat-mdc-select
  .mat-mdc-select-trigger {
  display: flex;
  align-items: center;
}

.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  .mat-mdc-select
  .mat-mdc-select-trigger
  .mat-mdc-select-value,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  .mat-mdc-select
  .mat-mdc-select-trigger
  .mat-mdc-select-value,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  .mat-mdc-select
  .mat-mdc-select-trigger
  .mat-mdc-select-value,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  .mat-mdc-select
  .mat-mdc-select-trigger
  .mat-mdc-select-value {
  display: flex;
  max-width: none;
}

.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  .mat-mdc-select
  .mat-mdc-select-trigger
  .mat-mdc-select-value
  mat-mdc-select-trigger
  .mat-icon,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  .mat-mdc-select
  .mat-mdc-select-trigger
  .mat-mdc-select-value
  mat-mdc-select-trigger
  .mat-icon,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  .mat-mdc-select
  .mat-mdc-select-trigger
  .mat-mdc-select-value
  mat-mdc-select-trigger
  .mat-icon,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  .mat-mdc-select
  .mat-mdc-select-trigger
  .mat-mdc-select-value
  mat-mdc-select-trigger
  .mat-icon {
  margin: 0 !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  .mat-mdc-select
  .mat-mdc-select-trigger
  .mat-mdc-select-arrow-wrapper,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  .mat-mdc-select
  .mat-mdc-select-trigger
  .mat-mdc-select-arrow-wrapper,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  .mat-mdc-select
  .mat-mdc-select-trigger
  .mat-mdc-select-arrow-wrapper,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  .mat-mdc-select
  .mat-mdc-select-trigger
  .mat-mdc-select-arrow-wrapper {
  display: flex;
  align-items: center;
  transform: none;
  margin-left: 4px;
}

.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  .mat-mdc-select
  .mat-mdc-select-trigger
  .mat-mdc-select-arrow-wrapper
  .mat-mdc-select-arrow,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  .mat-mdc-select
  .mat-mdc-select-trigger
  .mat-mdc-select-arrow-wrapper
  .mat-mdc-select-arrow,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  .mat-mdc-select
  .mat-mdc-select-trigger
  .mat-mdc-select-arrow-wrapper
  .mat-mdc-select-arrow,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  .mat-mdc-select
  .mat-mdc-select-trigger
  .mat-mdc-select-arrow-wrapper
  .mat-mdc-select-arrow {
  min-height: 0;
  --tw-text-opacity: 1 !important;
  color: rgb(100 116 139 / var(--tw-text-opacity, 1)) !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  .mat-mdc-select
  .mat-mdc-select-trigger
  .mat-mdc-select-arrow-wrapper
  .mat-mdc-select-arrow:where(.dark, .dark *),
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  .mat-mdc-select
  .mat-mdc-select-trigger
  .mat-mdc-select-arrow-wrapper
  .mat-mdc-select-arrow:where(.dark, .dark *),
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  .mat-mdc-select
  .mat-mdc-select-trigger
  .mat-mdc-select-arrow-wrapper
  .mat-mdc-select-arrow:where(.dark, .dark *),
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  .mat-mdc-select
  .mat-mdc-select-trigger
  .mat-mdc-select-arrow-wrapper
  .mat-mdc-select-arrow:where(.dark, .dark *) {
  --tw-text-opacity: 1 !important;
  color: rgb(148 163 184 / var(--tw-text-opacity, 1)) !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-infix {
  position: static;
  display: flex;
  align-items: center;
  width: 88px;
  min-height: 42px;
  padding: 0;
  border: 0;
}

.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-infix
  .mat-mdc-floating-label {
  top: -25px !important;
  left: 0 !important;
  width: 100% !important;
  transform: none !important;
  pointer-events: auto;
  font-weight: 500;
  --tw-text-opacity: 1 !important;
  color: rgba(var(--fuse-text-default-rgb), var(--tw-text-opacity)) !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-infix
  textarea.mat-mdc-input-element {
  margin: 12px 0;
  padding: 0 6px 0 0;
}

.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-infix
  .mat-mdc-chip-set {
  width: 100%;
  margin: 0 -8px;
}

.mat-mdc-form-field.mat-form-field-appearance-fill .mat-mdc-text-field-wrapper .mdc-line-ripple {
  display: none;
}

.mat-mdc-form-field.mat-form-field-appearance-fill .mat-mdc-form-field-subscript-wrapper {
  font-size: 12px;
  font-weight: 500;
}

.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-form-field-subscript-wrapper
  .mat-mdc-form-field-hint-wrapper,
.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-form-field-subscript-wrapper
  .mat-mdc-form-field-error-wrapper {
  padding: 0;
}

.mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-form-field-subscript-wrapper
  .mat-mdc-form-field-hint {
  --tw-text-opacity: 1 !important;
  color: rgba(var(--fuse-text-hint-rgb), var(--tw-text-opacity)) !important;
}

.mat-toolbar.mat-primary
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-form-field-subscript-wrapper
  .mat-mdc-form-field-hint {
  color: oklch(var(--pc) / var(--tw-text-opacity, 1)) !important;
  --tw-text-opacity: 0.38 !important
;
}

.mat-toolbar.mat-accent
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-form-field-subscript-wrapper
  .mat-mdc-form-field-hint {
  color: oklch(var(--ac) / var(--tw-text-opacity, 1)) !important;
  --tw-text-opacity: 0.38 !important
;
}

.mat-toolbar.mat-warn
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-form-field-subscript-wrapper
  .mat-mdc-form-field-hint {
  color: oklch(var(--wac) / var(--tw-text-opacity, 1)) !important;
  --tw-text-opacity: 0.38 !important
;
}

.mat-mdc-form-field.mat-form-field-appearance-fill:has(textarea.mat-mdc-input-element)
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix,
.mat-mdc-form-field.mat-form-field-appearance-fill:has(textarea.mat-mdc-input-element)
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix,
.mat-mdc-form-field.mat-form-field-appearance-fill:has(textarea.mat-mdc-input-element)
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix,
.mat-mdc-form-field.mat-form-field-appearance-fill:has(textarea.mat-mdc-input-element)
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix {
  align-self: flex-start;
  padding-top: 14px !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-rounded .mat-mdc-text-field-wrapper {
  border-radius: 24px;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-rounded.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-rounded.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix {
  border-radius: 24px 0 0 24px;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-rounded.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  > .mat-icon,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-rounded.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  > .mat-icon {
  margin-right: 12px;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-rounded.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  > .mat-mdc-icon-button,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-rounded.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  > .mat-mdc-icon-button {
  margin: 0 2px 0 -10px !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-rounded.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  > .mat-mdc-select,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-rounded.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  > .mat-mdc-select {
  margin-right: 8px;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-rounded.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  > .mat-datepicker-toggle,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-rounded.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  > .mat-datepicker-toggle {
  margin-right: 4px;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-rounded.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  > *:not(.mat-icon):not(.mat-mdc-icon-button):not(.mat-mdc-select):not(.mat-datepicker-toggle),
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-rounded.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  > *:not(.mat-icon):not(.mat-mdc-icon-button):not(.mat-mdc-select):not(.mat-datepicker-toggle) {
  margin-right: 12px;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-rounded.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-rounded.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix {
  border-radius: 0 24px 24px 0;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-rounded.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  > .mat-icon,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-rounded.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  > .mat-icon {
  margin-left: 12px !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-rounded.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  > .mat-mdc-icon-button,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-rounded.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  > .mat-mdc-icon-button {
  margin: 0 -10px 0 2px !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-rounded.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  > .mat-mdc-select,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-rounded.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  > .mat-mdc-select {
  margin-left: 12px !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-rounded.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  > .mat-datepicker-toggle,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-rounded.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  > .mat-datepicker-toggle {
  margin-left: 4px !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-rounded.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  > *:not(.mat-icon):not(.mat-mdc-icon-button):not(.mat-mdc-select):not(.mat-datepicker-toggle),
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-rounded.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  > *:not(.mat-icon):not(.mat-mdc-icon-button):not(.mat-mdc-select):not(.mat-datepicker-toggle) {
  margin-left: 12px !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-dense
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  .mat-mdc-icon-button,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-dense
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  .mat-mdc-icon-button,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-dense
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  .mat-mdc-icon-button,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-dense
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  .mat-mdc-icon-button {
  width: 32px !important;
  min-width: 32px;
  height: 32px;
  min-height: 32px;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-dense
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  > .mat-mdc-icon-button,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-dense
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  > .mat-mdc-icon-button {
  margin-left: -6px;
  margin-right: 12px;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-dense
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  > .mat-mdc-icon-button,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-dense
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  > .mat-mdc-icon-button {
  margin-left: 12px;
  margin-right: -6px;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-dense
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-infix {
  min-height: 40px;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-dense
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-infix
  textarea.mat-mdc-input-element {
  margin: 8px 0;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-dense:has(
    textarea.mat-mdc-input-element
  )
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-dense:has(
    textarea.mat-mdc-input-element
  )
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-dense:has(
    textarea.mat-mdc-input-element
  )
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-dense:has(
    textarea.mat-mdc-input-element
  )
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix {
  padding-top: 10px !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-dense.fuse-mat-rounded
  .mat-mdc-text-field-wrapper {
  border-radius: 20px;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-dense.fuse-mat-rounded.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-dense.fuse-mat-rounded.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix {
  border-radius: 20px 0 0 20px !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-dense.fuse-mat-rounded.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-dense.fuse-mat-rounded.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix {
  border-radius: 0 20px 20px 0 !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix {
  align-self: stretch !important;
  margin: 0 16px 0 -16px !important;
  padding-left: 16px !important;
  border-radius: 6px 0 0 6px;
  border-right-width: 1px;
  border-style: solid;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  > .mat-icon,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  > .mat-icon {
  margin-right: 16px;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  > .mat-mdc-icon-button,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  > .mat-mdc-icon-button {
  margin: 0 6px 0 -10px !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  > .mat-mdc-select,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  > .mat-mdc-select {
  margin-right: 12px !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  > .mat-datepicker-toggle,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  > .mat-datepicker-toggle {
  margin-right: 8px;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix
  > *:not(.mat-icon):not(.mat-mdc-icon-button):not(.mat-mdc-select):not(.mat-datepicker-toggle),
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix
  > *:not(.mat-icon):not(.mat-mdc-icon-button):not(.mat-mdc-select):not(.mat-datepicker-toggle) {
  margin-right: 16px;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix {
  align-self: stretch !important;
  margin: 0 -16px 0 16px !important;
  padding-right: 16px !important;
  border-radius: 0 6px 6px 0;
  border-left-width: 1px;
  border-style: solid;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  > .mat-icon,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  > .mat-icon {
  margin-left: 16px;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  > .mat-mdc-icon-button,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  > .mat-mdc-icon-button {
  margin: 0 -10px 0 6px !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  > .mat-mdc-select,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  > .mat-mdc-select {
  margin: 0 -4px 0 16px !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  > .mat-datepicker-toggle,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  > .mat-datepicker-toggle {
  margin-left: 8px;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix
  > *:not(.mat-icon):not(.mat-mdc-icon-button):not(.mat-mdc-select):not(.mat-datepicker-toggle),
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix
  > *:not(.mat-icon):not(.mat-mdc-icon-button):not(.mat-mdc-select):not(.mat-datepicker-toggle) {
  margin-left: 16px;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix {
  --tw-bg-opacity: 1 !important;
  background-color: rgba(var(--fuse-bg-default-rgb), var(--tw-bg-opacity)) !important;
  --tw-border-opacity: 1 !important;
  border-color: rgb(203 213 225 / var(--tw-border-opacity, 1)) !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix:where(.dark, .dark *),
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix:where(.dark, .dark *),
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix:where(.dark, .dark *),
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix:where(.dark, .dark *) {
  --tw-border-opacity: 1 !important;
  border-color: rgb(100 116 139 / var(--tw-border-opacity, 1)) !important;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix:has(
    textarea.mat-mdc-input-element
  )
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-prefix,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix:has(
    textarea.mat-mdc-input-element
  )
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-prefix,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix:has(
    textarea.mat-mdc-input-element
  )
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-icon-suffix,
.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-emphasized-affix:has(
    textarea.mat-mdc-input-element
  )
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mat-mdc-form-field-text-suffix {
  align-items: flex-start;
}

.mat-mdc-form-field.mat-form-field-appearance-fill.fuse-mat-bold .mat-mdc-text-field-wrapper {
  border-width: 2px !important;
}

.mat-mdc-form-field.mat-form-field-appearance-outline.mat-form-field-invalid
  .mdc-notched-outline__leading,
.mat-mdc-form-field.mat-form-field-appearance-outline.mat-form-field-invalid
  .mdc-notched-outline__notch,
.mat-mdc-form-field.mat-form-field-appearance-outline.mat-form-field-invalid
  .mdc-notched-outline__trailing {
  border-color: oklch(var(--w)) !important;
}

.mat-mdc-form-field.mat-form-field-appearance-outline.mat-focused:not(
    .mat-form-field-invalid
  ).mat-primary
  .mdc-notched-outline__leading,
.mat-mdc-form-field.mat-form-field-appearance-outline.mat-focused:not(
    .mat-form-field-invalid
  ).mat-primary
  .mdc-notched-outline__notch,
.mat-mdc-form-field.mat-form-field-appearance-outline.mat-focused:not(
    .mat-form-field-invalid
  ).mat-primary
  .mdc-notched-outline__trailing {
  border-color: oklch(var(--p)) !important;
}

.mat-mdc-form-field.mat-form-field-appearance-outline.mat-focused:not(
    .mat-form-field-invalid
  ).mat-accent
  .mdc-notched-outline__leading,
.mat-mdc-form-field.mat-form-field-appearance-outline.mat-focused:not(
    .mat-form-field-invalid
  ).mat-accent
  .mdc-notched-outline__notch,
.mat-mdc-form-field.mat-form-field-appearance-outline.mat-focused:not(
    .mat-form-field-invalid
  ).mat-accent
  .mdc-notched-outline__trailing {
  border-color: oklch(var(--a)) !important;
}

.mat-mdc-form-field.mat-form-field-appearance-outline:not(.mat-focused):not(.mat-form-field-invalid)
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mdc-notched-outline
  .mdc-notched-outline__leading,
.mat-mdc-form-field.mat-form-field-appearance-outline:not(.mat-focused):not(.mat-form-field-invalid)
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mdc-notched-outline
  .mdc-notched-outline__notch,
.mat-mdc-form-field.mat-form-field-appearance-outline:not(.mat-focused):not(.mat-form-field-invalid)
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mdc-notched-outline
  .mdc-notched-outline__trailing {
  --tw-border-opacity: 1 !important;
  border-color: rgb(203 213 225 / var(--tw-border-opacity, 1)) !important;
}

.mat-mdc-form-field.mat-form-field-appearance-outline:not(.mat-focused):not(.mat-form-field-invalid)
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mdc-notched-outline
  .mdc-notched-outline__leading:where(.dark, .dark *),
.mat-mdc-form-field.mat-form-field-appearance-outline:not(.mat-focused):not(.mat-form-field-invalid)
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mdc-notched-outline
  .mdc-notched-outline__notch:where(.dark, .dark *),
.mat-mdc-form-field.mat-form-field-appearance-outline:not(.mat-focused):not(.mat-form-field-invalid)
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mdc-notched-outline
  .mdc-notched-outline__trailing:where(.dark, .dark *) {
  --tw-border-opacity: 1 !important;
  border-color: rgb(100 116 139 / var(--tw-border-opacity, 1)) !important;
}

.mat-mdc-form-field.mat-form-field-appearance-outline
  .mat-mdc-text-field-wrapper
  .mat-mdc-form-field-flex
  .mdc-notched-outline
  .mdc-notched-outline__notch {
  border-right-style: none !important;
}

.mat-icon {
  display: inline-flex !important;
  align-items: center;
  justify-content: center;
  width: 24px;
  min-width: 24px;
  height: 24px;
  min-height: 24px;
  font-size: 24px;
  line-height: 24px;
  -webkit-appearance: none !important;
}

.mat-mdc-input-element::placeholder {
  transition: none !important;
  --tw-text-opacity: 1 !important;
  color: rgba(var(--fuse-text-hint-rgb), var(--tw-text-opacity)) !important;
}
