<div class="w-full p-4">
  <div class="rounded-lg shadow-lg p-6 border border-blue-100">
    <!-- Main Content -->
    <div class="space-y-6">
      <!-- 1. Sale Conditions -->
      <div class="border border-blue-100 rounded-lg p-6 shadow-sm">
        <h3 class="text-lg font-semibold mb-4">Sale Conditions</h3>

        <div class="space-y-4">
          <!-- Vacant Possession -->
          <div>
            <p class="block text-sm font-medium mb-1">Vacant Possession</p>
            <div class="relative">
              <select
                class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none pr-8"
                [(ngModel)]="vacantPossession"
              >
                <option value="Yes">Yes</option>
                <option value="No">No</option>
                <option value="Negotiable">Negotiable</option>
              </select>
              <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                <svg
                  class="w-4 h-4 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </div>
            </div>
          </div>

          <!-- Extension of Stay -->
          <div>
            <p class="block text-sm font-medium mb-1">Extension of Stay (No. of Months)</p>
            <div class="relative">
              <select
                class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none pr-8"
                [(ngModel)]="extensionOfStay"
              >
                @for (month of monthOptions; track month) {
                  <option [value]="month">{{ month }}</option>
                }
              </select>
              <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                <svg
                  class="w-4 h-4 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </div>
            </div>
          </div>

          <!-- Other Conditions -->
          <div>
            <p class="block text-sm font-medium mb-1">Other Conditions (Please Specify)</p>
            <button
              class="w-full p-2 border border-gray-300 rounded text-left text-gray-500 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
              (click)="openOtherConditionsInput()"
            >
              {{ otherConditions ? otherConditions : 'Click to Enter' }}
            </button>
          </div>
        </div>
      </div>

      <!-- Upload Intent to Sell Section -->
      <div class="border border-blue-100 rounded-lg p-6 shadow-sm">
        <h3 class="text-lg font-semibold mb-4">Upload Intent to Sell</h3>

        <div class="space-y-4">
          <div class="flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4">
            <!-- Visit HDB Website Button -->
            <a
              href="https://www.hdb.gov.sg/residential/selling-a-flat/plan-source-and-contract/intent-to-sell"
              target="_blank"
              class="flex-1"
            >
              <button
                type="button"
                class="w-full py-3 px-4 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors text-sm font-medium"
              >
                Visit HDB Website to<br />Register Intent to Sell
              </button>
            </a>

            <!-- Upload Screenshot Button -->
            <button
              type="button"
              class="flex-1 py-3 px-4 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors text-sm font-medium"
            >
              Upload Screenshot of<br />Intent to Sell
            </button>
          </div>

          <!-- Info Text -->
          <p class="text-xs text-blue-500 italic">
            All listings require a HDB registered Intent to Sell to be listed
          </p>
        </div>
      </div>

      <!-- 2. Terms & Conditions -->
      <div class="border border-blue-100 rounded-lg p-6 shadow-sm">
        <h3 class="text-lg font-semibold mb-4">Terms & Conditions</h3>

        <div class="border border-gray-200 rounded-lg p-4 max-h-64 overflow-y-auto mb-4">
          <ol class="list-decimal pl-5 space-y-2">
            <li>Listing is free and with no expiry.</li>
            <li>You may modify your listing anytime.</li>
            <li>All sale listings will have its listing date reflected.</li>
            <li>
              Relisting of sale listings will only be allowed after a cooling-off period of 30 days
              to prevent manipulation of listings to appear fresh.
            </li>
            <li>
              If your listing is found to violate our terms of use, it will be taken down by the
              administrators and you will be notified.
            </li>
            <li>
              You will be responsible for content contained within the system. Please cross-check
              field within your listing and the platform takes no responsibility.
            </li>
            <li>If your unit has been sold via other means, please remove the listing.</li>
            <li>
              Before listing, sellers need to perform a simple verification process. A pre-selected
              word will need to be written on a piece of paper and photo taken within one of the
              rooms. Upload the photo for verification.
            </li>
          </ol>
        </div>

        <!-- Agreement Checkbox -->
        <div class="flex items-center mb-4">
          <input
            type="checkbox"
            id="agreeTerms"
            class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
            [(ngModel)]="agreeToTerms"
          />
          <label for="agreeTerms" class="ml-2 text-sm font-medium text-gray-700">
            I agree to the Terms & Conditions
          </label>
        </div>
      </div>
    </div>

    <!-- Navigation Buttons -->
    <div class="flex justify-between mt-8">
      <button
        routerLink="/buyer-seller/step4"
        class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
      >
        Back
      </button>

      <button
        [disabled]="!agreeToTerms"
        [class.opacity-50]="!agreeToTerms"
        class="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
        routerLink="/buyer-seller/preview"
      >
        Preview Listing
      </button>
    </div>

    <!-- Other Conditions Modal -->
    @if (showOtherConditionsModal) {
      <div class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
        <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
          <h3 class="text-lg font-semibold text-blue-900 mb-4">Other Conditions</h3>
          <textarea
            class="w-full p-3 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 min-h-[100px]"
            placeholder="Enter any other conditions here..."
            [(ngModel)]="otherConditions"
          ></textarea>
          <div class="flex justify-end mt-4">
            <button
              class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors mr-2"
              (click)="cancelOtherConditions()"
            >
              Cancel
            </button>
            <button
              class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
              (click)="saveOtherConditions()"
            >
              Save
            </button>
          </div>
        </div>
      </div>
    }
  </div>
</div>
