import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { Router, RouterModule } from '@angular/router';

@Component({
  selector: 'app-buyer-seller',
  standalone: true,
  imports: [CommonModule, RouterModule, MatIconModule, MatSnackBarModule],
  template: `
    <div class="container mx-auto p-6">
      <div class="rounded-lg shadow-lg p-6">
        <h1 class="text-2xl font-bold mb-6 text-center">Buyer/Seller Module</h1>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          <!-- Buy a Flat Option -->
          <div
            class="bg-blue-50 p-10 rounded-lg shadow hover:shadow-lg transition-all cursor-pointer transform hover:scale-105"
            (click)="selectBuyOption()"
          >
            <div class="flex flex-col items-center text-center mb-4">
              <mat-icon width="32" height="32" class="text-blue-600 mb-3">search</mat-icon>
              <h2 class="text-2xl font-semibold text-blue-800">Buy a flat</h2>
            </div>
            <p class="text-gray-600 text-center">
              Find your dream property with our advanced search tools and filters.
            </p>
          </div>

          <!-- Sell a Flat Option -->
          <div
            class="bg-purple-50 p-10
            rounded-lg
            shadow
            hover:shadow-lg
            transition-all cursor-pointer transform hover:scale-105"
            (click)="selectSellOption()"
          >
            <div class="flex flex-col items-center text-center mb-4">
              <mat-icon width="32" height="32" class="text-purple-600 mb-3">sell</mat-icon>
              <h2 class="text-2xl font-semibold text-purple-800">Sell a flat</h2>
            </div>
            <p class="text-gray-600 text-center">
              List your property for sale and reach potential buyers quickly.
            </p>
          </div>
        </div>
      </div>
    </div>
  `,
})
export class BuyerSellerComponent {
  private router = inject(Router);
  private snackBar = inject(MatSnackBar);

  selectBuyOption(): void {
    // this.snackBar.open('Coming Soon: Buy a Flat feature will be available soon.', 'Close', {
    //   duration: 5000,
    //   horizontalPosition: 'center',
    //   verticalPosition: 'bottom',
    //   panelClass: ['bg-blue-600', 'text-white'],
    // });
    this.router.navigate(['/buyer-seller/sales-listing']);
  }

  selectSellOption(): void {
    this.router.navigate(['/buyer-seller/my-sales-listing']);
  }
}
