const mysql = require('mysql2/promise');
require('dotenv').config();

async function initializeAuthDatabase() {
  // First, connect without specifying database to create it if it doesn't exist
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
  });

  try {
    // Create database if it doesn't exist
    await connection.execute(`CREATE DATABASE IF NOT EXISTS ${process.env.DB_NAME}`);
    console.log(`✅ Database '${process.env.DB_NAME}' is ready.`);
    
    // Use the database
    await connection.execute(`USE ${process.env.DB_NAME}`);
    
    // Create users table (no dependencies)
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
    
        -- Basic Information
        name VARCHAR(100),
        user_name VARCHA<PERSON>(100),
        email VARCHAR(320),
        password VARCHAR(255),
    
        -- Contact Details
        mobile_no VARCHAR(25),
        mobile_no_code VARCHAR(10),
    
        -- OTP & Authentication
        otp_code VARCHAR(10),
        otp_expiry DATETIME,
        auth_provider VARCHAR(50),
    
        -- Verification Flags
        is_email_verified TINYINT(1) DEFAULT 0,
        is_mobile_verified TINYINT(1) DEFAULT 0,
    
        -- Profile Details
        profile_pic_url VARCHAR(255),
        bio TEXT,
        gender VARCHAR(10),
        date_of_birth DATE,
    
        -- Address Information
        country VARCHAR(50),
        state VARCHAR(100),
        city VARCHAR(100),
        zipcode VARCHAR(10),
        address TEXT,
    
        -- Professional Details
        profession VARCHAR(50),
        qualification VARCHAR(40),
        experience VARCHAR(25),
        skill VARCHAR(225),
    
        -- Account Status Flags
        is_blocked TINYINT(1) DEFAULT 0,
        is_deleted TINYINT(1) DEFAULT 0,
    
        -- Timestamps
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Users table is ready.');

    // Create roles table (no dependencies)
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS roles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        role_type VARCHAR(50),
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_deleted TINYINT(1) DEFAULT 0,
        is_active TINYINT(1) DEFAULT 1,
        created_by INT(11),
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Roles table is ready.');

    // Create user_roles table (depends on both users and roles)
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS user_roles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT(11) NOT NULL,
        role_id INT(11) NOT NULL,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user_role (user_id, role_id)
      )
    `);
    console.log('✅ User roles table is ready.');

    // Create sessions table for authentication (depends on users)
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS sessions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        token VARCHAR(255) UNIQUE NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      )
    `);
    console.log('✅ Sessions table is ready.');

    // Create password_reset_tokens table (depends on users)
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS password_reset_tokens (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        token VARCHAR(255) UNIQUE NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        used BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      )
    `);
    console.log('✅ Password reset tokens table is ready.');

    console.log('🎉 Authentication database initialization completed successfully!');
    
  } catch (error) {
    console.error('❌ Authentication database initialization failed:', error.message);
    throw error;
  } finally {
    await connection.end();
  }
}

module.exports = initializeAuthDatabase; 