import { CommonModule } from '@angular/common';
import { Component, type OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { apiRPC, injectController } from '@api/rpc';
import { SVGIconComponent } from '@lib/angular/svg-icon.component';
import {
  ResaleTrendChartComponent,
  type ResaleTrendChartData,
} from '../../component/resale-trend-chart/resale-trend-chart.component';

// Interface for Top Performers data
interface TopPerformer {
  street: string;
  volume: number;
}

@Component({
  selector: 'app-hdb-resale-trend-dummy',
  templateUrl: './hdb-resale-trend-dummy.component.html',
  standalone: true,
  imports: [CommonModule, FormsModule, SVGIconComponent, ResaleTrendChartComponent],
})
export class HdbResaleTrendComponent implements OnInit {
  // Inject HDB controller
  readonly #hdbController = injectController(apiRPC.HDBController);

  // Towns dropdown
  towns: string[] = ['All Towns'];
  selectedTown = 'All Towns';
  public paginatedPerformers: any[] = [];

  // Flag to track if towns are loading
  isLoadingTowns = false;

  // Streets dropdown
  streets: string[] = [
    'All Streets',
    'Ang Mo Kio Ave 1',
    'Ang Mo Kio Ave 2',
    'Ang Mo Kio Ave 3',
    'Ang Mo Kio Ave 4',
    'Ang Mo Kio Ave 5',
    'Bedok North Ave 1',
    'Bedok North Ave 2',
    'Bedok South Ave 1',
    'Tampines Ave 1',
    'Tampines Ave 2',
    'Tampines Ave 3',
  ];
  selectedStreet = 'All Streets';

  // Flat types
  flatTypes: string[] = ['All Types', '1 Room', '2 Room', '3 Room', '4 Room', '5 Room'];
  selectedFlatTypes: string[] = ['All Types'];

  // Time period
  fromDate = '';
  toDate = '';

  // Sort options
  sortOptions: string[] = ['Month', 'Quarter', 'Year'];
  selectedSortOption = 'Month';

  // Chart types
  chartTypes: string[] = [
    'Average PSF',
    'Median PSF',
    'Average Price',
    'Median Price',
    'Capital Gain (PSF)',
  ];
  selectedChartType = 'Average PSF';

  // Chart data for the resale trend chart
  chartData: ResaleTrendChartData = {
    title: 'Resale Trend',
    filters: {
      town: 'All Towns',
      street: 'All Streets',
      flatType: ['All Types'],
      dateRange: { from: '', to: '' },
      sortBy: 'Month',
      chartType: 'Average PSF',
    },
    labels: [],
    datasets: [],
    chartType: 'line',
  };

  // Top Performers data
  topPerformers: TopPerformer[] = [];

  // Pagination variables
  currentPage = 1;
  itemsPerPage = 10;
  totalPages = 1;

  // Filter summary for table - matches chart component style
  tableFilterSummary: string[] = [];

  constructor() {
    // Set default dates (last 6 months)
    const today = new Date();
    this.toDate = today.toISOString().split('T')[0];

    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(today.getMonth() - 6);
    this.fromDate = sixMonthsAgo.toISOString().split('T')[0];

    // Initialize chart with dummy data
    this.generateDummyChartData();

    // Initialize top performers data
    this.generateTopPerformersData();
  }

  ngOnInit(): void {
    this.fetchTowns();
    this.updatePaginatedPerformers();
  }

  updatePaginatedPerformers() {
    const start = (this.currentPage - 1) * 10;
    const end = this.currentPage * 10;
    this.paginatedPerformers = this.topPerformers.slice(start, end);
  }

  // Fetch towns from the API
  async fetchTowns(): Promise<void> {
    this.isLoadingTowns = true;
    try {
      const response = await this.#hdbController.getAllTowns();
      if (response && response.data) {
        // Add 'All Towns' as the first option
        const townNames = response.data.map((town: any) => town.name);
        this.towns = ['All Towns', ...townNames];
      }
      console.log('Towns loaded:', this.towns);
    } catch (error) {
      console.error('Error fetching towns:', error);
    } finally {
      this.isLoadingTowns = false;
    }
  }

  /**
   * Toggle selection of flat type
   * @param type Flat type to toggle
   */
  toggleFlatType(type: string): void {
    if (type === 'All Types') {
      // If "All Types" is clicked, select only it
      this.selectedFlatTypes = ['All Types'];
    } else {
      // Remove "All Types" if it's selected
      if (this.selectedFlatTypes.includes('All Types')) {
        this.selectedFlatTypes = this.selectedFlatTypes.filter((t) => t !== 'All Types');
      }

      // Toggle the selected type
      if (this.selectedFlatTypes.includes(type)) {
        this.selectedFlatTypes = this.selectedFlatTypes.filter((t) => t !== type);

        // If no types are selected, default to "All Types"
        if (this.selectedFlatTypes.length === 0) {
          this.selectedFlatTypes = ['All Types'];
        }
      } else {
        this.selectedFlatTypes.push(type);
      }
    }

    // Update chart data when flat type changes
    this.updateChartData();
  }

  /**
   * Select sort option
   * @param option Sort option to select
   */
  selectSortOption(option: string): void {
    this.selectedSortOption = option;

    // Update chart data when sort option changes
    this.updateChartData();
  }

  /**
   * Reset street selection to 'All Streets' when town changes
   * @param town Selected town
   */
  onTownChange(): void {
    this.selectedStreet = 'All Streets';
    this.updateChartData();
  }

  /**
   * Select chart type
   * @param type Chart type to select
   */
  selectChartType(type: string): void {
    this.selectedChartType = type;

    // Update chart data when chart type changes
    this.updateChartData();
  }

  /**
   * Generate dummy chart data for demonstration
   */
  private generateDummyChartData(): void {
    // Generate date labels for the past 8 quarters (2 years)
    const labels: string[] = [];
    const dateObjects: Date[] = [];
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentQuarter = Math.floor(currentDate.getMonth() / 3) + 1;

    // Generate 8 quarters (2 years) of data points
    for (let i = 0; i < 8; i++) {
      const quarter = ((currentQuarter - i - 1) % 4) + 1;
      const year = currentYear - Math.floor((i + currentQuarter) / 4);

      // Create a date for the middle of the quarter
      const month = (quarter - 1) * 3;
      const date = new Date(year, month, 15);
      dateObjects.push(date);
    }

    // Sort dates in ascending order (oldest to newest)
    dateObjects.sort((a, b) => a.getTime() - b.getTime());

    // Convert sorted dates to ISO strings for labels
    labels.push(...dateObjects.map((date) => date.toISOString()));

    // Generate dummy data based on the selected chart type
    let data: number[] = [];
    const label = this.selectedChartType;

    // Generate random data with an upward trend
    switch (this.selectedChartType) {
      case 'Average PSF':
        data = [540, 590, 480, 570, 620, 650, 620, 650];
        break;
      case 'Median PSF':
        data = [520, 570, 460, 550, 600, 630, 600, 630];
        break;
      case 'Average Price':
        data = [450000, 470000, 430000, 490000, 510000, 530000, 520000, 540000];
        break;
      case 'Median Price':
        data = [440000, 460000, 420000, 480000, 500000, 520000, 510000, 530000];
        break;
      case 'Capital Gain (PSF)':
        data = [15, 20, 10, 25, 30, 35, 30, 40];
        break;
      default:
        data = [540, 590, 480, 570, 620, 650, 620, 650];
        break;
    }

    // Update chart data
    this.chartData = {
      title: 'Resale Trend',
      filters: {
        town: this.selectedTown,
        street: this.selectedStreet,
        flatType: this.selectedFlatTypes,
        dateRange: { from: this.fromDate, to: this.toDate },
        sortBy: this.selectedSortOption,
        chartType: this.selectedChartType,
      },
      labels: labels,
      datasets: [
        {
          label: label,
          data: data,
          borderColor: 'rgba(54, 162, 235, 1)',
          backgroundColor: 'rgba(54, 162, 235, 0.2)',
          borderWidth: 2,
          tension: 0.4,
        },
      ],
      chartType: 'line',
    };
  }

  /**
   * Update chart data when filters change
   */
  private updateChartData(): void {
    // In a real implementation, this would fetch data from a service
    // For now, we'll just update the filters and regenerate dummy data
    this.chartData.filters = {
      town: this.selectedTown,
      street: this.selectedStreet,
      flatType: this.selectedFlatTypes,
      dateRange: { from: this.fromDate, to: this.toDate },
      sortBy: this.selectedSortOption,
      chartType: this.selectedChartType,
    };

    // Regenerate dummy data
    this.generateDummyChartData();

    // Update top performers data based on new filters
    this.generateTopPerformersData();

    // Reset pagination to first page when filters change
    this.currentPage = 1;
  }

  /**
   * Generate dummy top performers data
   */
  private generateTopPerformersData(): void {
    // Clear existing data
    this.topPerformers = [];

    // Generate dummy data based on selected town
    const townPrefix = this.selectedTown === 'All Towns' ? 'Sengkang' : this.selectedTown;

    // Generate 30 dummy entries (max allowed)
    for (let i = 1; i <= 30; i++) {
      const streetNumber = Math.floor(Math.random() * 10) + 1;
      this.topPerformers.push({
        street: `${townPrefix} Ave ${streetNumber}`,
        volume: Math.floor(Math.random() * 50) + 10, // Random volume between 10-60
      });
    }

    // Sort by volume (highest first)
    this.topPerformers.sort((a, b) => b.volume - a.volume);

    // Calculate total pages
    this.totalPages = Math.ceil(this.topPerformers.length / this.itemsPerPage);

    // Generate filter summary for table
    this.generateTableFilterSummary();
  }

  /**
   * Generate filter summary for the table - matches chart component logic
   */
  private generateTableFilterSummary(): void {
    const filters = this.chartData.filters;
    this.tableFilterSummary = [];

    if (filters.town && filters.town !== 'All Towns') {
      this.tableFilterSummary.push(`Town: ${filters.town}`);
    }

    if (filters.street && filters.street !== 'All Streets') {
      this.tableFilterSummary.push(`Street: ${filters.street}`);
    }

    if (
      filters.flatType &&
      filters.flatType.length > 0 &&
      !(filters.flatType.length === 1 && filters.flatType[0] === 'All Types')
    ) {
      this.tableFilterSummary.push(`Flat Type: ${filters.flatType.join(', ')}`);
    }

    if (filters.dateRange && filters.dateRange.from && filters.dateRange.to) {
      const from = this.formatDate(filters.dateRange.from);
      const to = this.formatDate(filters.dateRange.to);
      this.tableFilterSummary.push(`Period: ${from} - ${to}`);
    }

    if (filters.sortBy) {
      this.tableFilterSummary.push(`Sort By: ${filters.sortBy}`);
    }

    // Add volume metric
    this.tableFilterSummary.push(`Metric: Volume`);
  }

  /**
   * Format date for display in filter summary
   */
  private formatDate(dateString: string): string {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
  }

  /**
   * Get the latest quarter for display in the table header
   */
  getLatestQuarter(): string {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentQuarter = Math.floor(currentDate.getMonth() / 3) + 1;
    return `Q${currentQuarter} ${currentYear}`;
  }

  /**
   * Get display text for selected flat type
   */
  getSelectedFlatTypeDisplay(): string {
    if (this.selectedFlatTypes.includes('All Types')) {
      return 'All Types';
    } else if (this.selectedFlatTypes.length === 1) {
      return this.selectedFlatTypes[0];
    } else {
      return 'Multiple Types';
    }
  }

  /**
   * Get the town display for the table column header
   */
  getTableTownDisplay(): string {
    return this.selectedTown === 'All Towns' ? 'All Towns' : this.selectedTown;
  }

  /**
   * Get the dynamic column title for the table based on selected chart type
   */
  getTableColumnTitle(): string {
    const flatTypeDisplay = this.getSelectedFlatTypeDisplay();

    // Use the selected chart type to determine the column title
    let metricType = 'Volume';

    switch (this.selectedChartType) {
      case 'Average PSF':
        metricType = 'Average PSF';
        break;
      case 'Median PSF':
        metricType = 'Median PSF';
        break;
      case 'Average Price':
        metricType = 'Average Price';
        break;
      case 'Median Price':
        metricType = 'Median Price';
        break;
      case 'Capital Gain (PSF)':
        metricType = 'Capital Gain';
        break;
      default:
        metricType = 'Volume';
    }

    return `${flatTypeDisplay} ${metricType}`;
  }

  /**
   * Change current page in pagination
   */
  changePage(page: number): void {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
    }
  }

  /**
   * Generate array for pagination display
   * Shows current page, first, last, and pages around current
   */
  getPaginationArray(): (number | string)[] {
    const pages: (number | string)[] = [];

    if (this.totalPages <= 5) {
      // If 5 or fewer pages, show all pages
      for (let i = 1; i <= this.totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Always show first page
      pages.push(1);

      // Show ellipsis if current page is more than 3
      if (this.currentPage > 3) {
        pages.push('...');
      }

      // Show pages around current page
      const start = Math.max(2, this.currentPage - 1);
      const end = Math.min(this.totalPages - 1, this.currentPage + 1);

      for (let i = start; i <= end; i++) {
        pages.push(i);
      }

      // Show ellipsis if current page is less than totalPages - 2
      if (this.currentPage < this.totalPages - 2) {
        pages.push('...');
      }

      // Always show last page
      pages.push(this.totalPages);
    }

    return pages;
  }
}
