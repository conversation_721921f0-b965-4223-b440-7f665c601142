{"folders": [{"path": ".."}], "settings": {"workbench.colorTheme": "<PERSON><PERSON><PERSON><PERSON>", "editor.fontFamily": "Cascadia Code", "editor.fontSize": 18, "editor.fontWeight": "600", "editor.fontLigatures": true, "editor.lineHeight": 40, "files.exclude": {".angular": true, ".nx": true, ".docker": true, ".icons-cache": true, "**/.DS_Store": true, "**/.git": true, "**/node_modules": true, "dist": true}, "files.watcherExclude": {".angular": true, ".nx": true, ".docker": true, ".icons-cache": true, "**/.DS_Store": true, "**/.git": true, "**/node_modules": true, "dist": true}, "search.exclude": {".angular": true, ".nx": true, ".docker": true, ".icons-cache": true, "**/.DS_Store": true, "**/.git": true, "**/node_modules": true, "dist": true}, "editor.formatOnSave": false, "typescript.format.enable": false, "html.format.enable": false, "editor.formatOnPaste": false, "typescript.inlayHints.parameterNames.enabled": "literals", "[html]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "quickfix.biome": "explicit"}}, "[typescript]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.organizeImports": "explicit", "source.fixAll.eslint": "explicit", "quickfix.biome": "explicit"}}, "[typescriptreact]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.organizeImports": "explicit", "source.fixAll": "explicit"}}, "[scss]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[jsonc]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.organizeImports": "explicit", "source.fixAll.eslint": "explicit", "quickfix.biome": "explicit"}}, "prettier.singleQuote": true, "workbench.colorCustomizations": {"activityBar.activeBackground": "#13321b", "activityBar.activeBorder": "#422c74", "activityBar.background": "#13321b", "activityBar.foreground": "#e7e7e7", "activityBar.inactiveForeground": "#e7e7e799", "activityBarBadge.background": "#6446b9", "activityBarBadge.foreground": "#e7e7e7", "statusBar.background": "#050d07", "statusBar.foreground": "#e7e7e7", "statusBarItem.hoverBackground": "#13321b", "titleBar.activeBackground": "#050d07", "titleBar.activeForeground": "#e7e7e7", "titleBar.inactiveBackground": "#050d0799", "titleBar.inactiveForeground": "#e7e7e799", "sash.hoverBorder": "#13321b", "statusBarItem.remoteBackground": "#050d07", "statusBarItem.remoteForeground": "#e7e7e7", "commandCenter.border": "#e7e7e799"}, "peacock.color": "#050d07", "typescript.tsdk": "node_modules\\typescript\\lib"}}