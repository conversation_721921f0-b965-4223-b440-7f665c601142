import type { <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { Component, Injectable, inject } from '@angular/core';
import type { FormGroup } from '@angular/forms';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import type { MatDialogConfig } from '@angular/material/dialog';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { daisyMerge } from '@lib/common/tailwind-merge';
import { Subject, firstValueFrom } from 'rxjs';
import { EnsureSingleInstance } from '../../common/test-root-service';
import { MatrixFormFieldComponent } from './matrix-form-field';
import type { SilverField } from './silver-field.component';
import {
  SilverFieldComponent,
  SilverFieldService,
  SilverFieldTypes,
} from './silver-field.component';

export interface ModalDataI {
  heading: string;
  form: SilverField[];
  btn1Name?: string;
  btn2Name?: string;
  btn1Class?: string;
  btn2Class?: string;
  closeOnSubmit?: boolean;
  autoRefineValue?: boolean;
}

interface InputRef_I {
  onFormCreate: PromiseWithResolvers<any>;
  onFormSubmit: Subject<any>;
}

interface FormModalRef_I {
  formReady: () => Promise<any>;
  afterClosed: () => Promise<any>;
  refineValue: () => any;
  onSubmit$: Subject<any>;
  close: () => void;
  closed: boolean;
  dialogRef: MatDialogRef<FormModalComponent, any>;
}

export interface ResponseI {
  action: boolean;
  value: any;
  refineValue: () => any;
}

@Injectable({ providedIn: 'root' })
export class FormModalService {
  readonly #dialog = inject(MatDialog);
  readonly #silverFieldService = inject(SilverFieldService);

  constructor() {
    EnsureSingleInstance(this);
  }

  public open(data: ModalDataI, options = {}): Promise<ResponseI> {
    data.closeOnSubmit = data.closeOnSubmit !== false;
    data.autoRefineValue = data.autoRefineValue !== false;
    const dialogRef = this.#dialog.open(FormModalComponent, {
      width: '400px',
      data: { data },
      enterAnimationDuration: 0,
      panelClass: ['animate__animated', 'animate__slideInUp-10', 'g-dialog-mob-full'],
      ...options,
    });
    return firstValueFrom(dialogRef.afterClosed());
  }

  public openForm(data: ModalDataI, options: MatDialogConfig<any> = {}) {
    data.closeOnSubmit = data.closeOnSubmit !== false;
    data.autoRefineValue = data.autoRefineValue !== false;
    const onFormCreate = Promise.withResolvers<FormGroup>();
    const onFormSubmit = new Subject<any>();
    const input: InputRef_I = { onFormCreate, onFormSubmit };
    const dialogRef = this.#dialog.open(FormModalComponent, {
      width: '400px',
      data: { data, ref: input },
      enterAnimationDuration: 0,
      panelClass: ['animate__animated', 'animate__slideInUp-10', 'g-dialog-mob-full'],
      ...options,
    });
    const afterClosed = firstValueFrom(dialogRef.afterClosed());
    const ref: FormModalRef_I = {
      onSubmit$: onFormSubmit,
      formReady: () => onFormCreate.promise,
      close: () => dialogRef.close(),
      afterClosed: () => afterClosed,
      refineValue: async () =>
        this.#silverFieldService.refineValue(data.form, (await onFormCreate.promise).value),
      closed: false,
      dialogRef,
    };
    afterClosed.then(() => {
      ref.closed = true;
      onFormSubmit.complete();
    });
    return ref;
  }
}

@Component({
  imports: [
    FormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatIconModule,
    MatrixFormFieldComponent,
    ReactiveFormsModule,
    SilverFieldComponent,
  ],
  template: `<div mat-dialog-title style="font-size: 1.2rem">
      {{ modalData.heading }}
    </div>
    <mat-dialog-content class="mat-typography" [formGroup]="formGroup">
      @for (item of form; track item; let i = $index) {
        <div class="flex flex-col mb-3" [class]="item.cssClass || ''">
          @if (item.controlType === SilverFieldTypes.MATRIX) {
            @if (item.label) {
              <div class="mb-2 text-sm font-medium text-gray-900 dark:text-white">
                {{ item.label }} {{ item.valid?.required ? '*' : '' }}
              </div>
            }
            <silver-matrix-field
              color="primary"
              [formControl]="formGroup.controls[item.key]"
              [options]="item"
            />
          } @else {
            <app-silver-field
              class="flex flex-col"
              [class]="item.silverClass || ''"
              [field]="item"
              [ctrlName]="item.key"
              [form]="formGroup"
            />
          }
        </div>
      }
    </mat-dialog-content>
    <mat-dialog-actions align="end">
      <button
        [mat-dialog-close]="false"
        [class]="daisyMerge('btn btn-outline btn-sm btn-error mr-3', btn1Class)"
      >
        {{ btn1Name }}
      </button>
      <button
        [class]="daisyMerge('btn btn-outline btn-sm btn-primary', btn2Class)"
        (click)="submit()"
      >
        {{ btn2Name }}
        <mat-icon [class]="formGroup.invalid ? 'text-error' : 'text-success'">{{
          formGroup.invalid ? 'error_outline' : 'done_all'
        }}</mat-icon>
      </button>
    </mat-dialog-actions>`,
})
export class FormModalComponent implements OnInit, OnDestroy {
  readonly #dialogRef: MatDialogRef<FormModalComponent> = inject(MatDialogRef);
  readonly #silverFieldService = inject(SilverFieldService);
  readonly #data: { data: ModalDataI; ref: any } = inject(MAT_DIALOG_DATA);
  public formGroup!: FormGroup<any>;
  public modalData!: ModalDataI;
  public btn1Name!: string;
  public btn2Name!: string;
  public btn1Class = '';
  public btn2Class = '';
  public daisyMerge = daisyMerge;
  public form: SilverField[] = [];
  public ref!: InputRef_I;
  refineValue = () => this.#silverFieldService.refineValue(this.form, this.formGroup.value);
  SilverFieldTypes = SilverFieldTypes;

  public ngOnInit(): void {
    this.modalData = this.#data.data;
    this.ref = this.#data.ref;
    this.btn1Name = this.modalData.btn1Name ?? 'Cancel';
    this.btn2Name = this.modalData.btn2Name ?? 'Submit';
    this.btn1Class = this.modalData.btn1Class ?? '';
    this.btn2Class = this.modalData.btn2Class ?? '';
    this.form = this.modalData.form ?? [];
    this.formGroup = this.#silverFieldService.fieldsToForm(this.form);
    if (this.ref) this.ref.onFormCreate.resolve(this.formGroup);
  }

  public submit() {
    if (this.formGroup.invalid) {
      this.formGroup.markAllAsTouched();
      return;
    }
    if (this.modalData.closeOnSubmit) {
      const data = {
        action: true,
        value: this.modalData.autoRefineValue ? this.refineValue() : this.formGroup.value,
        refineValue: this.modalData.autoRefineValue ? undefined : this.refineValue,
      };
      this.#dialogRef.close(data);
    } else {
      this.ref.onFormSubmit.next(
        this.modalData.autoRefineValue ? this.refineValue() : this.formGroup.value,
      );
    }
  }

  ngOnDestroy(): void {
    this.#silverFieldService.destroyFields(this.form);
  }
}
