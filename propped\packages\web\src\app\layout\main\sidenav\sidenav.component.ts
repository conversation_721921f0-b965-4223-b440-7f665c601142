import type { OnInit } from '@angular/core';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, inject } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { RouterModule } from '@angular/router';
import type { ActiveUser } from '@lib/angular/auth/models/active-user.model';
import { SilverSearchBarComponent } from '@lib/angular/search-bar.component';
import { SVGIconComponent } from '@lib/angular/svg-icon.component';
import { ROLES } from '@lib/common/const/roles';
import { FuzzySearch } from '@lib/common/fuzzy-search';
import { AuthService } from '../../../services/auth.service';
import type { Menu, MenuGroup } from '../../../services/design/sidebar-menu.service';
import { Menus } from '../../../services/design/sidebar-menu.service';

@Component({
  imports: [SVGIconComponent, RouterModule, SilverSearchBarComponent, MatIconModule],
  selector: 'app-sidenav',
  templateUrl: './sidenav.component.html',
  styles: [
    `
      :host {
        width: 16rem;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
      }
    `,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SidenavComponent implements OnInit {
  readonly #authService = inject(AuthService);
  readonly #cdRef = inject(ChangeDetectorRef);

  public fuzzyMenus: typeof FuzzySearch;
  public searchResult: any[] = [];
  public menus: MenuGroup[] = [];
  public searchMenus: MenuGroup[] = [];
  private loginUser: ActiveUser;

  async ngOnInit() {
    const userDetail = await this.#authService.getActiveUser();
    this.loginUser = userDetail;
    this.menus = this.setMenu(this.#authService.permissionMap);
    this.searchMenus = this.menus;
    this.#cdRef.detectChanges();
  }

  public search(query: string) {
    this.searchResult = this.fuzzyMenus.search(query);
    this.searchMenus = this.menus.filter(
      (v) => this.searchResult.includes(v) || v.children.some((c) => this.searchResult.includes(c)),
    );
  }

  private setMenu(perms: Record<string, number[]>) {
    const fun = (v: MenuGroup | Menu) =>
      perms[v.perm] ? this.loginUser.hasAnyRole(perms[v.perm]) : true;
    const isMaster = this.loginUser.hasRole(ROLES.MASTER.id);
    let m = isMaster ? Menus : Menus.filter(fun);
    if (!isMaster) m.forEach((v) => (v.children = v.children.filter(fun)));
    m = m.filter((v) => v.children.length > 0);
    const allMenus = m.flatMap((v: MenuGroup | Menu) => (v as MenuGroup).children ?? v);
    this.fuzzyMenus = new FuzzySearch(allMenus, ['title'], { sort: true });
    this.search('');
    return m;
  }
}
