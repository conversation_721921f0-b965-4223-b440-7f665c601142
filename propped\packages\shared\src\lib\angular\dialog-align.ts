export const dialogFromRight = {
  panelClass: [
    'animate__animated',
    'animate__slideInRight',
    'h-full',
    'g-dialog-full',
    'g-dialog-mob-full',
    '!absolute',
    '!right-0',
  ],
  enterAnimationDuration: 0,
};

export const dialogFromLeft = {
  panelClass: [
    'animate__animated',
    'animate__slideInLeft',
    'h-full',
    'g-dialog-full',
    'g-dialog-mob-full',
    '!absolute',
    '!left-0',
  ],
  enterAnimationDuration: 0,
};

export const dialogFromBottom = {
  panelClass: [
    'animate__animated',
    'animate__slideInUp',
    '!absolute',
    '!bottom-0',
    'rounded-none',
    '!rounded-t-2xl',
    '[--animate-duration:0.2s]',
  ],
  enterAnimationDuration: 0,
};
export const dialogFromCenter = {
  panelClass: ['animate__animated', 'animate__zoomIn', '!absolute', '[--animate-duration:0.2s]'],
  enterAnimationDuration: 0,
};
