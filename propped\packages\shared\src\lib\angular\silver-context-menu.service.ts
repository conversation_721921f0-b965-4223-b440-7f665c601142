import { CdkContextMenuTrigger, CdkMenu } from '@angular/cdk/menu';
import type { ElementRef, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { ChangeDetectionStrategy, Component, Injectable, ViewChild, inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import type { Subscription } from 'rxjs';
import { firstValueFrom, fromEvent, take } from 'rxjs';
import { wait } from '../common/fun';
import { EnsureSingleInstance } from '../common/test-root-service';
import { SVGIconComponent } from './svg-icon.component';

interface ContextMenuDataI {
  pos: { clientX: number; clientY: number };
  list: ContextMenu[];
  innerClass?: string;
}

export interface ContextMenu {
  name: string;
  key?: string;
  innerClass?: string;
  action?: (...x: any[]) => any;
  cssClass?: string;
  iconClass?: string;
  disabled?: boolean;
  icon?: string;
  url?: string;
  submenu?: ContextMenu[];
}

@Injectable({ providedIn: 'root' })
export class SilverContextMenuService {
  readonly #dialog = inject(MatDialog);

  constructor() {
    EnsureSingleInstance(this);
  }

  open(
    pos: { clientX: number; clientY: number },
    list: ContextMenu[],
    opt: { innerClass?: string } = {},
  ): any {
    if (pos instanceof MouseEvent || pos instanceof PointerEvent) pos.preventDefault();
    const ref = this.#dialog.open<ContextMenuComponent, ContextMenuDataI, void>(
      ContextMenuComponent,
      {
        data: { pos, list, innerClass: opt.innerClass },
        disableClose: true,
        hasBackdrop: false,
        panelClass: 'context-menu',
        position: { left: `${pos.clientX}px`, top: `${pos.clientY}px` },
      },
    );
    return firstValueFrom(ref.afterClosed());
  }
}

@Component({
  selector: 'app-context-menu',
  imports: [CdkContextMenuTrigger, CdkMenu, MatIconModule, SVGIconComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <div
      style="visibility: hidden; position: fixed"
      class="base-300 p-2"
      #trigger
      [cdkContextMenuTriggerFor]="inner"
    ></div>
    <ng-template #inner>
      <ul
        class="menu bg-base-200 w-56 rounded-box flex-nowrap overflow-auto"
        [class]="data.innerClass || ''"
        cdkMenu
        (closed)="closed()"
      >
        @for (item of data.list; track $index) {
          <li [class]="item.innerClass || ''" [disabled]="item.disabled ?? true">
            <a (click)="select(item)" [class]="item.cssClass || ''">
              @if (item.icon) {
                <mat-icon
                  class="!w-5 !h-5 text-[20px] p-0 !overflow-visible rounded-full"
                  [class]="item.iconClass || ''"
                  >{{ item.icon }}</mat-icon
                >
              } @else if (item.url) {
                <app-svg-icon
                  class="w-5 h-5 text-[20px] p-0 !overflow-visible rounded-full"
                  [class]="item.iconClass || ''"
                  [icon]="item.url"
                />
              }
              {{ item.name }}
            </a>
          </li>
        }
      </ul>
    </ng-template>
  `,
})
export class ContextMenuComponent implements OnInit, OnDestroy {
  @ViewChild('trigger', { static: true }) contextMenu!: ElementRef;
  readonly #dialogRef: MatDialogRef<ContextMenuComponent> = inject(MatDialogRef);
  public data: ContextMenuDataI = inject(MAT_DIALOG_DATA);
  #subscriptions: Record<string, Subscription> = {};
  #selected!: ContextMenu;

  async ngOnInit(): Promise<void> {
    await wait(0);
    this.open();
    this.#subscriptions['window#blur'] = fromEvent(window, 'blur')
      .pipe(take(1))
      .subscribe(() => this.close());
    this.#subscriptions['window#wheel'] = fromEvent(window, 'wheel')
      .pipe(take(1))
      .subscribe(() => this.close());
  }

  public closed() {
    this.#dialogRef.close({ action: !!this.#selected, result: this.#selected || undefined });
  }

  public select(item: ContextMenu) {
    this.#selected = item;
    this.close();
  }

  private open() {
    const { clientX, clientY } = this.data.pos;
    const evt = new MouseEvent('contextmenu', { bubbles: true, clientX, clientY });
    this.contextMenu.nativeElement.dispatchEvent(evt);
  }

  private close() {
    this.contextMenu.nativeElement.dispatchEvent(new MouseEvent('click'));
  }

  public ngOnDestroy(): void {
    for (const sub of Object.values(this.#subscriptions)) sub.unsubscribe();
    (this as any).#subscriptions = undefined;
  }
}
