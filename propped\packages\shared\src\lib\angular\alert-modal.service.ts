import type { ElementRef, OnInit } from '@angular/core';
import { Component, Injectable, NgZone, inject, viewChild } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import type { MatDialogConfig } from '@angular/material/dialog';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { daisyMerge } from '@lib/common/tailwind-merge';
import { marked } from 'marked';
import { firstValueFrom } from 'rxjs';
import { EnsureSingleInstance } from '../common/test-root-service';
import { SVGIconComponent } from './svg-icon.component';

interface AlertDataI {
  heading?: string;
  body?: string;
  icon?: string;
  iconSvg?: string;
  iconClass?: string;
  domClass?: string;
  innerClass?: string;
  md?: string;
  html?: string;
  dom?: (root: HTMLElement, onClose: Promise<void>) => void;
  singleBtn?: boolean;
  noBtn?: boolean;
  showHeading?: boolean;
  btn1Name?: string;
  btn2Name?: string;
  btn1Class?: string;
  btn2Class?: string;
  actionBoxClass?: string;
  contentClass?: string;
}

@Injectable({ providedIn: 'root' })
export class AlertModalService {
  readonly #dialog = inject(MatDialog);

  constructor() {
    EnsureSingleInstance(this);
  }

  public open(data: AlertDataI, options: MatDialogConfig<any> = {}) {
    const dialogRef = this.#dialog.open(AlertModalComponent, {
      width: '400px',
      data: { data },
      enterAnimationDuration: 0,
      panelClass: ['animate__animated', 'animate__bounceIn'],
      // exitAnimationDuration: 0,
      ...options,
    });
    return firstValueFrom(dialogRef.afterClosed());
  }

  public openModal(data: AlertDataI, options: MatDialogConfig<any> = {}) {
    const dialogRef = this.#dialog.open(AlertModalComponent, {
      width: '400px',
      data: { data },
      enterAnimationDuration: 0,
      panelClass: ['animate__animated', 'animate__bounceIn'],
      // exitAnimationDuration: 0,
      ...options,
    });
    return {
      dialogRef,
      close: (x: any) => dialogRef.close(x),
      onClose: firstValueFrom(dialogRef.afterClosed()),
    };
  }

  public show(message: string, heading?: string) {
    return this.open({ heading: heading || 'Message', body: message, singleBtn: true });
  }

  public confirm(message: string, heading?: string) {
    return this.open({ heading: heading || 'Confirm', md: message });
  }

  public deleteConfirm(message: string, heading?: string) {
    return this.open({
      heading: heading || '',
      // showHeading: false,
      iconSvg: 'iconify:fa:warning',
      iconClass: 'text-error text-[48px] !mb-4',
      md: message,
      btn1Name: 'Cancel',
      btn2Name: 'Delete',
      btn1Class: 'btn-xl',
      btn2Class: 'btn-xl btn-error btn-clear-outline',
      actionBoxClass: '!justify-center !pb-10',
      contentClass: 'flex-col gap-2',
      innerClass: 'text-center text-lg !mb-4',
    });
  }
}

@Component({
  imports: [MatDialogModule, MatButtonModule, MatIconModule, SVGIconComponent],
  template: ` @if (showHeading) {
      <div mat-dialog-title style="font-size: 1.2rem" class="!p-4 !flex flex-row items-center">
        {{ heading }}
        <mat-icon
          color="accent"
          class="ml-auto cursor-pointer hover:text-error"
          [mat-dialog-close]="false"
          >close</mat-icon
        >
      </div>
    }
    <mat-dialog-content
      class="mat-typography !flex items-center gap-3"
      [class]="alertData.contentClass ?? ''"
      style="max-height: unset "
    >
      @if (alertData.iconSvg) {
        <app-svg-icon
          [class]="
            daisyMerge(
              '!w-10 !h-10 text-[24px] p-2 !overflow-visible rounded-full',
              alertData.iconClass || ''
            )
          "
          [icon]="alertData.iconSvg"
        />
      } @else if (alertData.icon) {
        <mat-icon
          [class]="
            daisyMerge(
              '!w-10 !h-10 text-[24px] p-2 !overflow-visible rounded-full',
              alertData.iconClass || ''
            )
          "
          >{{ alertData.icon }}</mat-icon
        >
      }
      <div [class]="daisyMerge('hidden', alertData.domClass ?? '')" #bodyBox></div>
      @if (alertData.body) {
        <div [class]="alertData.innerClass ?? ''">{{ alertData.body }}</div>
      } @else if (alertData.md) {
        <div [class]="alertData.innerClass ?? ''" [innerHtml]="alertData.md"></div>
      }
    </mat-dialog-content>
    @if (!noBtn) {
      <mat-dialog-actions align="end" [class]="alertData.actionBoxClass">
        @if (singleBtn) {
          <button
            [mat-dialog-close]="true"
            class="btn btn-outline btn-sm btn-accent"
            cdkFocusInitial
          >
            {{ btn1Name }}
          </button>
        } @else {
          <button
            [mat-dialog-close]="false"
            [class]="daisyMerge('btn btn-outline btn-sm btn-error mr-3', btn1Class)"
            cdkFocusInitial
          >
            {{ btn1Name }}
          </button>
          <button
            [class]="daisyMerge('btn btn-outline btn-sm btn-primary', btn2Class)"
            [mat-dialog-close]="true"
          >
            {{ btn2Name }}
          </button>
        }
      </mat-dialog-actions>
    }`,
})
export class AlertModalComponent implements OnInit {
  public alertData!: AlertDataI;
  public btn1Class = '';
  public btn1Name!: string;
  public btn2Class = '';
  public btn2Name!: string;
  public daisyMerge = daisyMerge;
  public heading!: string;
  public noBtn!: boolean;
  public showHeading!: boolean;
  public singleBtn!: boolean;

  readonly #dialogRef: MatDialogRef<AlertModalComponent> = inject(MatDialogRef);
  readonly #ngZone = inject(NgZone);
  readonly data: { data: AlertDataI } = inject(MAT_DIALOG_DATA);

  readonly bodyBox = viewChild.required<ElementRef<HTMLDivElement>>('bodyBox');

  constructor() {
    this.alertData = this.data.data;
    const ad = this.alertData;
    if (ad.md) ad.md = marked(ad.md, { async: false }) as string;
    else if (ad.html) ad.md = ad.html;
    this.singleBtn = ad.singleBtn ?? false;
    this.noBtn = ad.noBtn ?? false;
    this.heading = ad.heading ?? 'Message';
    this.showHeading = ad.showHeading ?? true;
    this.btn1Name = ad.btn1Name || (ad.singleBtn ? 'OK' : 'Cancel');
    this.btn2Name = ad.btn2Name || 'Confirm';
    this.btn1Class = ad.btn1Class || '';
    this.btn2Class = ad.btn2Class || '';
  }

  ngOnInit(): void {
    if (!this.bodyBox() || !this.alertData.dom) return;
    this.alertData.domClass = daisyMerge(this.alertData.domClass || '', 'block');
    this.#ngZone.runOutsideAngular(() => {
      this.alertData.dom!(
        this.bodyBox().nativeElement,
        firstValueFrom(this.#dialogRef.afterClosed()),
      );
    });
  }
}
