import { type Model, Schema, model } from 'mongoose';

export interface IKinderGartenSchool {
  schoolName: string;
  location: {
    type: 'Point';
    coordinates: [number, number];
  };
}

// Define the schema
const KinderGartenSchoolSchema = new Schema<IKinderGartenSchool>({
  schoolName: { type: String, required: true },

  location: {
    type: { type: String, enum: ['Point'], default: 'Point' },
    coordinates: { type: [Number], required: true },
  },
});

KinderGartenSchoolSchema.index({ location: '2dsphere' });

export const KinderGartenSchoolModel: Model<IKinderGartenSchool> = model<IKinderGartenSchool>(
  'KinderGartenSchool',
  KinderGartenSchoolSchema,
);
