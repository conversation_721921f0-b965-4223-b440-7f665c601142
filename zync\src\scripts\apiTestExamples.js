/**
 * API Testing Examples for Zync Authentication System
 * 
 * This file contains example API calls for testing the authentication system.
 * You can use these examples with tools like Postman, curl, or any HTTP client.
 */

const API_BASE_URL = 'http://localhost:3005/auth';

// Example API calls for testing
const apiExamples = {
  
  // ========================================
  // REGISTRATION FLOW
  // ========================================
  
  registration: {
    step1_emailVerification: {
      method: 'POST',
      url: `${API_BASE_URL}/register`,
      body: {
        email: '<EMAIL>'
      },
      description: 'Step 1: Send OTP to email for verification'
    },
    
    step2_otpVerification: {
      method: 'POST',
      url: `${API_BASE_URL}/verify-register-otp`,
      body: {
        email: '<EMAIL>',
        otp: '12345' // Use OTP received in email
      },
      description: 'Step 2: Verify OTP sent to email'
    },
    
    step3_completeRegistration: {
      method: 'POST',
      url: `${API_BASE_URL}/complete-registration`,
      body: {
        email: '<EMAIL>',
        name: 'Test User',
        username: 'testuser123',
        password: 'SecurePass123!',
        confirmPassword: 'SecurePass123!'
      },
      description: 'Step 3: Complete registration with user details'
    }
  },

  // ========================================
  // LOGIN FLOW
  // ========================================
  
  login: {
    normalLogin: {
      method: 'POST',
      url: `${API_BASE_URL}/login`,
      body: {
        identifier: 'testuser123', // username or email
        password: 'SecurePass123!'
      },
      description: 'Normal login (returns JWT if no 2FA, or 2FA challenge)'
    },
    
    verifyTFAEmail: {
      method: 'POST',
      url: `${API_BASE_URL}/verify-tfa-email`,
      body: {
        tempToken: 'temporary_jwt_token_from_login',
        otp: '12345'
      },
      description: 'Verify 2FA using email OTP'
    },
    
    verifyTOTP: {
      method: 'POST',
      url: `${API_BASE_URL}/verify-totp`,
      body: {
        tempToken: 'temporary_jwt_token_from_login',
        totpCode: '123456'
      },
      description: 'Verify 2FA using TOTP from authenticator app'
    }
  },

  // ========================================
  // PASSWORD RESET FLOW
  // ========================================
  
  passwordReset: {
    step1_requestReset: {
      method: 'POST',
      url: `${API_BASE_URL}/forgot-password`,
      body: {
        email: '<EMAIL>'
      },
      description: 'Step 1: Request password reset (sends OTP to email)'
    },
    
    step2_verifyToken: {
      method: 'POST',
      url: `${API_BASE_URL}/verify-forgot-password-otp`,
      body: {
        email: '<EMAIL>',
        token: '12345'
      },
      description: 'Step 2: Verify reset token from email'
    },
    
    step3_resetPassword: {
      method: 'POST',
      url: `${API_BASE_URL}/reset-password`,
      body: {
        tempResetToken: 'temporary_reset_token_from_step2',
        newPassword: 'NewSecurePass123!',
        confirmPassword: 'NewSecurePass123!'
      },
      description: 'Step 3: Set new password'
    }
  },

  // ========================================
  // EMAIL ACTIVATION
  // ========================================
  
  emailActivation: {
    resendActivation: {
      method: 'POST',
      url: `${API_BASE_URL}/active-mail`,
      body: {
        email: '<EMAIL>'
      },
      description: 'Resend email activation OTP'
    },
    
    verifyActivation: {
      method: 'POST',
      url: `${API_BASE_URL}/verify-active-mail-otp`,
      body: {
        email: '<EMAIL>',
        otp: '12345'
      },
      description: 'Verify email activation OTP'
    }
  },

  // ========================================
  // VALIDATION ENDPOINTS
  // ========================================
  
  validation: {
    checkEmail: {
      method: 'POST',
      url: `${API_BASE_URL}/check-email-exists`,
      body: {
        email: '<EMAIL>'
      },
      description: 'Check if email already exists'
    },
    
    checkUsername: {
      method: 'POST',
      url: `${API_BASE_URL}/check-username-exists`,
      body: {
        username: 'testuser123'
      },
      description: 'Check if username already exists'
    }
  },

  // ========================================
  // 2FA MANAGEMENT (Requires Authentication)
  // ========================================
  
  twoFactorAuth: {
    toggleTFA: {
      method: 'POST',
      url: `${API_BASE_URL}/toggle-tfa`,
      headers: {
        'Authorization': 'Bearer your_jwt_token_here'
      },
      body: {
        enable: true // or false to disable
      },
      description: 'Enable or disable 2FA for the user'
    },
    
    setupTOTP: {
      method: 'POST',
      url: `${API_BASE_URL}/setup-totp`,
      headers: {
        'Authorization': 'Bearer your_jwt_token_here'
      },
      body: {},
      description: 'Generate TOTP QR code for setup'
    },
    
    confirmTOTPSetup: {
      method: 'POST',
      url: `${API_BASE_URL}/confirm-totp-setup`,
      headers: {
        'Authorization': 'Bearer your_jwt_token_here'
      },
      body: {
        tempSecret: 'encrypted_secret_from_setup',
        totpCode: '123456'
      },
      description: 'Confirm TOTP setup with verification code'
    }
  },

  // ========================================
  // SESSION MANAGEMENT
  // ========================================
  
  session: {
    logout: {
      method: 'POST',
      url: `${API_BASE_URL}/logout`,
      headers: {
        'Authorization': 'Bearer your_jwt_token_here'
      },
      body: {},
      description: 'Logout and invalidate JWT token'
    }
  }
};

// Helper function to display API examples
const displayAPIExamples = () => {
  console.log('🚀 Zync Authentication API Examples\n');
  console.log('Base URL:', API_BASE_URL);
  console.log('\n📋 Copy these examples to test with Postman, curl, or any HTTP client:\n');
  
  Object.entries(apiExamples).forEach(([category, endpoints]) => {
    console.log(`\n=== ${category.toUpperCase()} ===`);
    
    Object.entries(endpoints).forEach(([name, config]) => {
      console.log(`\n${name}:`);
      console.log(`  Description: ${config.description}`);
      console.log(`  Method: ${config.method}`);
      console.log(`  URL: ${config.url}`);
      
      if (config.headers) {
        console.log(`  Headers:`, JSON.stringify(config.headers, null, 4));
      }
      
      if (config.body && Object.keys(config.body).length > 0) {
        console.log(`  Body:`, JSON.stringify(config.body, null, 4));
      }
    });
  });
  
  console.log('\n\n📝 Notes:');
  console.log('- Replace placeholder values with actual data');
  console.log('- Use OTP codes received in emails');
  console.log('- Include JWT tokens in Authorization headers for protected endpoints');
  console.log('- All requests should have Content-Type: application/json header');
};

// Run the display function
displayAPIExamples();

module.exports = apiExamples;
