import { drizzle } from 'drizzle-orm/mysql2';
import mongoose from 'mongoose';
import { environment } from '../../environments/environment';

const settings = environment.mysql;

const makeMySQLUrl = (config) => {
  const protocol = 'mysql';
  const url = new URL(config.database, `${protocol}://${config.host}`);
  url.username = config.username;
  url.password = config.password;
  if (config.port) url.port = config.port;
  return url.toString();
};

const mysqlURI = makeMySQLUrl(settings);

export const drizzleDb = drizzle(mysqlURI, { logger: !environment.production });

const mongoConnectionURL = (config) => {
  const query: any = { w: 'majority' };
  if (config.auth) query.authSource = config.auth;
  const protocol = config.srv ? 'mongodb+srv' : 'mongodb';
  const url = new URL(config.database, `${protocol}://${config.host}`);
  url.username = config.username;
  url.password = config.password;
  url.search = new URLSearchParams(query).toString();
  if (config.port) url.port = config.port;
  return url.toString();
};

export const connectMongo = () => mongoose.connect(mongoConnectionURL(environment.mongodb));
