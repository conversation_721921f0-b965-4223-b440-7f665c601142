import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { SellerInfoComponent } from '../make-offer-buyer/seller-info/seller-info.component';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';

@Component({
  selector: 'app-preview-listing-seller-dialog',
  standalone: true,
  imports: [CommonModule, SellerInfoComponent],
  template: `
    <div class="flex flex-col items-center justify-center p-6 relative min-w-[360px]">
      <button class="absolute top-2 right-2 text-2xl" (click)="close()">&times;</button>
      <div class="text-2xl font-bold text-center mb-2">Good morning</div>
      <app-seller-info [username]="data?.userData?.name"/>
      <div class="text-xl font-semibold text-center my-4">This is your own listing</div>
      <button class="w-full bg-blue-600 text-white rounded-lg py-2 font-semibold mb-2" (click)="goToChats()">Go to your chats</button>
      <button class="w-full bg-blue-600 text-white rounded-lg py-2 font-semibold mb-2">Edit Listing</button>
      <button class="w-full bg-blue-600 text-white rounded-lg py-2 font-semibold">View Sale Listing Metrics</button>
    </div>
  `
})
export class PreviewListingSellerDialogComponent {
  private router = inject(Router);
  private dialogRef = inject(MatDialogRef<PreviewListingSellerDialogComponent>);
  public data = inject(MAT_DIALOG_DATA);

  goToChats() {
    this.router.navigate(['/buyer-seller/seller-preview'],{
      queryParams:{listingId: this.data?._id}
    });
    this.close(); // Optional: close modal after navigation
  }

  close() {
    this.dialogRef.close();
  }
}
