import { fromEvent, merge } from 'rxjs';

export const KeyPower = {
  NONE: 0,
  CTRL: 2 ** 1,
  SHIFT: 2 ** 2,
  ALT: 2 ** 3,
  META: 2 ** 4,
};

export const calculateKeyPower = (e: KeyboardEvent) => {
  const { CTRL, SHIFT, ALT, META } = KeyPower;
  let power = 0;
  if (e.ctrlKey) power |= CTRL;
  if (e.shiftKey) power |= SHIFT;
  if (e.altKey) power |= ALT;
  if (e.metaKey) power |= META;
  return power;
};

export const prevent = <T extends Event>(e: T) => {
  e.preventDefault();
  e.stopPropagation();
};

export const pointerMove$ = fromEvent<MouseEvent>(document, 'pointermove');
export const pointerUp$ = merge(
  fromEvent<MouseEvent>(document, 'pointerup'),
  fromEvent<MouseEvent>(document, 'pointercancel'),
);
