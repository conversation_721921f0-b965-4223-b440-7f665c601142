import type { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { ChangeDetectionStrategy, Component, Injectable, inject, viewChild } from '@angular/core';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { DomSanitizer } from '@angular/platform-browser';
import type { Subscription } from 'rxjs';
import { firstValueFrom } from 'rxjs';
import { EnsureSingleInstance } from '../common/test-root-service';

@Injectable({ providedIn: 'root' })
export class MonacoService {
  readonly #dialog = inject(MatDialog);

  constructor() {
    EnsureSingleInstance(this);
  }

  open(
    opt: { value?: string | null; innerClass?: string; settings?: any } = {},
    dOpt: any = {},
  ): any {
    const ref = this.#dialog.open<MonacoComponent, any, void>(MonacoComponent, {
      data: opt,
      disableClose: true,
      ...dOpt,
    });
    return firstValueFrom(ref.afterClosed());
  }
}

@Component({
  selector: 'app-monaco',
  imports: [MatDialogModule],
  template: `
    <mat-dialog-content class="!flex flex-col gap-3 h-full !max-h-max !p-0 !m-auto">
      <iframe class="h-[878px] w-[970px]" #ref [src]="src"></iframe>
      <div class="flex flex-row justify-end gap-3">
        <button class="min-w-16 btn btn-error" mat-dialog-close>Cancel</button>
        <button class="min-w-16 btn btn-primary" (click)="submit()">Submit</button>
      </div>
    </mat-dialog-content>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MonacoComponent implements OnInit, OnDestroy {
  readonly ref = viewChild.required<ElementRef>('ref');
  readonly #dialogRef: MatDialogRef<MonacoComponent> = inject(MatDialogRef);
  readonly #sanitizer = inject(DomSanitizer);
  public data: any = inject(MAT_DIALOG_DATA);
  public iframeWindow!: any;
  public src: any;
  readonly #subscriptions: Record<string, Subscription> = {};

  constructor() {
    const url = new URL('/assets/no-cache/monaco-editor.html', location.href);
    const setting = {
      language: 'javascript',
      theme: 'vs-dark',
      fontSize: 18,
      wordWrap: 'on',
      ...(this.data.settings || {}),
      value: this.data.value || this.data.settings?.value || '',
    };
    url.searchParams.set('settings', JSON.stringify(setting));
    this.src = this.#sanitizer.bypassSecurityTrustResourceUrl(url.href);
  }

  ngOnInit() {
    this.iframeWindow = this.ref().nativeElement.contentWindow;
  }

  public submit() {
    const code = this.iframeWindow.editor.getValue();
    this.#dialogRef.close({ action: true, value: code });
  }

  public setValue(value: string) {
    this.iframeWindow.editor.setValue(value);
  }

  public ngOnDestroy(): void {
    for (const sub of Object.values(this.#subscriptions)) sub.unsubscribe();
    (this as any).#subscriptions = undefined;
  }
}
