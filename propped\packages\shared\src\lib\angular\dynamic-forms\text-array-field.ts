import type { BooleanInput } from '@angular/cdk/coercion';
import { coerceBooleanProperty } from '@angular/cdk/coercion';
import type { <PERSON><PERSON><PERSON><PERSON>, <PERSON>D<PERSON>roy } from '@angular/core';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  HostBinding,
  Input,
  ViewEncapsulation,
  inject,
} from '@angular/core';
import type { AbstractControl, ControlValueAccessor } from '@angular/forms';
import { FormsModule, NgControl, ReactiveFormsModule } from '@angular/forms';
import type { MatFormField } from '@angular/material/form-field';
import { MAT_FORM_FIELD, MatFormFieldControl } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { Subject } from 'rxjs';
import { isNil } from '../../common/fun';

@Component({
  selector: 'silver-text-array',
  imports: [FormsModule, ReactiveFormsModule, MatIconModule, MatInputModule],
  providers: [{ provide: MatFormFieldControl, useExisting: SilverTextArrayComponent }],
  template: `
    <div
      [attr.aria-labelledby]="_formField?.getLabelId?.()"
      (focusin)="onFocusIn($event)"
      (focusout)="onFocusOut($event)"
    >
      <div class="max-h-[200px] overflow-y-auto flex flex-wrap gap-2">
        @for (keyword of _value; track keyword; let index = $index) {
          <span class="p-1 rounded-[40px] bg-base-100 text-base-content">
            {{ keyword }}
            <mat-icon class="!h-4 !w-4 text-[16px] leading-[18px]" (click)="remove(index)"
              >cancel</mat-icon
            >
          </span>
        }
      </div>
      <input matInput placeholder="add chips..." type="text" (keyup.enter)="add($event)" />
    </div>
  `,
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SilverTextArrayComponent
  implements ControlValueAccessor, MatFormFieldControl<any[]>, OnDestroy, DoCheck
{
  static nextId = 0;
  readonly #elementRef = inject(ElementRef);
  readonly ngControl = inject(NgControl, { self: true, optional: true });
  readonly #cdRef = inject(ChangeDetectorRef);
  readonly _formField: MatFormField | null = inject(MAT_FORM_FIELD, { optional: true });
  stateChanges = new Subject<void>();
  focused = false;
  touched = false;
  controlType = 'silver-text-array';
  @HostBinding() id = `silver-text-array-${SilverTextArrayComponent.nextId++}`;

  onChange = (_: any) => {
    //
  };
  onTouched = () => {
    //
  };

  private _options: any;
  _lastResult = { selected: [] as any[], selectedObj: [] as any[] };
  _value: string[] | null = null;
  private _previousControl: AbstractControl<any, any> | null = null;

  get empty() {
    return !this._value?.length;
  }

  @HostBinding('class.floating')
  get shouldLabelFloat() {
    return this.focused || !this.empty;
  }

  // eslint-disable-next-line @angular-eslint/no-input-rename
  @Input('aria-describedby') userAriaDescribedBy!: string;

  @Input()
  get placeholder(): string {
    return this._placeholder;
  }

  set placeholder(value: string) {
    this._placeholder = value;
    this.stateChanges.next();
  }

  @Input()
  get options(): any {
    return this._options;
  }

  set options(value: any) {
    this._options = value;
    this.stateChanges.next();
  }

  private _placeholder!: string;

  @Input()
  get required(): boolean {
    return this._required;
  }

  set required(value: BooleanInput) {
    this._required = coerceBooleanProperty(value);
    this.stateChanges.next();
  }

  private _required = false;

  @Input()
  get disabled(): boolean {
    return this._disabled;
  }
  set disabled(value: BooleanInput) {
    this._disabled = coerceBooleanProperty(value);
    this.stateChanges.next();
  }

  private _disabled = false;

  @Input()
  get value(): string[] | null {
    return /* this._required &&  */ !this._value?.length ? null : this._value;
  }

  set value(val: string[] | null) {
    this._value = !isNil(val) && Array.isArray(val) ? [...val] : null;
    this.#cdRef.detectChanges();
  }

  get errorState(): boolean {
    return this.touched && this.empty && (this.ngControl?.invalid ?? false);
  }

  constructor() {
    if (this.ngControl != null) {
      this.ngControl.valueAccessor = this;
    }
  }

  remove(ix: number) {
    this._value?.splice(ix, 1);
    this.onChange(this._value);
    this.stateChanges.next();
  }

  add($event: any) {
    const target = $event.target as HTMLInputElement;
    const value = target.value.trim();
    if (!value) return;
    this._value ??= [];
    this._value.push(value);
    target.value = '';
    this.onChange(this._value);
    this.stateChanges.next();
  }

  focus() {
    this.focused = true;
    this.stateChanges.next();
  }

  focusOut() {
    this.focused = false;
    this.stateChanges.next();
  }

  onFocusIn(_event: FocusEvent) {
    if (!this.focused) {
      this.focused = true;
      this.stateChanges.next();
    }
  }

  onFocusOut(event: FocusEvent) {
    if (!this.#elementRef.nativeElement.contains(event.relatedTarget as Element)) {
      this.touched = true;
      this.focused = false;
      this.onTouched();
      this.stateChanges.next();
    }
  }

  setDescribedByIds(_ids: string[]) {
    //
  }

  onContainerClick() {
    this.focus();
  }

  writeValue(value: string[] | null): void {
    this.value = value;
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  ngDoCheck() {
    const ngControl = this.ngControl;
    if (!ngControl) return;
    const touched = !!ngControl.touched;
    if (touched !== this.touched) {
      this.touched = touched;
      this.stateChanges.next();
    }
    if (this._previousControl !== ngControl.control) {
      if (
        this._previousControl !== undefined &&
        ngControl.disabled !== null &&
        ngControl.disabled !== this.disabled
      ) {
        this.disabled = ngControl.disabled;
      }
      this._previousControl = ngControl.control;
    }
  }

  ngOnDestroy() {
    this.stateChanges.complete();
  }
}
