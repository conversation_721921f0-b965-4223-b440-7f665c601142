const updateAuthDatabase = require('../config/updateAuthDB');

/**
 * <PERSON><PERSON><PERSON> to run the database update for the new authentication system
 */
const runUpdate = async () => {
  try {
    console.log('🚀 Starting database update for new authentication system...');
    await updateAuthDatabase();
    console.log('✅ Database update completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Database update failed:', error);
    process.exit(1);
  }
};

// Run the update
runUpdate();
