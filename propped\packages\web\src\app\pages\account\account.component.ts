import type { OnInit } from '@angular/core';
import { Component, inject } from '@angular/core';
import { FormControl, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import type { MatDialogRef } from '@angular/material/dialog';
import { MatDialog } from '@angular/material/dialog';
import { apiRPC, injectController } from '@api/rpc';
import type { ActiveUser } from '@lib/angular/auth/models/active-user.model';
import { SnackBarService } from '@lib/angular/snack-bar.service';
import { firstValueFrom } from 'rxjs';
import { AuthService } from '../../services/auth.service';
import { generateSecret, generateToken } from './totp';

@Component({
  imports: [FormsModule, ReactiveFormsModule],
  selector: 'app-account',
  templateUrl: './account.component.html',
})
export class AccountComponent implements OnInit {
  readonly #authService = inject(AuthService);
  readonly #matDialog = inject(MatDialog);
  readonly #snackBarService = inject(SnackBarService);

  readonly #authController = injectController(apiRPC.AuthController);

  private dialogRef: MatDialogRef<any>;
  private loginUser: ActiveUser;
  public status: { TFARequire: boolean; TOTPActive: boolean } = {} as any;

  public totpFormData: any = {
    form: new FormControl('', [
      Validators.required,
      Validators.minLength(6),
      Validators.maxLength(6),
    ]),
  };

  public ngOnInit() {
    this.loginUser = this.#authService.getLoginUserSync();
    this.getStatus();
  }

  public async getStatus() {
    const result = await this.#authController.getTFAStatus();
    this.status = result.data;
  }

  public async generateSecret(template) {
    this.totpFormData.data = await generateSecret(this.loginUser.username);
    this.totpFormData.form.reset();
    // const token = generateToken(this.totpFormData.data.secret);
    // this.totpFormData.otp = interval(3 * 1000).pipe(
    //   startWith({}),
    //   map(() => token.generate())
    // );
    const dialog = this.#matDialog.open(template, { width: '360px' });
    this.dialogRef = dialog;
    const result = await firstValueFrom(dialog.afterClosed());
    if (result) this.getStatus();
  }

  public async setTFAEnable(enable: boolean) {
    await this.#authController.setTFA({ enable });
    this.getStatus();
  }

  public async removeTOTP() {
    await this.#authController.removeTOTP();
    this.getStatus();
  }

  public async saveOTPSecret() {
    const { secret } = this.totpFormData.data;
    const token = generateToken(secret);
    const x = { token: `${this.totpFormData.form.value}`, window: 1 };
    const match = token.validate(x) !== null;
    if (!match) {
      this.#snackBarService.error('Token not match');
      return;
    }
    await this.#authController.setTOTPSecretTFA({ secret });
    this.#snackBarService.success('Setup Complete!');
    this.dialogRef.close(true);
  }
}
