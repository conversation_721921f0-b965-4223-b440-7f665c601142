import { Auth } from '../../guards/auth.guard';
import { Compress } from '../../receptor/compression.receptor';
import { type IMetroStation, MetroStationModel } from './metro.model';

@Auth()
export class MetroController {
  @Compress()
  async getMetroStationsWithinRadius({
    latitude,
    longitude,
    radius,
  }: {
    latitude: number;
    longitude: number;
    radius: number;
  }) {
    const metroStations = await MetroStationModel.find({
      location: {
        $near: {
          $geometry: { type: 'Point', coordinates: [longitude, latitude] },
          $maxDistance: radius,
        },
      },
    })
      .select('alphaNumericCode stationName roadName buildingName postal location')
      .lean<IMetroStation[]>();
    return {
      success: true,
      center: { latitude, longitude },
      metroStations,
      count: metroStations.length,
    };
  }
}
