import { Schema, model, type Document, type Model } from 'mongoose';

// Interface for the document
export interface ICondoTransactionDump extends Document {
  contract_date: string;
  project: string;
  district: string;
  street: string;
  market_segment: string;
  tenure: string;
  floor_range: string;
  type_of_sale: number;
  area_sqm: number;
  type_of_area: string;
  price: number;
  property_type: string;
  district_num: number;
  location: {
    type: 'Point';
    coordinates: [number, number] | null;
  };
}

// Define the schema
const CondoTransactionDumpSchema = new Schema<ICondoTransactionDump>({
  contract_date: { type: String, required: true }, // Format: YYYY-MM
  project: { type: String, required: true },
  district: { type: String, required: true },
  street: { type: String, required: true },
  market_segment: { type: String, required: true },
  tenure: { type: String, required: true },
  floor_range: { type: String, required: true },
  type_of_sale: { type: Number, required: true },
  area_sqm: { type: Number, required: true },
  type_of_area: { type: String, required: true },
  price: { type: Number, required: true },
  property_type: { type: String, required: true },
  district_num: { type: Number, required: true },
  location: {
    type: {
      type: String,
      enum: ['Point'],
      default: 'Point',
    },
    coordinates: {
      type: [Number],
      default: null,
    },
  },
});

// Create 2dsphere index for geospatial queries
CondoTransactionDumpSchema.index({ location: '2dsphere' });

// Export the model
export const CondoTransactionDump: Model<ICondoTransactionDump> = model<ICondoTransactionDump>(
  'CondoTransactionDump',
  CondoTransactionDumpSchema,
);
