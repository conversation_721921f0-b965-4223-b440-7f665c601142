import { Auth } from '../../guards/auth.guard';
import { Compress } from '../../receptor/compression.receptor';
import { type IHospital, HospitalModel } from './hospitals.model';

@Auth()
export class HospitalController {
  @Compress()
  async getHospitalsWithinRadius({
    latitude,
    longitude,
    radius,
  }: {
    latitude: number;
    longitude: number;
    radius: number;
  }) {
    const hospitals = await HospitalModel.find({
      location: {
        $near: {
          $geometry: { type: 'Point', coordinates: [longitude, latitude] },
          $maxDistance: radius,
        },
      },
    })
      .select('blkNo roadName buildingName address postal location')
      .lean<IHospital[]>();
    return {
      success: true,
      center: { latitude, longitude },
      hospitals,
      count: hospitals.length,
    };
  }
}
