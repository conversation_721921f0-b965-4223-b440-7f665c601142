const jwt = require('jsonwebtoken');
const speakeasy = require('speakeasy');
const QRCode = require('qrcode');
const crypto = require('crypto');
const pool = require('../config/connection');

const SECRET_KEY = process.env.SECRET_KEY;
const APP_NAME = process.env.APP_NAME || 'Zync App';

/**
 * Generate a unique 5-digit OTP
 * @returns {Promise<string>} 5-digit OTP
 */
const generateUniqueOTP = async () => {
  let otp;
  let isUnique = false;
  let attempts = 0;
  const maxAttempts = 10;

  while (!isUnique && attempts < maxAttempts) {
    otp = Math.floor(10000 + Math.random() * 90000).toString(); // 5-digit OTP
    
    // Check if OTP exists in userTokenTable
    const [rows] = await pool.query(
      "SELECT email FROM userTokenTable WHERE token = ?", 
      [otp]
    );
    
    if (rows.length === 0) {
      isUnique = true;
    }
    attempts++;
  }
  
  if (!isUnique) {
    throw new Error('Unable to generate unique OTP after maximum attempts');
  }
  
  return otp;
};

/**
 * Store OTP in userTokenTable with expiration
 * @param {string} email - User email
 * @param {string} otp - Generated OTP
 * @returns {Promise<void>}
 */
const storeOTP = async (email, otp) => {
  await pool.query(
    `INSERT INTO userTokenTable (email, token, updatedAt) 
     VALUES (?, ?, NOW()) 
     ON DUPLICATE KEY UPDATE token = VALUES(token), updatedAt = NOW()`,
    [email, otp]
  );
};

/**
 * Verify OTP from userTokenTable
 * @param {string} email - User email
 * @param {string} otp - OTP to verify
 * @param {number} expiryMinutes - OTP expiry time in minutes (default: 10)
 * @returns {Promise<boolean>} True if OTP is valid
 */
const verifyOTP = async (email, otp, expiryMinutes = 10) => {
  const [rows] = await pool.query(
    `SELECT token, updatedAt FROM userTokenTable 
     WHERE email = ? AND token = ? 
     AND updatedAt > DATE_SUB(NOW(), INTERVAL ? MINUTE)`,
    [email, otp, expiryMinutes]
  );
  
  return rows.length > 0;
};

/**
 * Delete OTP from userTokenTable
 * @param {string} email - User email
 * @returns {Promise<void>}
 */
const deleteOTP = async (email) => {
  await pool.query("DELETE FROM userTokenTable WHERE email = ?", [email]);
};

/**
 * Generate JWT token with different expiration times based on device
 * @param {Object} payload - Token payload
 * @param {string} deviceType - Device type: 'web', 'android', 'ios'
 * @returns {Object} Token and expiration info
 */
const generateJWTToken = (payload, deviceType = 'web') => {
  const expirationTimes = {
    web: '8h',
    android: '30d',
    ios: '30d'
  };
  
  const expiresIn = expirationTimes[deviceType] || expirationTimes.web;
  
  const token = jwt.sign(payload, SECRET_KEY, { expiresIn });
  
  // Calculate expiration date
  const expiryDate = new Date();
  if (deviceType === 'web') {
    expiryDate.setHours(expiryDate.getHours() + 8);
  } else {
    expiryDate.setDate(expiryDate.getDate() + 30);
  }
  
  return {
    token,
    expiresIn,
    expiryDate: expiryDate.toISOString().slice(0, 19).replace('T', ' ')
  };
};

/**
 * Generate TOTP secret and QR code
 * @param {string} username - User's username
 * @param {string} email - User's email
 * @returns {Promise<Object>} Secret, QR code URL, and manual entry key
 */
const generateTOTPSecret = async (username, email) => {
  const secret = speakeasy.generateSecret({
    name: `${APP_NAME} (${username})`,
    account: email,
    issuer: APP_NAME,
    length: 32
  });
  
  // Generate QR code
  const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url);
  
  return {
    secret: secret.base32,
    qrCode: qrCodeUrl,
    manualEntryKey: secret.base32,
    otpauth_url: secret.otpauth_url
  };
};

/**
 * Verify TOTP token
 * @param {string} token - 6-digit TOTP token
 * @param {string} secret - User's TOTP secret
 * @param {number} window - Time window for verification (default: 2)
 * @returns {boolean} True if token is valid
 */
const verifyTOTPToken = (token, secret, window = 2) => {
  return speakeasy.totp.verify({
    secret: secret,
    encoding: 'base32',
    token: token,
    window: window
  });
};

/**
 * Encrypt sensitive data
 * @param {string} text - Text to encrypt
 * @returns {string} Encrypted text
 */
const encrypt = (text) => {
  const algorithm = 'aes-256-cbc';
  const key = crypto.scryptSync(SECRET_KEY, 'salt', 32);
  const iv = crypto.randomBytes(16);

  const cipher = crypto.createCipheriv(algorithm, key, iv);
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');

  return iv.toString('hex') + ':' + encrypted;
};

/**
 * Decrypt sensitive data
 * @param {string} encryptedText - Encrypted text
 * @returns {string} Decrypted text
 */
const decrypt = (encryptedText) => {
  const algorithm = 'aes-256-cbc';
  const key = crypto.scryptSync(SECRET_KEY, 'salt', 32);

  const textParts = encryptedText.split(':');
  const iv = Buffer.from(textParts.shift(), 'hex');
  const encrypted = textParts.join(':');

  const decipher = crypto.createDecipheriv(algorithm, key, iv);
  let decrypted = decipher.update(encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');

  return decrypted;
};

/**
 * Validate email format
 * @param {string} email - Email to validate
 * @returns {boolean} True if email is valid
 */
const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate username format
 * @param {string} username - Username to validate
 * @returns {boolean} True if username is valid
 */
const isValidUsername = (username) => {
  // Username: 3-30 characters, alphanumeric and underscore only
  const usernameRegex = /^[a-zA-Z0-9_]{3,30}$/;
  return usernameRegex.test(username);
};

/**
 * Validate password strength
 * @param {string} password - Password to validate
 * @returns {Object} Validation result with isValid and errors
 */
const validatePassword = (password) => {
  const errors = [];
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  
  if (!/(?=.*[a-z])/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/(?=.*[A-Z])/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/(?=.*\d)/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  if (!/(?=.*[@$!%*?&])/.test(password)) {
    errors.push('Password must contain at least one special character (@$!%*?&)');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

module.exports = {
  generateUniqueOTP,
  storeOTP,
  verifyOTP,
  deleteOTP,
  generateJWTToken,
  generateTOTPSecret,
  verifyTOTPToken,
  encrypt,
  decrypt,
  isValidEmail,
  isValidUsername,
  validatePassword
};
