import { BehaviorSubject } from 'rxjs';

export let CURRENT_THEME =
  localStorage.getItem('app#theme') ||
  (matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');

export const THEME_CONTROLLER = new BehaviorSubject(CURRENT_THEME);

THEME_CONTROLLER.subscribe((v: string) => {
  CURRENT_THEME = v;
  localStorage.setItem('app#theme', v);
  // document.querySelector('html')!.dataset['theme'] = v === 'dark' ? 'synthwave' : 'autumn';
  document.querySelector('html')!.dataset['theme'] = v === 'dark' ? 'dark' : 'light';
});
