import { Component, inject, type On<PERSON><PERSON><PERSON>, type OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { type ICondo } from '@api/controllers/condo/condo.model';
import { type IHDBData } from '@api/controllers/hdb/hdb.model';
import { type IKinderGartenSchool } from '@api/controllers/schools/kindergarten-school.model';
import { apiRPC, injectController } from '@api/rpc';
import { DomInjectorComponent } from '@lib/angular/dom-injector.component';
import { FormModalService } from '@lib/angular/dynamic-forms/form-modal.service';
import {
  type SilverField,
  SilverFieldTypes,
} from '@lib/angular/dynamic-forms/silver-field.component';
import { SnackBarService } from '@lib/angular/snack-bar.service';
import { THEME_CONTROLLER } from '@lib/angular/theming.service';
import { html } from '@lib/common/jsx-dom';
import { daisyMerge } from '@lib/common/tailwind-merge';
import type { Subscription } from 'rxjs';
import { DualChartViewerService } from '../../component/data-chart/dual-data-chart.component';
import { TableViewerService } from '../../component/data-chart/stacked-table.component';
import { MapsService } from '../../services/maps.service';
import { HDBSalesService } from '../hdb-transactions/hdb-transaction.service';
import { BlockDetailsModalService } from './property-modal.service';
import { SalesServiceModal } from './sales-listing.service';

interface Property {
  blockNo: string;
  street: string;
  town: string;
  postalCode: string;
  latitude: number;
  longitude: number;
  salesPrice: number;
  totalFloors?: number;
  unitsPerFloor?: number;
  totalUnits?: number;
  ethnicQuota?: string;
  leaseStart?: string;
  projectName?: string;
}

const singaporeCenter = { lat: 1.3521, lng: 103.8198 };

@Component({
  selector: 'app-overview',
  templateUrl: './overview.component.html',
  standalone: true,
  imports: [MatIconModule, DomInjectorComponent, FormsModule],
})
export class OverviewComponent implements OnInit, OnDestroy {
  readonly #blockDetailsModalService = inject(BlockDetailsModalService);
  readonly #mapsService = inject(MapsService);
  readonly #salesServiceModal = inject(SalesServiceModal);
  readonly #snackBarService = inject(SnackBarService);
  readonly #tableViewerService = inject(TableViewerService);
  readonly #salesService = inject(HDBSalesService);
  // readonly #chartViewerService = inject(ChartViewerService);
  readonly #dualChartViewerService = inject(DualChartViewerService);
  readonly #formModalService = inject(FormModalService);

  public daisyMerge = daisyMerge;

  readonly #busStopsController = injectController(apiRPC.BusStopsController);
  readonly #condoController = injectController(apiRPC.CondoController);
  readonly #hawkerController = injectController(apiRPC.HawkerController);
  readonly #hdbController = injectController(apiRPC.HDBController);
  readonly #schoolController = injectController(apiRPC.SchoolController);
  readonly #oneMapSearchController = injectController(apiRPC.OneMapSearchController);
  readonly #metroController = injectController(apiRPC.MetroController);
  readonly #hospitalController = injectController(apiRPC.HospitalController);

  public activeTab: 'HDB' | 'Condo' = 'HDB';
  public showHDBListings = false;
  public showHDBData = false;
  private googleMap: google.maps.Map;
  private markers: google.maps.Marker[] = [];
  public showCondoData = false;
  public showCondoListings = false;

  // Properties for hawker centers
  public hawkerCenterMarkers: google.maps.Marker[] = [];
  public isLoadingHawkerCenters = false;
  private hawkerCenterRadiusCircles: google.maps.Circle[] = [];
  private hawkerCenters: any[] = [];
  private selectedPropertyMarker: google.maps.Marker | null = null;

  // Properties for bus stops
  public busStopMarkers: google.maps.Marker[] = [];
  public isLoadingBusStops = false;
  private busStopRadiusCircles: google.maps.Circle[] = [];
  public setSearchResultMarker = false;

  // User location properties
  public userLocationMarker: google.maps.Marker | null = null;
  public userLocationCircle: google.maps.Circle | null = null;
  public selectedRadius = 2; // Default radius in km
  public isLoadingUserLocation = false;
  public showUserLocationInfoWindow = false;
  public userLocationInfoWindowPosition = { x: 0, y: 0 };
  public userLocationCoordinates: { lat: number; lng: number } | null = null;
  public isLoadingNearbyProperties = false;
  public nearbyPropertiesMarkers: google.maps.Marker[] = [];
  public propertiesShown = false; // Flag to track if properties have been shown
  public hdbTowns: string[] = [];
  public selectedTowns: Set<string> = new Set();
  public showDropdown = false; // Changed to false initially
  public townColors: { [key: string]: string } = {};
  public recentSearches: any[] = [];
  public transactionMedianPrices: any[][] = [];
  public transactionAveragePSF: any[][] = [];
  public hospitalMarkers: google.maps.Marker[] = [];
  public metroMarkers: google.maps.Marker[] = [];
  public schoolMarkers: google.maps.Marker[] = [];

  // Filter properties
  public filter: any = {};
  public filteredPropertyData: Property[] = [];
  public foodIconUrl = 'https://maps.google.com/mapfiles/kml/shapes/dining.png';
  public hospitalIconUrl = 'https://maps.google.com/mapfiles/kml/shapes/hospitals.png';
  public metroIconUrl = 'https://maps.google.com/mapfiles/kml/shapes/rail.png';

  toggleDropdown() {
    this.showDropdown = !this.showDropdown;

    // Assign colors to towns when opening dropdown if not already assigned
    if (Object.keys(this.townColors).length === 0 && this.hdbTowns.length > 0) {
      this.assignColorsToTowns();
    }
  }

  async openFilterForm() {
    const form: SilverField[] = [
      {
        controlType: SilverFieldTypes.SELECT,
        label: 'Town',
        key: 'town',
        value: null,
        nullable: true,
        valid: { required: false },
        multiple: true,
        options: this.hdbTowns,
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Minimum Price',
        placeholder: 'Minimum Price',
        type: 'number',
        key: 'minimumPrice',
        value: '',
        valid: { required: false },
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Maximum Price',
        placeholder: 'Maximum Price',
        type: 'number',
        key: 'maximumPrice',
        value: '',
        valid: { required: false },
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Postal Code',
        placeholder: 'Postal Code',
        type: 'text',
        key: 'postalCode',
        value: '',
        valid: { required: false },
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Street',
        placeholder: 'Street Name',
        type: 'text',
        key: 'street',
        value: '',
        valid: { required: false },
      },
    ];

    const result = await this.#formModalService.open({ heading: 'Filter Properties', form });
    if (!result.action) return;

    this.filter = result.value;
    this.applyFilters();
  }

  applyFilters() {
    // Start with all properties
    let filtered = [...this.propertyData];

    // Apply town filter
    if (this.filter.town?.length) {
      filtered = filtered.filter((property) => this.filter.town.includes(property.town));
    }

    // Apply minimum price filter
    if (this.filter.minimumPrice) {
      const minPrice = this.filter.minimumPrice;
      filtered = filtered.filter((property) => property.salesPrice >= minPrice);
    }

    // Apply maximum price filter
    if (this.filter.maximumPrice) {
      const maxPrice = Number(this.filter.maximumPrice);
      filtered = filtered.filter((property) => property.salesPrice <= maxPrice);
    }

    // Apply postal code filter
    if (this.filter.postalCode) {
      filtered = filtered.filter((property) =>
        property.postalCode.includes(this.filter.postalCode),
      );
    }

    // Apply street filter
    if (this.filter.street) {
      filtered = filtered.filter((property) =>
        property.street.toLowerCase().includes(this.filter.street.toLowerCase()),
      );
    }

    this.filteredPropertyData = filtered;

    // Update map markers to reflect filtered data
    if (this.showHDBListings || this.showCondoListings) {
      this.clearMarkers();
      this.initializeMap();
    }
  }

  private assignColorsToTowns(): void {
    this.townColors = {};
    this.hdbTowns.forEach((town, index) => {
      const townName = town[1]; // Town name is at index 1
      this.townColors[townName] = this.townColorPalette[index % this.townColorPalette.length];
    });
  }

  async toggleTown(townName: string) {
    if (this.selectedTowns.has(townName)) {
      this.selectedTowns.delete(townName);
      // If no towns are selected, show all HDB data
      if (this.selectedTowns.size === 0) {
        this.initializeHDBMap();
      } else {
        // Otherwise, refresh the map with the remaining selected towns
        await this.displayFilteredHDBProperties();
      }
    } else {
      this.selectedTowns.add(townName);
      await this.displayFilteredHDBProperties();
    }
  }

  isSelected(townName: string): boolean {
    return this.selectedTowns.has(townName);
  }

  // Predefined colors for towns
  private townColorPalette = [
    '#4285F4', // Google Blue
    '#EA4335', // Google Red
    '#FBBC05', // Google Yellow
    '#34A853', // Google Green
    '#8E24AA', // Purple
    '#16A085', // Turquoise
    '#F39C12', // Orange
    '#D35400', // Pumpkin
    '#C0392B', // Pomegranate
    '#2980B9', // Belize Hole
    '#27AE60', // Nephritis
    '#F1C40F', // Sunflower
    '#E74C3C', // Alizarin
    '#9B59B6', // Amethyst
    '#FB6D9D', // Pink
    '#95A5A6', // Concrete
    '#1ABC9C', // Aqua
    '#2ECC71', // Emerald
    '#3498DB', // Peter River
    '#9B27B0', // Deep Purple
    '#FF5722', // Deep Orange
    '#607D8B', // Blue Grey
    '#3F51B5', // Indigo
    '#795548', // Brown
  ];

  private mapElement: HTMLDivElement = html`<div class="w-full h-full"></div>`;

  // Dummy Property data for HDB sales listings
  public propertyData: Property[] = [
    {
      blockNo: '266C',
      street: 'Compassvale Bow',
      town: 'Sengkang',
      postalCode: '543266',
      latitude: 1.3827197,
      longitude: 103.8973621,
      salesPrice: 625000,
      totalFloors: 12,
      unitsPerFloor: 10,
      totalUnits: 120,
      ethnicQuota: '5%',
      leaseStart: '2025-01-01',
      projectName: 'Compassvale Bow',
    },
    {
      blockNo: '878',
      street: 'Yishun Street 81',
      town: 'Yishun',
      postalCode: '760878',
      latitude: 1.4140529,
      longitude: 103.8358877,
      salesPrice: 750000,
      totalFloors: 12,
      unitsPerFloor: 10,
      totalUnits: 120,
      ethnicQuota: '5%',
      leaseStart: '2025-01-01',
      projectName: 'Yishun Street 81',
    },
    {
      blockNo: '933',
      street: 'Tampines Street 91',
      town: 'Tampines',
      postalCode: '520933',
      latitude: 1.3452418,
      longitude: 103.9390987,
      salesPrice: 800000,
      totalFloors: 12,
      unitsPerFloor: 10,
      totalUnits: 120,
      ethnicQuota: '5%',
      leaseStart: '2025-01-01',
      projectName: 'Tampines Street 91',
    },
    {
      blockNo: '222A',
      street: 'Bedok North Drive',
      town: 'Bedok',
      postalCode: '461222',
      latitude: 1.3250047,
      longitude: 103.9277704,
      salesPrice: 900000,
      totalFloors: 12,
      unitsPerFloor: 10,
      totalUnits: 120,
      ethnicQuota: '5%',
      leaseStart: '2025-01-01',
      projectName: 'Bedok North Drive',
    },
    {
      blockNo: '988',
      street: 'Jurong West Street 93',
      town: 'Jurong',
      postalCode: '640988',
      latitude: 1.3361536,
      longitude: 103.6955012,
      salesPrice: 920000,
      totalFloors: 12,
      unitsPerFloor: 10,
      totalUnits: 120,
      ethnicQuota: '5%',
      leaseStart: '2025-01-01',
      projectName: 'Jurong West Street 93',
    },
    {
      blockNo: '20',
      street: "Saint George's Road",
      town: 'Kallang',
      postalCode: '321020',
      latitude: 1.3247397,
      longitude: 103.8628603,
      salesPrice: 545000,
      totalFloors: 12,
      unitsPerFloor: 10,
      totalUnits: 120,
      ethnicQuota: '5%',
      leaseStart: '2025-01-01',
      projectName: "Saint George's Road",
    },
    {
      blockNo: '48',
      street: 'Strathmore Avenue',
      town: 'Queenstown',
      postalCode: '140048',
      latitude: 1.2938027,
      longitude: 103.8077893,
      salesPrice: 700000,
      totalFloors: 12,
      unitsPerFloor: 10,
      totalUnits: 120,
      ethnicQuota: '5%',
      leaseStart: '2025-01-01',
      projectName: 'Strathmore Avenue',
    },
    {
      blockNo: '11',
      street: 'Holland Drive',
      town: 'Queenstown',
      postalCode: '271011',
      latitude: 1.309054,
      longitude: 103.7940632,
      salesPrice: 580000,
      totalFloors: 12,
      unitsPerFloor: 10,
      totalUnits: 120,
      ethnicQuota: '5%',
      leaseStart: '2025-01-01',
      projectName: 'Holland Drive',
    },
    {
      blockNo: '508',
      street: 'Ang Mo Kio Avenue 8',
      town: 'Ang Mo Kio',
      postalCode: '565508',
      latitude: 1.3737901,
      longitude: 103.8490929,
      salesPrice: 720000,
      totalFloors: 12,
      unitsPerFloor: 10,
      totalUnits: 120,
      ethnicQuota: '5%',
      leaseStart: '2025-01-01',
      projectName: 'Ang Mo Kio Avenue 8',
    },
    {
      blockNo: '442',
      street: 'Pasir Ris Drive 6',
      town: 'Pasir Ris',
      postalCode: '510442',
      latitude: 1.3689329,
      longitude: 103.9579518,
      salesPrice: 870000,
      totalFloors: 12,
      unitsPerFloor: 10,
      totalUnits: 120,
      ethnicQuota: '5%',
      leaseStart: '2025-01-01',
      projectName: 'Pasir Ris Drive 6',
    },
  ];

  // Dummy Property data for Condo sales listings
  public condoData: any[] = [
    {
      salesPrice: '750,000',
      listedDate: '17 Aug 2024 (12 Days Ago)',
      blockNo: 'Block 458',
      town: 'Sengkang',
      projectName: 'The Coris',
      street: 'Compassvale Bow',
      postalCode: '548265',
      leaseStart: 'Aug 2007',
      totalFloors: 17,
      unitsPerFloor: 4,
      totalUnits: 64,
      latitude: 1.2821,
      longitude: 103.8547,
      ethnicQuota:
        'You can buy from any flat seller, regardless of their ethnic group and citizenship',
      floorPlans: [
        'https://example.com/floorplan1.jpg', // Example URL
      ],
      actions: ['Show Past Transactions', 'Go To Listing'],
    },
    {
      salesPrice: '3,500,000',
      listedDate: '10 July 2024 (50 Days Ago)',
      blockNo: 'Tower 1',
      town: 'Marina Bay',
      projectName: 'Marina Boulevard Residences',
      street: 'Marina Boulevard',
      postalCode: '018980',
      leaseStart: '2010',
      totalFloors: 50,
      unitsPerFloor: 10,
      latitude: 1.2833,
      longitude: 103.7598,
      totalUnits: 500,
      ethnicQuota: 'No Ethnic Quota Restrictions',
      floorPlans: ['https://example.com/floorplan2.jpg'],
      actions: ['Show Past Transactions', 'Go To Listing'],
    },
    {
      salesPrice: '2,800,000',
      listedDate: '5 June 2024 (85 Days Ago)',
      blockNo: 'Block C',
      town: 'Queenstown',
      projectName: 'Alexandra Residences',
      street: 'Alexandra Road',
      postalCode: '159972',
      leaseStart: '2005',
      totalFloors: 35,
      latitude: 1.3099,
      longitude: 103.8088,
      unitsPerFloor: 6,
      totalUnits: 210,
      ethnicQuota: 'Available for all buyers',
      floorPlans: ['https://example.com/floorplan3.jpg'],
      actions: ['Show Past Transactions', 'Go To Listing'],
    },
    {
      salesPrice: '4,200,000',
      listedDate: '1 May 2024 (120 Days Ago)',
      blockNo: 'Tower 5',
      town: 'Bukit Timah',
      projectName: 'Farrer Residences',
      street: 'Farrer Road',
      postalCode: '268845',
      leaseStart: '2015',
      totalFloors: 45,
      unitsPerFloor: 8,
      totalUnits: 360,
      ethnicQuota: 'No restrictions',
      floorPlans: ['https://example.com/floorplan4.jpg'],
      actions: ['Show Past Transactions', 'Go To Listing'],
    },
  ];

  // Search properties
  public searchQuery = '';
  public searchResults: any[] = [];
  public searchResultMarker: google.maps.Marker | null = null;
  public searchResultCircle: google.maps.Circle | null = null;
  private searchDebounceTimer: any = null;
  private db: any = null;

  public hdbData: IHDBData[] = [];
  public isLoading = false;

  apiCondoData: ICondo[] = [];
  schoolData: IKinderGartenSchool[] = [];
  selectedSchoolType: string | null = null;
  isLoadingSchools = false;
  schoolMarker: any = null;

  // Properties for radius visualization
  radiusCircles: google.maps.Circle[] = [];
  nearbyCondoMarkers: google.maps.Marker[] = [];
  isLoadingNearbyCondos = false;

  // Property data to display in the info table
  public selectedProperty: any = null;
  public showPropertyTable = false;
  public propertyTablePosition = { x: 0, y: 0 };
  public transactionData: any[] = [];
  public hasTransactions = false;

  // Pagination and limit properties
  public paginationOptions = [10, 50, 100, 500, 1000];
  public currentHDBPage = 0;
  public currentCondoPage = 0;
  public selectedHDBLimit = 50; // Default limit
  public selectedCondoLimit = 50; // Default limit
  public totalHDBCount = 0;
  public totalCondoCount = 0;

  public Math = Math;

  async ngOnInit() {
    await this.#mapsService.loadGoogleMapsApi();
    const { Map: GoogleMap, MapTypeId } = google.maps;

    // Define dark mode map styles
    const darkModeMapStyles = [
      { elementType: 'geometry', stylers: [{ color: '#242f3e' }] },
      { elementType: 'labels.text.stroke', stylers: [{ color: '#242f3e' }] },
      { elementType: 'labels.text.fill', stylers: [{ color: '#746855' }] },
      {
        featureType: 'administrative.locality',
        elementType: 'labels.text.fill',
        stylers: [{ color: '#d59563' }],
      },
      {
        featureType: 'poi',
        elementType: 'labels.text.fill',
        stylers: [{ color: '#d59563' }],
      },
      {
        featureType: 'poi.park',
        elementType: 'geometry',
        stylers: [{ color: '#263c3f' }],
      },
      {
        featureType: 'poi.park',
        elementType: 'labels.text.fill',
        stylers: [{ color: '#6b9a76' }],
      },
      {
        featureType: 'road',
        elementType: 'geometry',
        stylers: [{ color: '#38414e' }],
      },
      {
        featureType: 'road',
        elementType: 'geometry.stroke',
        stylers: [{ color: '#212a37' }],
      },
      {
        featureType: 'road',
        elementType: 'labels.text.fill',
        stylers: [{ color: '#9ca5b3' }],
      },
      {
        featureType: 'road.highway',
        elementType: 'geometry',
        stylers: [{ color: '#746855' }],
      },
      {
        featureType: 'road.highway',
        elementType: 'geometry.stroke',
        stylers: [{ color: '#1f2835' }],
      },
      {
        featureType: 'road.highway',
        elementType: 'labels.text.fill',
        stylers: [{ color: '#f3d19c' }],
      },
      {
        featureType: 'transit',
        elementType: 'geometry',
        stylers: [{ color: '#2f3948' }],
      },
      {
        featureType: 'transit.station',
        elementType: 'labels.text.fill',
        stylers: [{ color: '#d59563' }],
      },
      {
        featureType: 'water',
        elementType: 'geometry',
        stylers: [{ color: '#17263c' }],
      },
      {
        featureType: 'water',
        elementType: 'labels.text.fill',
        stylers: [{ color: '#515c6d' }],
      },
      {
        featureType: 'water',
        elementType: 'labels.text.stroke',
        stylers: [{ color: '#17263c' }],
      },
    ];

    // Check if dark mode is enabled
    const isDarkMode =
      localStorage.getItem('app#theme') && localStorage.getItem('app#theme') === 'dark';
    // Create map options with conditional styling
    const mapOptions = {
      zoom: 11,
      center: singaporeCenter,
      mapTypeId: MapTypeId.ROADMAP,
      // Apply dark mode styles if dark mode is enabled
      styles: isDarkMode ? darkModeMapStyles : [],
    };

    // Initialize the map with options
    this.googleMap = new GoogleMap(this.mapElement, mapOptions);

    // Add listener for theme changes (if using a theme toggle)
    this.setupDarkModeListener(darkModeMapStyles);
    if (this.showHDBListings) this.initializeMap();
    else if (this.showHDBData) this.initializeHDBMap();
    this.db = await this.#salesService.loadDB();
    this.hdbTowns = await this.#salesService.getAllTowns(this.db);
    console.log('FETCHED HDB TOWNS', this.hdbTowns);
    if (localStorage.getItem('recentSearches')) {
      this.recentSearches = JSON.parse(localStorage.getItem('recentSearches') || '[]');
    }

    // Initialize filtered property data
    this.filteredPropertyData = [...this.propertyData];
  }

  ngOnDestroy(): void {
    console.log('adding recent searches to local storage');
    localStorage.setItem('recentSearches', JSON.stringify(this.recentSearches));

    // Remove any event listeners if needed
    if (this.darkModeMediaQuery) {
      this.darkModeMediaQuery.removeEventListener('change', this.handleDarkModeChange);
    }

    // Clean up theme subscription
    if (this.themeSubscription) {
      this.themeSubscription.unsubscribe();
      this.themeSubscription = null;
    }
  }

  // Dark mode media query reference
  private darkModeMediaQuery: MediaQueryList | null = null;

  // Event handler for dark mode changes
  private handleDarkModeChange: ((e: MediaQueryListEvent) => void) | null = null;

  // Theme subscription
  private themeSubscription: Subscription | null = null;

  /**
   * Sets up a listener for dark mode changes and updates the map style accordingly
   * @param darkModeStyles The styles to apply when in dark mode
   */
  private setupDarkModeListener(darkModeStyles: google.maps.MapTypeStyle[]): void {
    // Subscribe to theme changes from the THEME_CONTROLLER
    this.themeSubscription = THEME_CONTROLLER.subscribe((theme: string) => {
      if (this.googleMap) {
        // Apply dark styles if in dark mode, otherwise use default styles
        this.googleMap.setOptions({
          styles: theme === 'dark' ? darkModeStyles : [],
        });
      }
    });

    // Also keep the media query listener for system preference changes
    if (window.matchMedia) {
      this.darkModeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

      // Create the event handler for system preference changes
      this.handleDarkModeChange = (e: MediaQueryListEvent) => {
        // Only apply if there's no user preference stored in localStorage
        if (!localStorage.getItem('app#theme') && this.googleMap) {
          this.googleMap.setOptions({
            styles: e.matches ? darkModeStyles : [],
          });
        }
      };

      // Add the listener for system preference changes
      this.darkModeMediaQuery.addEventListener('change', this.handleDarkModeChange);
    }
  }

  /**
   * Toggle the active tab (HDB or Condo)
   * If the same tab is clicked again, it will be deselected
   */
  toggleActiveTab(tab: 'HDB' | 'Condo'): void {
    // If the same tab is clicked again, deselect it
    if (this.activeTab === tab) {
      this.activeTab = null;

      // Clear all markers related to this tab
      this.clearMarkers();

      // Reset display flags
      if (tab === 'HDB') {
        this.showHDBListings = false;
        this.showHDBData = false;
      } else {
        this.showCondoListings = false;
        this.showCondoData = false;
      }

      // Clear school-related markers when no active tab
      this.clearMapMarkers(this.schoolMarkers);

      console.log(`Deselected ${tab} tab`);
    } else {
      // Set the new active tab
      this.activeTab = tab;
      console.log(`Selected ${tab} tab`);
    }
  }

  async displayPropertyData(): Promise<void> {
    // If no tab is selected, do nothing
    if (!this.activeTab) return;

    if (this.userLocationMarker) {
      this.clearUserLocation();
    }

    if (this.activeTab === 'HDB') {
      this.showHDBData = !this.showHDBData;
      this.showHDBListings = false;
      this.showCondoData = false;
      this.showCondoListings = false;

      if (this.showHDBData) {
        this.isLoading = true;
        try {
          // Reset to first page when initially loading
          this.currentHDBPage = 0;
          const response = await this.#hdbController.getAll({
            page: this.currentHDBPage,
            limit: this.selectedHDBLimit,
          });
          this.hdbData = response.data;
          this.totalHDBCount = response.total;
          this.#snackBarService.success(
            `Loaded ${this.hdbData.length} of ${this.totalHDBCount} HDB properties`,
          );
          this.initializeHDBMap();
        } catch (error) {
          console.error('Error fetching HDB data:', error);
          this.#snackBarService.error('Error fetching HDB data');
        } finally {
          this.isLoading = false;
        }
      } else {
        this.clearMarkers();
      }
    } else if (this.activeTab === 'Condo') {
      this.showCondoData = !this.showCondoData;
      this.showHDBData = false;
      this.showHDBListings = false;
      this.showCondoListings = false;

      if (this.showCondoData) {
        this.isLoading = true;
        try {
          // Reset to first page when initially loading
          this.currentCondoPage = 0;
          const response = await this.#condoController.getAll({
            page: this.currentCondoPage,
            limit: this.selectedCondoLimit,
          });
          this.apiCondoData = response.data;
          this.totalCondoCount = response.total;
          this.#snackBarService.success(
            `Loaded ${this.apiCondoData.length} of ${this.totalCondoCount} condo properties`,
          );
          this.initializeCondoMap();
        } catch (error) {
          console.error('Error fetching Condo data:', error);
          this.#snackBarService.error('Error fetching Condo data');
        } finally {
          this.isLoading = false;
        }
      } else {
        this.clearMarkers();
      }
    }
  }

  displaySaleListings(): void {
    if (!this.activeTab) return;

    if (this.activeTab === 'HDB') {
      this.showHDBListings = !this.showHDBListings;
      this.showHDBData = false;
      this.showCondoData = false;
      this.showCondoListings = false;
      if (this.showHDBListings) this.initializeMap();
      else this.clearMarkers();
    } else if (this.activeTab === 'Condo') {
      this.showCondoListings = !this.showCondoListings;
      this.showHDBData = false;
      this.showHDBListings = false;
      this.showCondoData = false;
      if (this.showCondoListings) this.initializeMap();
      else this.clearMarkers();
    }
  }

  private initializeMap(): void {
    this.addMarkers();
  }

  private initializeHDBMap(): void {
    // this.addHDBMarkers();
  }

  private initializeCondoMap(): void {
    this.addCondoMarkers();
  }

  private addMarkers(): void {
    const { Marker } = google.maps;
    this.clearMarkers();
    console.log('Adding markers for properties...');
    // Add a marker for each property
    if (this.activeTab === 'HDB') {
      this.filteredPropertyData.forEach((property) => {
        console.log(`Adding marker for property at ${property.latitude}, ${property.longitude}`);
        const marker = new Marker({
          position: { lat: property.latitude, lng: property.longitude },
          map: this.googleMap,
          title: `${property.blockNo} ${property.street}, ${property.town}`,
          icon: {
            url: 'data:image/svg+xml;utf-8,<svg xmlns="http://www.w3.org/2000/svg" height="40" width="40" viewBox="0 0 24 24" fill="blue"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/></svg>',
            scaledSize: new google.maps.Size(30, 30),
          },
        });

        marker.addListener('click', (_: any) => {
          // Hide any previously shown property table
          this.showPropertyTable = false;

          // Set the selected property data
          this.selectedProperty = {
            date: new Date().toLocaleDateString(),
            flatType: '4-Room',
            floorLevel: '07-12',
            area: Math.round(Math.random() * 500 + 800),
            price: property.salesPrice,
            psf: Math.round(property.salesPrice / (Math.random() * 500 + 800)),
          };

          console.log('SELECTED PROPERTY', property);
          this.#salesServiceModal.open(property);

          this.propertyData = this.propertyData.filter((p) => p.blockNo !== property.blockNo);
          this.propertyData.unshift(property);

          // Calculate position for the table (next to the marker)
          const point = this.getPixelPositionFromLatLng(marker.getPosition()!);
          this.propertyTablePosition = {
            x: point.x + 20, // Offset to the right of the marker
            y: point.y - 100, // Offset above the marker
          };

          // Show the property table
          console.log('Showing property table', this.selectedProperty);
          this.showPropertyTable = true;
        });

        this.markers.push(marker);
      });
    } else {
      this.condoData.forEach((property) => {
        console.log(`Adding marker for property at ${property.latitude}, ${property.longitude}`);
        const marker = new Marker({
          position: { lat: property.latitude, lng: property.longitude },
          map: this.googleMap,
          title: `${property.blockNo} ${property.street}, ${property.town}`,
          icon: {
            url: 'data:image/svg+xml;utf-8,<svg xmlns="http://www.w3.org/2000/svg" height="40" width="40" viewBox="0 0 24 24" fill="blue"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/></svg>',
            scaledSize: new google.maps.Size(30, 30),
          },
        });

        marker.addListener('click', (_: any) => {
          // Hide any previously shown property table
          this.showPropertyTable = false;

          // Set the selected property data
          this.selectedProperty = {
            date: new Date().toLocaleDateString(),
            flatType: 'Condo',
            floorLevel: '10-15',
            area: Math.round(Math.random() * 800 + 1000),
            price: property.salesPrice,
            psf: Math.round(property.salesPrice / (Math.random() * 800 + 1000)),
          };

          this.propertyData = this.propertyData.filter((p) => p.blockNo !== property.blockNo);
          this.propertyData.unshift(property);

          this.#salesServiceModal.condoSalesOpen(property);
          // Calculate position for the table (next to the marker)
          const point = this.getPixelPositionFromLatLng(marker.getPosition()!);
          this.propertyTablePosition = {
            x: point.x + 20, // Offset to the right of the marker
            y: point.y - 100, // Offset above the marker
          };

          // Show the property table
          this.showPropertyTable = true;
        });

        this.markers.push(marker);
      });
    }
  }

  public generateMonthlyMedianPricesAndAvgPSF(data: any[]) {
    console.log('Generating monthly median prices and average PSF...', data);
    if (!data || data.length === 0) return;

    const pricesByYearMonth = new Map<string, number[]>();
    const psfByYearMonth = new Map<string, number[]>();

    data.forEach((row) => {
      const dateStr = row.date; // Format: 'YYYY-MM'
      if (!dateStr) return;

      const [year, month] = dateStr.split('-');
      const yearMonth = `${year}-${month}`;

      const price = Number(row.price);
      const psf = Number(row.psf);

      if (!pricesByYearMonth.has(yearMonth)) {
        pricesByYearMonth.set(yearMonth, []);
        psfByYearMonth.set(yearMonth, []);
      }

      pricesByYearMonth.get(yearMonth)!.push(price);
      psfByYearMonth.get(yearMonth)!.push(psf);
    });

    const result = Array.from(pricesByYearMonth.keys()).map((yearMonth) => {
      const prices = pricesByYearMonth.get(yearMonth)!;
      const psfs = psfByYearMonth.get(yearMonth)!;

      const sortedPrices = [...prices].sort((a, b) => a - b);
      const median =
        sortedPrices.length % 2 === 0
          ? (sortedPrices[sortedPrices.length / 2 - 1] + sortedPrices[sortedPrices.length / 2]) / 2
          : sortedPrices[Math.floor(sortedPrices.length / 2)];

      const avgPSF = psfs.reduce((sum, val) => sum + val, 0) / psfs.length;

      const [year, month] = yearMonth.split('-');
      const date = new Date(Number(year), Number(month) - 1);
      const formattedDate = date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
      });

      return {
        date: formattedDate,
        medianPrice: median,
        averagePSF: Math.round(avgPSF),
      };
    });

    const sortedResult = result.sort((a, b) => {
      const dateA = new Date(a.date);
      const dateB = new Date(b.date);
      return dateA.getTime() - dateB.getTime();
    });

    this.transactionMedianPrices = sortedResult.map(({ date, medianPrice }) => [date, medianPrice]);
    this.transactionAveragePSF = sortedResult.map(({ date, averagePSF }) => [date, averagePSF]);

    console.log('Monthly Median Prices:', this.transactionMedianPrices);
    console.log('Monthly Average PSF:', this.transactionAveragePSF);
  }

  private async showHDBTransactionTable(): Promise<void> {
    if (!this.transactionData || this.transactionData.length === 0) {
      this.#snackBarService.error('No transaction data available for property');
      return;
    }
    const tableData = {
      heading: 'Transaction Details',
      header: ['Date', 'Type', 'Floor Level', 'Area (SQFT)', 'Price (S$)', 'PSF (S$)'],
      data: this.transactionData.map((transaction) => Object.values(transaction).flat()),
    };

    await this.#tableViewerService.open(tableData).then((result) => {
      if (result?.showChart) {
        this.showHDBTransactionChart();
      }
    });
  }

  private showHDBTransactionChart() {
    this.generateMonthlyMedianPricesAndAvgPSF(this.transactionData);

    this.#dualChartViewerService.open({
      heading: 'HDB Transactions - Price & PSF Trend',
      header: ['Date', 'Median Price', 'Average PSF'],
      data: this.transactionMedianPrices.map((entry, index) => {
        const psfEntry = this.transactionAveragePSF[index];
        return [entry[0], entry[1], psfEntry[1]];
      }),
    });
  }

  private async showCondoTransactionTable(): Promise<void> {
    if (!this.transactionData || this.transactionData.length === 0) {
      this.#snackBarService.error('No transaction data available for property');
      return;
    }
    const tableData = {
      heading: 'Transaction Details',
      header: ['Date', 'FloorLevel', 'Area (SQFT)', 'Price (S$)', 'PSF (S$)'],
      data: this.transactionData.map((transaction) => Object.values(transaction).flat()),
    };

    await this.#tableViewerService.open(tableData).then((result) => {
      if (result?.showChart) {
        this.showCondoTransactionChart();
      }
    });
  }

  private showCondoTransactionChart() {
    console.log('FUNCTION TO SHOW Condo Transaction data chart called');

    this.generateMonthlyMedianPricesAndAvgPSF(this.transactionData);

    this.#dualChartViewerService.open({
      heading: 'Condo Transactions - Price & PSF Trend',
      header: ['Date', 'Median Price', 'Average PSF'],
      data: this.transactionMedianPrices.map((entry, index) => {
        const psfEntry = this.transactionAveragePSF[index];
        return [entry[0], entry[1], psfEntry[1]];
      }),
    });
  }

  private addCondoMarkers(): void {
    this.clearMarkers();
    console.log('Adding markers for condo data...');

    // Add a marker for each condo
    this.apiCondoData.forEach((condo) => {
      if (condo.location?.coordinates?.length === 2) {
        const [longitude, latitude] = condo.location.coordinates;

        const marker = new google.maps.Marker({
          position: { lat: latitude, lng: longitude },
          map: this.googleMap,
          title: condo.projectName,
          icon: {
            path: google.maps.SymbolPath.CIRCLE,
            fillColor: '#FF0000',
            fillOpacity: 1,
            strokeWeight: 1,
            scale: 10,
          },
        });

        // Add click listener to show property details table
        marker.addListener('click', async (_: any) => {
          // Hide any previously shown property table
          this.showPropertyTable = false;
          this.hasTransactions = false;
          this.transactionData = [];

          // Set the selected property data with default values
          this.selectedProperty = {
            date: new Date().toLocaleDateString(),
            flatType: 'Condo', // Default property type
          };

          const { data } = await this.#condoController.getById({ id: condo._id });
          console.log('CONDO POP UP MODAL DATA', data);
          this.toggleCondoPropertyModal(data);

          try {
            // Call the getLatestTransactionsByLocation controller function
            const response = await this.#condoController.getLatestTransactionsByLocation({
              lat: latitude.toString(),
              long: longitude.toString(),
            });
            console.log('Transaction response:', response);
            if (response.success && response.data && response.data.length > 0) {
              this.transactionData = response.data.map((transaction) => ({
                date: transaction.contract_date,
                floorLevel: transaction.floor_range,
                area: Math.round(transaction.area_sqm * 10.764), // Convert sqm to sqft
                price: transaction.price,
                psf: Math.round(transaction.price / (transaction.area_sqm * 10.764)),
              }));
              this.hasTransactions = true;
              // this.showPropertyTable = true;
              console.log('Transaction data loaded:', this.transactionData);
            } else {
              console.log('No transaction data found for this location');
              this.hasTransactions = false;
              this.showPropertyTable = false;
            }
          } catch (error) {
            console.error('Error fetching transaction data:', error);
            this.hasTransactions = false;
            this.showPropertyTable = false;
          }
        });

        this.markers.push(marker);
      }
    });
  }

  private clearMarkers(): void {
    // Remove all markers from the map
    console.log('Clearing markers...');
    this.markers.forEach((marker) => marker.setMap(null));
    this.markers = [];
  }

  // Format price to display with commas
  formatPrice(price: number): string {
    return price.toLocaleString();
  }

  // Center map on Condo location
  centerMapOnCondo(condo: any): void {
    if (condo.location?.coordinates?.length === 2) {
      const [longitude, latitude] = condo.location.coordinates;
      this.googleMap.setCenter({ lat: latitude, lng: longitude });
      this.googleMap.setZoom(15);
    }
  }

  async togglePropertyModal(data: any) {
    const { showTransactions } = await this.#blockDetailsModalService.open(data);
    if (showTransactions) await this.showHDBTransactionTable();
  }

  async toggleCondoPropertyModal(data: any) {
    const { showTransactions } = await this.#blockDetailsModalService.openCondo(data);
    if (showTransactions) await this.showCondoTransactionTable();
  }

  /**
   * Add markers for nearby condos on the map
   * @param condos Array of condos to add as markers
   * @param color Color for the markers (e.g., '#FF0000' for red)
   */
  addNearbyCondoMarkers(condos: any[], color: string): void {
    for (const condo of condos) {
      if (condo.location?.coordinates?.length === 2) {
        const [longitude, latitude] = condo.location.coordinates;

        const marker = new google.maps.Marker({
          position: { lat: latitude, lng: longitude },
          map: this.googleMap,
          title: condo.projectName || 'Unnamed Condo',
          icon: {
            path: google.maps.SymbolPath.CIRCLE,
            fillColor: color,
            fillOpacity: 0.7,
            strokeColor: '#000000',
            strokeWeight: 1,
            scale: 8,
          },
        });

        // Add info window for the condo
        const infoWindow = new google.maps.InfoWindow({
          content: html`
            <div>
              <h3>${condo.projectName || 'Unnamed Condo'}</h3>
              <p>${condo.address || 'No address available'}</p>
              <p>Total Units: ${condo.totalUnits || 'N/A'}</p>
            </div>
          `,
        });

        marker.addListener('click', () => {
          infoWindow.open(this.googleMap, marker);
        });

        this.nearbyCondoMarkers.push(marker);
      }
    }
  }

  /**
   * Add markers for nearby HDB properties on the map
   * @param hdb Array of HDB properties to add as markers
   * @param color Color for the markers (e.g., '#FF0000' for red)
   */
  addNearbyHDBMarkers(hdb: any[], color: string): void {
    for (const hdbItem of hdb) {
      if (hdbItem.location?.coordinates?.length === 2) {
        const [longitude, latitude] = hdbItem.location.coordinates;

        const marker = new google.maps.Marker({
          position: { lat: latitude, lng: longitude },
          map: this.googleMap,
          title: hdbItem.title || hdbItem.projectName || 'Unnamed HDB',
          icon: {
            path: google.maps.SymbolPath.CIRCLE,
            fillColor: color,
            fillOpacity: 0.7,
            strokeColor: '#000000',
            strokeWeight: 1,
            scale: 8,
          },
        });

        // Add info window for the HDB
        const infoWindow = new google.maps.InfoWindow({
          content: html`
            <div>
              <h3>${hdbItem.title || hdbItem.projectName || 'Unnamed HDB'}</h3>
              <p>Block: ${hdbItem.blockNumber || 'N/A'}</p>
              <p>Town: ${hdbItem.hdbTown || 'N/A'}</p>
              <p>Postal Code: ${hdbItem.postalCode || 'N/A'}</p>
            </div>
          `,
        });

        marker.addListener('click', () => {
          console.log('MARKER ICON CLICKED');
          infoWindow.open(this.googleMap, marker);
        });

        this.nearbyCondoMarkers.push(marker);
      }
    }
  }

  /**
   * Clear all radius circles from the map
   */
  clearRadiusCircles(): void {
    for (const circle of this.radiusCircles) circle.setMap(null);
    this.radiusCircles = [];
  }

  /**
   * Clear all nearby condo markers from the map
   */
  clearNearbyCondoMarkers(): void {
    for (const marker of this.nearbyCondoMarkers) marker.setMap(null);
    this.nearbyCondoMarkers = [];
  }

  private hideAllMarkers(): void {
    // Hide all existing markers
    this.clearMarkers();
    this.clearMapMarkers(this.schoolMarkers);
    this.clearNearbyCondoMarkers();
    this.clearMapMarkers(this.hawkerCenterMarkers);
    this.clearMapMarkers(this.busStopMarkers);
  }

  /**
   * Check if hawker centers are visible
   * @returns True if hawker centers are visible
   */
  isHawkerCentersVisible(): boolean {
    return this.hawkerCenterMarkers.length > 0;
  }

  /**
   * Restore all markers to the map
   */
  private restoreAllMarkers(): void {
    // Restore HDB markers
    if (this.activeTab === 'HDB' && this.showHDBData) {
      this.initializeHDBMap();
    }
    // Restore Condo markers
    else if (this.activeTab === 'Condo' && this.showCondoData) {
      this.initializeCondoMap();
    }
  }

  /**
   * Add markers for bus stops on the map
   * @param busStops Array of bus stops to add as markers
   * @param color Color for the markers
   */
  private addBusStopMarkers(busStops: any[], color: string): void {
    for (const busStop of busStops) {
      if (busStop.location?.coordinates?.length === 2) {
        const [longitude, latitude] = busStop.location.coordinates;

        // Create marker
        const marker = new google.maps.Marker({
          position: { lat: latitude, lng: longitude },
          map: this.googleMap,
          title: busStop.name,
          icon: {
            path: google.maps.SymbolPath.CIRCLE,
            fillColor: color,
            fillOpacity: 1,
            strokeColor: '#000000',
            strokeWeight: 1,
            scale: 8,
          },
        });

        // Create info window content
        const infoWindowContent = html`
          <div class="p-4">
            <h3 class="font-bold">${busStop.name}</h3>
            ${busStop.details ? html`<p>Landmark: ${busStop.details}</p>` : ''}
          </div>
        `;

        // Create info window
        const infoWindow = new google.maps.InfoWindow({ content: infoWindowContent });

        // Add click listener to open info window
        marker.addListener('click', () => {
          infoWindow.open(this.googleMap, marker);
        });

        // Add marker to array for later removal
        this.busStopMarkers.push(marker);
      }
    }
  }

  /**
   * Check if bus stops are visible
   * @returns True if bus stops are visible
   */
  isBusStopsVisible(): boolean {
    return this.busStopMarkers.length > 0;
  }

  /**
   * Add markers for hawker centers on the map
   * @param hawkerCenters Array of hawker centers to add as markers
   * @param color Color for the markers
   */
  private addHawkerCenterMarkers(hawkerCenters: any[]): void {
    for (const hawkerCenter of hawkerCenters) {
      if (hawkerCenter.location?.coordinates?.length === 2) {
        const [longitude, latitude] = hawkerCenter.location.coordinates;

        const marker = new google.maps.Marker({
          position: { lat: latitude, lng: longitude },
          map: this.googleMap,
          title: hawkerCenter.name,
          icon: {
            url: this.foodIconUrl,
            scaledSize: new google.maps.Size(30, 30), // Adjust size as needed
          },
        });

        const infoWindowContent = html`
          <div class="p-4 max-w-xs text-sm text-gray-800">
            <h3 class="text-base font-bold text-blue-700 mb-2">${hawkerCenter.name}</h3>
            ${hawkerCenter.photoUrl
              ? html`<p>
                  <span class="font-semibold">Photo:</span>
                  <img
                    src="${hawkerCenter.photoUrl}"
                    alt="Hawker Center"
                    class="w-full mx-auto h-32 object-cover"
                  />
                </p>`
              : ''}
            ${hawkerCenter.description
              ? html`<p class="mb-1 text-gray-600 italic">${hawkerCenter.description}</p>`
              : ''}

            <div class="space-y-1">
              <p>
                <span class="font-semibold">Address:</span> ${hawkerCenter.blockHouseNumber || ''}
                ${hawkerCenter.streetName || ''}
              </p>
              ${hawkerCenter.postalCode
                ? html`<p>
                    <span class="font-semibold">Postal Code:</span> ${hawkerCenter.postalCode}
                  </p>`
                : ''}
              ${hawkerCenter.cookedFoodStalls
                ? html`<p>
                    <span class="font-semibold">Food Stalls:</span> ${hawkerCenter.cookedFoodStalls}
                  </p>`
                : ''}
            </div>
          </div>
        `;

        // Create info window
        const infoWindow = new google.maps.InfoWindow({ content: infoWindowContent });

        // Add click listener to open info window
        marker.addListener('click', () => {
          infoWindow.open(this.googleMap, marker);
        });

        // Add marker to array for later removal
        this.hawkerCenterMarkers.push(marker);
      }
    }
  }

  public clearMapMarkers(markers: any[]): void {
    for (const marker of markers) marker.setMap(null);
    markers = [];
  }

  public clearAllMarkersAndCircles(): void {
    // Clear all marker arrays
    this.clearMapMarkers(this.schoolMarkers);
    this.clearMapMarkers(this.hospitalMarkers);
    this.clearMapMarkers(this.hawkerCenterMarkers);
    this.clearMapMarkers(this.busStopMarkers);

    this.schoolMarkers = [];
    this.hospitalMarkers = [];
    this.hawkerCenterMarkers = [];
    this.busStopMarkers = [];

    // Clear search result marker and circle
    if (this.searchResultMarker) {
      this.searchResultMarker.setMap(null);
      this.searchResultMarker = null;
    }

    if (this.searchResultCircle) {
      this.searchResultCircle.setMap(null);
      this.searchResultCircle = null;
    }

    // Clear user location circle
    if (this.userLocationCircle) {
      this.userLocationCircle.setMap(null);
      this.userLocationCircle = null;
    }

    // Clear all radius circles
    this.hawkerCenterRadiusCircles.forEach((circle) => circle.setMap(null));
    this.busStopRadiusCircles.forEach((circle) => circle.setMap(null));
    // Reset radius circle arrays
    this.hawkerCenterRadiusCircles = [];
    this.busStopRadiusCircles = [];
  }

  public addHospitalMarkers(hospitals: any[]) {
    for (const hospital of hospitals) {
      if (hospital.location?.coordinates?.length === 2) {
        const [longitude, latitude] = hospital.location.coordinates;

        // Create marker
        const marker = new google.maps.Marker({
          position: { lat: latitude, lng: longitude },
          map: this.googleMap,
          title: hospital.name,
          icon: {
            url: this.hospitalIconUrl,
            scaledSize: new google.maps.Size(30, 30), // Resize as needed
          },
        });

        const infoWindowContent = html`
          <div class="p-4 max-w-xs text-sm text-gray-800">
            <h3 class="text-base font-bold text-blue-700 mb-2">${hospital.buildingName}</h3>
            <div class="space-y-1">
              <p><span class="font-semibold">Address:</span> ${hospital.address}</p>
              <p><span class="font-semibold">Postal Code:</span> ${hospital.postal}</p>
            </div>
            <img
              src="https://maps.google.com/mapfiles/kml/shapes/hospitals.png"
              class="inline w-4 h-4 mr-1"
            />
            <a
              href="https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}"
              target="_blank"
              class="mt-2 inline-block text-blue-600 underline text-xs"
            >
              Get Directions
            </a>
          </div>
        `;

        // Create info window
        const infoWindow = new google.maps.InfoWindow({ content: infoWindowContent });

        // Add click listener to open info window
        marker.addListener('click', () => {
          infoWindow.open(this.googleMap, marker);
        });

        // Add marker to array for later removal
        this.hospitalMarkers.push(marker);
      }
    }
  }

  public addMetroMarkers(metroStations: any[]) {
    for (const metroStation of metroStations) {
      if (metroStation.location?.coordinates?.length === 2) {
        const [longitude, latitude] = metroStation.location.coordinates;

        // Create marker
        const marker = new google.maps.Marker({
          position: { lat: latitude, lng: longitude },
          map: this.googleMap,
          title: metroStation.stationName,
          icon: {
            url: this.metroIconUrl,
            scaledSize: new google.maps.Size(30, 30),
          },
        });

        // Create info window content
        const infoWindowContent = html`
          <div class="p-4 max-w-xs text-sm text-gray-800">
            <div class="flex items-center mb-2">
              <img
                src="https://maps.google.com/mapfiles/kml/shapes/rail.png"
                class="w-5 h-5 mr-2"
                alt="Metro Icon"
              />
              <h3 class="text-base font-bold text-blue-700">${metroStation.stationName}</h3>
            </div>

            <div class="space-y-1">
              ${metroStation.buildingName
                ? html`<p>
                    <span class="font-semibold">Building:</span> ${metroStation.buildingName}
                  </p>`
                : ''}
              <p><span class="font-semibold">Code:</span> ${metroStation.alphaNumericCode}</p>
              <p><span class="font-semibold">Address:</span> ${metroStation.address}</p>
            </div>
            <a
              href="https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}"
              target="_blank"
              class="mt-3 inline-block text-blue-600 underline text-xs"
            >
              Get Directions
            </a>
          </div>
        `;
        // Create info window
        const infoWindow = new google.maps.InfoWindow({ content: infoWindowContent });

        // Add click listener to open info window
        marker.addListener('click', () => {
          infoWindow.open(this.googleMap, marker);
        });

        // Add marker to array for later removal
        this.metroMarkers.push(marker);
      }
    }
  }

  public async findNearByMetroAndLRTStations(): Promise<void> {
    if (!this.userLocationCoordinates) {
      this.#snackBarService.error('Please set your location first');
      return;
    }
    try {
      // Convert radius from km to meters
      const radiusInMeters = this.selectedRadius * 1000;

      // Call the new controller function
      const response = await this.#metroController.getMetroStationsWithinRadius({
        latitude: this.userLocationCoordinates.lat,
        longitude: this.userLocationCoordinates.lng,
        radius: radiusInMeters,
      });

      if (response.success) {
        // Clear existing markers
        this.clearMapMarkers(this.metroMarkers);

        // Add new markers
        this.addMetroMarkers(response.metroStations);

        this.#snackBarService.success(
          `Found ${response.count} metro stations within ${this.selectedRadius}km`,
        );
      } else {
        this.#snackBarService.error('Failed to find metro stations');
      }
    } catch (error) {
      console.error('Error finding metro stations:', error);
      this.#snackBarService.error('Error finding metro stations');
    }
  }

  public async findNearbyHospitals(): Promise<void> {
    if (!this.userLocationCoordinates) {
      this.#snackBarService.error('Please set your location first');
      return;
    }
    try {
      // Convert radius from km to meters
      const radiusInMeters = this.selectedRadius * 1000;

      // Call the new controller function
      const response = await this.#hospitalController.getHospitalsWithinRadius({
        latitude: this.userLocationCoordinates.lat,
        longitude: this.userLocationCoordinates.lng,
        radius: radiusInMeters,
      });

      if (response.success) {
        // Clear existing markers
        this.clearMapMarkers(this.hospitalMarkers);

        // Add new markers
        this.addHospitalMarkers(response.hospitals);

        this.#snackBarService.success(
          `Found ${response.count} hospitals within ${this.selectedRadius}km`,
        );
      } else {
        this.#snackBarService.error('Failed to find hospitals');
      }
    } catch (error) {
      console.error('Error finding hospitals:', error);
      this.#snackBarService.error('Error finding hospitals');
    }
  }

  public async findNearbyBusStations(): Promise<void> {
    if (!this.userLocationCoordinates) {
      this.#snackBarService.error('Please set your location first');
      return;
    }

    this.isLoadingBusStops = true;
    try {
      // Convert radius from km to meters
      const radiusInMeters = this.selectedRadius * 1000;

      // Call the new controller function
      const response = await this.#busStopsController.findBusStopsWithinRadius({
        latitude: this.userLocationCoordinates.lat,
        longitude: this.userLocationCoordinates.lng,
        radius: radiusInMeters,
      });

      if (response.success) {
        // Clear existing markers
        this.clearMapMarkers(this.busStopMarkers);

        // Draw radius circle
        this.drawUserSelectedOptionRadius(this.userLocationCoordinates, radiusInMeters);

        // Add markers for bus stops
        this.addBusStopMarkers(response.busStops, '#FF9800');

        this.#snackBarService.success(
          `Found ${response.count} bus stops within ${this.selectedRadius}km`,
        );
      } else {
        this.#snackBarService.error('Failed to find bus stops');
      }
    } catch (error) {
      console.error('Error finding bus stops:', error);
      this.#snackBarService.error('Error finding bus stops');
    } finally {
      this.isLoadingBusStops = false;
    }
  }

  public addSchoolMarkers(schools: any[], color: string) {
    for (const school of schools) {
      if (school.location?.coordinates?.length === 2) {
        const [longitude, latitude] = school.location.coordinates;

        // Create marker
        const marker = new google.maps.Marker({
          position: { lat: latitude, lng: longitude },
          map: this.googleMap,
          title: school.name,
          icon: {
            path: google.maps.SymbolPath.CIRCLE,
            fillColor: color,
            fillOpacity: 1,
            strokeColor: '#000000',
            strokeWeight: 1,
            scale: 8,
          },
        });

        // Create info window content
        const infoWindowContent = html`
          <div class="p-2 max-w-md text-sm text-gray-800">
            <h3 class="text-lg text-center font-bold text-blue-700 mb-1">${school.schoolName}</h3>
            <div class="space-y-1">
              <p><span class="font-semibold py-1">Address:</span> ${school.address}</p>
              <p><span class="font-semibold py-1">Postal Code:</span> ${school.postal}</p>
              <p>
                <span class="font-semibold py-1">Telephone:</span> ${school.telephoneNumber ||
                'N/A'}
              </p>
              <p>
                <span class="font-semibold py-1">Email:</span>
                <a href="mailto:${school.email}" class="text-blue-600 underline"
                  >${school.email || 'N/A'}</a
                >
              </p>
              <p><span class="font-semibold py-1">Town:</span> ${school.town}</p>
              <p><span class="font-semibold py-1">Level:</span> ${school.schoolLevel}</p>
              <p><span class="font-semibold py-1">Type:</span> ${school.schoolType}</p>
            </div>
          </div>
        `;

        // Create info window
        const infoWindow = new google.maps.InfoWindow({ content: infoWindowContent });

        // Add click listener to open info window
        marker.addListener('click', () => {
          infoWindow.open(this.googleMap, marker);
        });

        // Add marker to array for later removal
        this.schoolMarkers.push(marker);
      }
    }
  }

  public async findNearbySchools(): Promise<void> {
    if (!this.userLocationCoordinates) {
      this.#snackBarService.error('Please set your location first');
      return;
    }

    this.isLoadingSchools = true;

    try {
      // Convert radius from km to meters
      const radiusInMeters = this.selectedRadius * 1000;

      // Call the new controller function
      const response = await this.#schoolController.findSchoolsWithinRadius({
        latitude: this.userLocationCoordinates.lat,
        longitude: this.userLocationCoordinates.lng,
        radius: radiusInMeters,
      });

      console.log('---->>>NEARBY SCHOOLS<<<----', response.schools);

      if (response.success) {
        // Clear existing markers
        this.clearMapMarkers(this.schoolMarkers);

        if (this.userLocationCircle) {
          this.userLocationCircle.setMap(null);
        }
        this.hawkerCenterRadiusCircles.forEach((circle) => {
          circle.setMap(null);
        });
        this.hawkerCenterMarkers = [];

        // Draw radius circle
        this.drawUserSelectedOptionRadius(this.userLocationCoordinates, radiusInMeters);

        // Add markers for schools
        this.addSchoolMarkers(response.schools, '#FF9800');

        this.#snackBarService.success(
          `Found ${response.count} schools within ${this.selectedRadius}km`,
        );
      } else {
        this.#snackBarService.error('Failed to find schools');
      }
    } catch (error) {
      console.error('Error finding schools:', error);
      this.#snackBarService.error('Error finding schools');
    } finally {
      this.isLoadingSchools = false;
    }
  }

  /**
   * Find hawker centers near the user's location using the new controller function
   */
  public async findNearbyHawkerCenters(): Promise<void> {
    if (!this.userLocationCoordinates) {
      this.#snackBarService.error('Please set your location first');
      return;
    }

    this.isLoadingHawkerCenters = true;

    try {
      // Convert radius from km to meters
      const radiusInMeters = this.selectedRadius * 1000;

      // Call the new controller function
      const response = await this.#hawkerController.findHawkerCentersWithinRadius({
        latitude: this.userLocationCoordinates.lat,
        longitude: this.userLocationCoordinates.lng,
        radius: radiusInMeters,
      });

      if (response.success) {
        // Clear existing markers
        this.clearMapMarkers(this.hawkerCenterMarkers);

        // Draw radius circle
        this.drawUserSelectedOptionRadius(this.userLocationCoordinates, radiusInMeters);

        // Add markers for hawker centers
        this.addHawkerCenterMarkers(response.hawkerCenters);

        this.#snackBarService.success(
          `Found ${response.count} hawker centers within ${this.selectedRadius}km`,
        );
      } else {
        this.#snackBarService.error('Failed to find hawker centers');
      }
    } catch (error) {
      console.error('Error finding hawker centers:', error);
      this.#snackBarService.error('Error finding hawker centers');
    } finally {
      this.isLoadingHawkerCenters = false;
    }
  }

  /**
   * Draw a radius circle for hawker center search
   */
  private drawUserSelectedOptionRadius(center: { lat: number; lng: number }, radius: number): void {
    // Create radius circle
    const circle = new google.maps.Circle({
      strokeColor: '#FF9800',
      strokeOpacity: 0.8,
      strokeWeight: 2,
      fillColor: '#FF9800',
      fillOpacity: 0.1,
      map: this.googleMap,
      center: center,
      radius: radius,
    });

    // Add circle to array for later removal
    this.hawkerCenterRadiusCircles.push(circle);
  }

  /**
   * Generic function to handle nearby option checkbox changes
   * @param optionId The ID of the option that changed
   * @param isChecked Whether the checkbox is checked
   * @param position The position to use for the search (optional)
   */
  private handleNearbyOptionChange(
    optionId: string,
    isChecked: boolean,
    position?: google.maps.LatLngLiteral,
  ): void {
    // Handle different checkbox options
    switch (optionId) {
      case 'schools':
        if (isChecked) {
          this.findNearbySchools();
        } else {
          this.clearMapMarkers(this.schoolMarkers);
        }
        break;

      case 'hawker':
        if (isChecked) {
          this.findNearbyHawkerCenters();
        } else {
          this.clearMapMarkers(this.hawkerCenterMarkers);
        }
        break;

      case 'busStops':
        if (isChecked) {
          this.findNearbyBusStations();
        } else {
          this.clearMapMarkers(this.busStopMarkers);
        }
        break;

      case 'mrt':
        if (isChecked) {
          console.log('MRT/LRT Stations option checked');
          this.findNearByMetroAndLRTStations();
        } else {
          this.clearMapMarkers(this.metroMarkers);
        }
        break;

      case 'healthcare':
        if (isChecked) {
          console.log('Healthcare option checked');
          this.findNearbyHospitals();
        } else {
          this.clearMapMarkers(this.hospitalMarkers);
        }
        break;

      case 'salesListings':
        if (isChecked && position) {
          this.drawUserLocationRadius(position);
          this.findPropertiesNearUserLocation();
        } else {
          this.clearNearbyPropertiesMarkers();
        }
        break;

      case 'hdbData':
        if (isChecked) {
          this.findPropertiesNearUserLocation();
        } else {
          // This option doesn't have specific markers to clear
          // It uses the same markers as salesListings
          this.clearNearbyPropertiesMarkers();
        }
        break;
    }
  }

  /**
   * Clear all hawker center radius circles from the map
   */
  private clearHawkerCenterRadiusCircles(): void {
    for (const circle of this.hawkerCenterRadiusCircles) circle.setMap(null);
    this.hawkerCenterRadiusCircles = [];
  }

  /**
   * Converts a LatLng position to pixel coordinates on the screen
   * @param _latLng The latitude and longitude position
   * @returns The pixel coordinates {x, y}
   */
  private getPixelPositionFromLatLng(_latLng: google.maps.LatLng): { x: number; y: number } {
    // Since we're using a fixed position for the table now, we don't need to calculate exact pixel coordinates
    // Just return fixed values that will ensure the table is visible
    return { x: 0, y: 0 };
  }

  /**
   * Show the user's current location on the map
   * Uses browser's geolocation API to get the current position
   */
  showCurrentLocation(): void {
    // Clear any existing user location markers
    this.clearUserLocation();
    this.clearMarkers();

    this.isLoadingUserLocation = true;
    this.#snackBarService.info('Getting your current location...');

    // Check if geolocation is supported by the browser
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (_position) => {
          // Success callback

          //user's actual location
          // const userLocation = {
          //   lat: _position.coords.latitude,
          //   lng: _position.coords.longitude
          // };

          //temp location for testing
          const userLocation = {
            lat: 1.2834,
            lng: 103.8607,
          };

          // Add marker at user's location
          this.addUserLocationMarker(userLocation);

          // Center the map on the user's location
          this.googleMap.setCenter(userLocation);
          this.googleMap.setZoom(15); // Zoom in to a reasonable level

          // Draw radius circle
          this.drawUserLocationRadius(userLocation);

          this.isLoadingUserLocation = false;
          this.#snackBarService.success('Found your location');
          this.showUserLocationInfoWindow = true;
        },
        (error) => {
          // Error callback
          this.isLoadingUserLocation = false;
          console.error('Error getting user location:', error);

          let errorMessage = 'Could not get your location';
          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorMessage =
                'Location access denied. Please enable location services in your browser.';
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage = 'Location information is unavailable.';
              break;
            case error.TIMEOUT:
              errorMessage = 'Location request timed out.';
              break;
          }

          this.#snackBarService.error(errorMessage);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 0,
        },
      );
    } else {
      this.isLoadingUserLocation = false;
      this.#snackBarService.error('Geolocation is not supported by your browser');
    }
  }

  /**
   * Add a marker at the user's current location
   * @param position The user's position (latitude and longitude)
   */
  private addUserLocationMarker(position: google.maps.LatLngLiteral): void {
    const { Marker, InfoWindow } = google.maps;

    // Create a marker with a custom icon
    this.userLocationMarker = new Marker({
      position,
      map: this.googleMap,
      icon: {
        path: google.maps.SymbolPath.CIRCLE,
        fillColor: '#4285F4', // Google blue
        fillOpacity: 1,
        strokeColor: '#FFFFFF',
        strokeWeight: 2,
        scale: 10,
      },
      title: 'Your Location',
      animation: google.maps.Animation.DROP,
      zIndex: 1000, // Ensure it's above other markers
    });

    // Store the user location coordinates for later use
    this.userLocationCoordinates = position;

    // Set default radius to 2km
    this.selectedRadius = 1;

    // Create an InfoWindow with the UI shown in the first image
    const infoWindowContent = document.createElement('div');
    infoWindowContent.className = 'info-window-content p-4 max-w-xs';
    // Property title section
    const propertyTitle = document.createElement('div');
    propertyTitle.className = 'mb-3';

    const blockTitle = document.createElement('h3');
    blockTitle.textContent = 'My Location';
    blockTitle.className = 'text-lg font-bold dark:text-black';
    propertyTitle.appendChild(blockTitle);
    // propertyTitle.appendChild(projectName);
    // propertyTitle.appendChild(address);
    infoWindowContent.appendChild(propertyTitle);

    // Show Nearby section with radius dropdown
    const nearbySection = document.createElement('div');
    nearbySection.className = 'mb-3';

    const nearbyLabel = document.createElement('div');
    nearbyLabel.className = 'flex items-center justify-between mb-2 gap-1';

    const nearbyText = document.createElement('span');
    nearbyText.textContent = 'Show Nearby within';
    nearbyText.className = 'text-md dark:text-black';

    const radiusDropdown = document.createElement('select');
    radiusDropdown.className =
      'px-2 py-1 bg-white border border-gray-300 text-gray-800 rounded w-24';
    radiusDropdown.addEventListener('change', (event: Event) => {
      this.selectedRadius = Number((event.target as HTMLSelectElement).value);
      this.updateLocationRadius();
    });
    radiusDropdown.innerHTML = `
      <option value="1" selected>1 km</option>
      <option value="2">2 km</option>
      <option value="3">3 km</option>
      <option value="4">4 km</option>
    `;

    nearbyLabel.appendChild(nearbyText);
    nearbyLabel.appendChild(radiusDropdown);
    nearbySection.appendChild(nearbyLabel);

    // Checkboxes for nearby options
    const checkboxOptions = [
      { id: 'schools', label: 'Schools' },
      { id: 'hawker', label: 'Hawker Centres' },
      { id: 'busStops', label: 'Bus Stops' },
      { id: 'mrt', label: 'MRT/LRT Stations' },
      { id: 'salesListings', label: 'Sales Listings' },
      { id: 'hdbData', label: 'HDB Data' },
      { id: 'healthcare', label: 'Healthcare' },
    ];

    checkboxOptions.forEach((option) => {
      const checkboxContainer = document.createElement('div');
      checkboxContainer.className = 'flex items-center mb-2';

      const checkbox = document.createElement('input');
      checkbox.type = 'checkbox';
      checkbox.id = option.id;
      checkbox.className = 'mr-2';

      const label = document.createElement('label');
      label.htmlFor = option.id;
      label.textContent = option.label;
      label.className = 'text-md dark:text-black';

      // Add event listeners based on checkbox type
      checkbox.addEventListener('change', (event: Event) => {
        const isChecked = (event.target as HTMLInputElement).checked;

        // Handle different checkbox options using the generic function
        this.handleNearbyOptionChange(option.id, isChecked, position);
      });

      checkboxContainer.appendChild(checkbox);
      checkboxContainer.appendChild(label);
      nearbySection.appendChild(checkboxContainer);
    });

    infoWindowContent.appendChild(nearbySection);

    const infoWindow = new InfoWindow({
      content: infoWindowContent,
      disableAutoPan: false,
    });

    // Add click event to show the info window
    this.userLocationMarker.addListener('click', () => {
      infoWindow.open(this.googleMap, this.userLocationMarker);
    });

    // Open the info window initially
    setTimeout(() => {
      if (this.userLocationMarker) {
        infoWindow.open(this.googleMap, this.userLocationMarker);
      }
    }, 500);
  }

  /**
   * Draw a circle representing the selected radius around the user's location
   * @param center The center position for the radius circle
   */
  private drawUserLocationRadius(center: google.maps.LatLngLiteral): void {
    // Clear any existing radius circle
    if (this.userLocationCircle) {
      this.userLocationCircle.setMap(null);
    }

    const { Circle } = google.maps;

    // Create a new circle with the selected radius
    this.userLocationCircle = new Circle({
      map: this.googleMap,
      center,
      radius: this.selectedRadius * 1000, // Convert km to meters
      strokeColor: '#4285F4',
      strokeOpacity: 0.8,
      strokeWeight: 2,
      fillColor: '#4285F4',
      fillOpacity: 0.1,
    });
  }

  /**
   * Update the radius circle when the user selects a new radius
   */
  updateLocationRadius(): void {
    if (this.userLocationMarker && this.userLocationCircle) {
      // Get the current center of the circle
      const center = this.userLocationMarker.getPosition().toJSON();

      if (this.userLocationCircle) {
        this.userLocationCircle.setMap(null);
      }

      if (this.searchResultCircle) {
        this.searchResultCircle.setMap(null);
      }

      this.hawkerCenterRadiusCircles.forEach((circle) => {
        circle.setMap(null);
      });
      this.busStopRadiusCircles.forEach((circle) => {
        circle.setMap(null);
      });
      this.hawkerCenterRadiusCircles = [];
      this.busStopRadiusCircles = [];

      console.log('USER LOCATION RADIUS', this.userLocationCircle);

      // Update the radius
      this.drawUserLocationRadius(center);

      this.#snackBarService.info(`Radius updated to ${this.selectedRadius} km for your location.`);

      // Get all checkboxes and update their state based on the new radius
      const checkboxOptions = [
        'schools',
        'hawker',
        'busStops',
        'mrt',
        'salesListings',
        'hdbData',
        'healthcare',
      ];

      // Update each active option with the new radius
      checkboxOptions.forEach((optionId) => {
        const checkbox = document.getElementById(optionId) as HTMLInputElement;
        if (checkbox && checkbox.checked) {
          // Call the generic function to handle this option with the new radius
          this.handleNearbyOptionChange(optionId, true, center);
        }
      });
    }

    // Update search result circle if it exists
    if (this.searchResultMarker && this.searchResultCircle && this.setSearchResultMarker) {
      // Get the current center of the circle
      const center = this.searchResultMarker.getPosition().toJSON();

      if (this.searchResultCircle) {
        this.searchResultCircle.setMap(null);
        this.searchResultCircle = null;
      }

      if (this.userLocationCircle) {
        this.userLocationCircle.setMap(null);
        this.userLocationCircle = null;
      }

      // Update the radius
      this.drawSearchResultRadius(center);

      this.#snackBarService.info(`Radius updated to ${this.selectedRadius} km`);

      // If properties have already been shown, automatically update them with the new radius
      if (this.propertiesShown) {
        this.findPropertiesNearUserLocation();
      }
    }
  }

  /**
   * Clear the user location marker and radius circle
   */
  clearUserLocation(): void {
    if (this.userLocationMarker) {
      this.userLocationMarker.setMap(null);
      this.userLocationMarker = null;
    }

    if (this.userLocationCircle) {
      this.userLocationCircle.setMap(null);
      this.userLocationCircle = null;
    }

    // Hide the info window
    this.showUserLocationInfoWindow = false;

    // Clear nearby properties markers
    this.clearNearbyPropertiesMarkers();

    // Reset user location coordinates
    this.userLocationCoordinates = null;

    // Reset properties shown flag
    this.propertiesShown = false;
  }

  /**
   * Find properties near the user's location based on the selected tab (HDB or Condo)
   */
  async findPropertiesNearUserLocation(): Promise<void> {
    if (!this.userLocationCoordinates || !this.activeTab) {
      this.#snackBarService.error('No location selected or no property type selected');
      return;
    }

    // Hide the info window
    this.showUserLocationInfoWindow = false;

    // Clear any existing nearby property markers
    this.clearNearbyPropertiesMarkers();

    this.isLoadingNearbyProperties = true;
    this.#snackBarService.info(
      `Finding ${this.activeTab} properties within ${this.selectedRadius} km...`,
    );

    try {
      const { lat: latitude, lng: longitude } = this.userLocationCoordinates;
      const radius = this.selectedRadius * 1000; // Convert km to meters for MongoDB

      if (this.activeTab === 'HDB') {
        // Call HDB controller function
        const response = await this.#hdbController.findHDBPropertiesNearUserLocation({
          latitude,
          longitude,
          radius,
        });

        if (response.success && response.hdb.length > 0) {
          // Add markers for nearby HDB properties
          this.addNearbyPropertiesMarkers(response.hdb, '#FF5722'); // Orange color for HDB
          this.#snackBarService.success(
            `Found ${response.hdb.length} HDB properties within ${this.selectedRadius} km`,
          );
          this.propertiesShown = true; // Set flag to indicate properties have been shown
        } else {
          this.#snackBarService.info(`No HDB properties found within ${this.selectedRadius} km`);
        }
      } else if (this.activeTab === 'Condo') {
        // Call Condo controller function
        const response = await this.#condoController.findCondosNearUserLocation({
          latitude,
          longitude,
          radius,
        });

        if (response.success && response.condos.length > 0) {
          // Add markers for nearby Condo properties
          this.addNearbyPropertiesMarkers(response.condos, '#9C27B0'); // Purple color for Condos
          this.#snackBarService.success(
            `Found ${response.condos.length} Condos within ${this.selectedRadius} km`,
          );
          this.propertiesShown = true; // Set flag to indicate properties have been shown
        } else {
          this.#snackBarService.info(`No Condos found within ${this.selectedRadius} km`);
        }
      }
    } catch (error) {
      console.error('Error finding nearby properties:', error);
      this.#snackBarService.error('Error finding nearby properties');
    } finally {
      this.isLoadingNearbyProperties = false;
    }
  }

  /**
   * Add markers for nearby properties on the map
   * @param properties Array of properties to add as markers
   * @param color Color for the markers
   */
  private addNearbyPropertiesMarkers(properties: any[], color: string): void {
    const { Marker } = google.maps;

    properties.forEach((property) => {
      // Extract coordinates based on the property type
      let position: google.maps.LatLngLiteral;
      let title: string;

      if (this.activeTab === 'HDB') {
        // HDB properties have location.coordinates
        const [lng, lat] = property.location.coordinates;
        position = { lat, lng };
        title = `${property.blockNumber} ${property.hdbTown || 'HDB'}`;
      } else {
        // Condo properties have location.coordinates
        const [lng, lat] = property.location.coordinates;
        position = { lat, lng };
        title = property.projectName || 'Condo';
      }

      // Create a marker
      const marker = new Marker({
        position,
        map: this.googleMap,
        icon: {
          path: google.maps.SymbolPath.CIRCLE,
          fillColor: color,
          fillOpacity: 0.8,
          strokeColor: '#FFFFFF',
          strokeWeight: 1,
          scale: 8,
        },
        title,
        animation: google.maps.Animation.DROP,
      });

      // Add click event to the marker to show property details
      marker.addListener('click', async () => {
        try {
          // Show loading indicator
          // this.#snackBarService.info('Loading property details...');

          if (this.activeTab === 'HDB') {
            // Fetch complete HDB data by ID
            const { data } = await this.#hdbController.getById({ id: property._id });
            console.log('BLOCK HDB DATA for transaction search ', data);
            // Fetch transaction data
            const hdbResponse = await this.#hdbController.extractHDBTransactionData({
              latitude: Number(property.location.coordinates[1]),
              longitude: Number(property.location.coordinates[0]),
            });
            console.log('HDB TRANSACTION RESPONSE', hdbResponse);
            // Update transaction data if available
            if (hdbResponse.success && hdbResponse.data && hdbResponse.data.length > 0) {
              //console.log('HDB transaction data:', hdbResponse.data);
              this.transactionData = hdbResponse.data.slice(0, 10).map((transaction) => ({
                date: transaction.month,
                flatType: transaction.flat_type || '4-Room',
                floorLevel: transaction.storey_range,
                area: Math.round(transaction.floor_area_sqm * 10.764), // Convert sqm to sqft
                price: transaction.resale_price,
                psf: Math.round(transaction.resale_price / (transaction.floor_area_sqm * 10.764)),
              }));
              this.hasTransactions = true;
            } else {
              this.hasTransactions = false;
              this.transactionData = [];
            }

            // Show property details modal
            this.togglePropertyModal(data);
          } else {
            // Fetch complete Condo data by ID
            const { data } = await this.#condoController.getById({ id: property._id });

            // Fetch transaction data
            const [longitude, latitude] = property.location.coordinates;
            const response = await this.#condoController.getLatestTransactionsByLocation({
              lat: latitude.toString(),
              long: longitude.toString(),
            });

            // Update transaction data if available
            if (response.success && response.data && response.data.length > 0) {
              this.transactionData = response.data.map((transaction) => ({
                date: transaction.contract_date,
                floorLevel: transaction.floor_range,
                area: Math.round(transaction.area_sqm * 10.764), // Convert sqm to sqft
                price: transaction.price,
                psf: Math.round(transaction.price / (transaction.area_sqm * 10.764)),
              }));
              this.hasTransactions = true;
            } else {
              this.hasTransactions = false;
              this.transactionData = [];
            }

            // Show property details modal
            this.toggleCondoPropertyModal(data);
          }
        } catch (error) {
          console.error('Error fetching property details:', error);
          this.#snackBarService.error('Error loading property details');
        }
      });

      // Add to the array of nearby property markers
      this.nearbyPropertiesMarkers.push(marker);
    });
  }

  /**
   * Clear all nearby property markers from the map
   */
  private clearNearbyPropertiesMarkers(): void {
    this.nearbyPropertiesMarkers.forEach((marker) => {
      marker.setMap(null);
    });
    this.nearbyPropertiesMarkers = [];
  }

  /**
   * Handle search input with debounce
   */
  onSearchInput(): void {
    // Show dropdown when typing
    this.showDropdown = true;

    // Clear any previous timer
    if (this.searchDebounceTimer) {
      clearTimeout(this.searchDebounceTimer);
    }

    // If search query is empty, clear results
    if (!this.searchQuery || this.searchQuery.trim().length < 2) {
      this.searchResults = [];
      return;
    }

    // Set a new timer to delay the API call
    this.searchDebounceTimer = setTimeout(() => {
      this.fetchSearchResults();
    }, 300); // 300ms debounce
  }

  /**
   * Fetch search results from the OneMap API
   */
  async fetchSearchResults(): Promise<void> {
    try {
      const response = await this.#oneMapSearchController.getSearchResults({
        searchVal: this.searchQuery.trim(),
      });

      if (response.success && response.locations && response.locations.length > 0) {
        this.searchResults = response.locations;
        console.log('Search results:', this.searchResults);
      } else {
        this.searchResults = [];
      }
    } catch (error) {
      console.error('Error fetching search results:', error);
      this.#snackBarService.error('Error searching for locations');
      this.searchResults = [];
    }
  }

  findIfSearchResultExists(result: any): boolean {
    return this.recentSearches.some(
      (search) => search.LATITUDE === result.LATITUDE && search.LONGITUDE === result.LONGITUDE,
    );
  }

  /**
   * Select a search result and show it on the map
   * @param result The selected search result
   */
  selectSearchResult(result: any): void {
    // Hide the dropdown when a result is selected
    this.showDropdown = false;

    // Add to recent searches if not already there
    if (!this.findIfSearchResultExists(result)) {
      this.recentSearches.push(result);
      console.log('ADDED SEARCH RESULT', this.recentSearches);
    }

    // Clear any existing search result marker
    this.clearSearchResultMarker();
    this.setSearchResultMarker = true;
    // Clear user location if present
    if (this.userLocationMarker) {
      this.clearUserLocation();
    }

    // Clear all other markers
    this.clearMarkers();

    // Hide property data if shown
    this.showHDBData = false;
    this.showCondoData = false;

    // Extract coordinates
    const position = {
      lat: Number(result.LATITUDE),
      lng: Number(result.LONGITUDE),
    };

    // Store the coordinates for property search
    this.userLocationCoordinates = position;

    // Set default radius to 2km
    this.selectedRadius = 1;

    // Add marker at the selected location
    this.addSearchResultMarker(position, result);

    // Draw radius circle
    this.drawSearchResultRadius(position);

    // Center the map on the selected location
    this.googleMap.setCenter(position);
    this.googleMap.setZoom(16); // Zoom in to a reasonable level

    // Clear search results dropdown but keep the query
    this.searchResults = [];
  }

  /**
   * Add a marker for the selected search result
   * @param position The position (lat/lng) to place the marker
   * @param result The search result data
   */
  private addSearchResultMarker(position: google.maps.LatLngLiteral, result: any): void {
    const { Marker, InfoWindow } = google.maps;

    // Create a marker
    this.searchResultMarker = new Marker({
      position,
      map: this.googleMap,
      icon: {
        path: google.maps.SymbolPath.CIRCLE,
        fillColor: '#4CAF50', // Green
        fillOpacity: 1,
        strokeColor: '#FFFFFF',
        strokeWeight: 2,
        scale: 12,
      },
      title: result.BUILDING || result.ROAD_NAME || 'Location',
      animation: google.maps.Animation.DROP,
      zIndex: 1000, // Ensure it's above other markers
    });

    // Store the coordinates for later use
    this.userLocationCoordinates = position;

    // Set default radius to 1km
    this.selectedRadius = 1;

    // Create an InfoWindow with the UI shown in the first image
    const infoWindowContent = document.createElement('div');
    infoWindowContent.className = 'info-window-content p-4 max-w-xs';
    // Property title section
    const propertyTitle = document.createElement('div');
    propertyTitle.className = 'mb-3 dark:text-black';

    console.log('<<<-----SEARCH RESULT--->>>', result);

    const address = document.createElement('p');
    address.textContent = result.ROAD_NAME || result.ADDRESS || 'Location';
    address.className = 'text-md';

    // propertyTitle.appendChild(blockTitle);
    // propertyTitle.appendChild(projectName);
    propertyTitle.appendChild(address);
    infoWindowContent.appendChild(propertyTitle);

    // Show Nearby section with radius dropdown
    const nearbySection = document.createElement('div');
    nearbySection.className = 'mb-3';

    const nearbyLabel = document.createElement('div');
    nearbyLabel.className = 'flex items-center justify-between mb-2 gap-1';

    const nearbyText = document.createElement('span');
    nearbyText.textContent = 'Show Nearby within';
    nearbyText.className = 'text-md dark:text-black';

    const radiusDropdown = document.createElement('select');
    radiusDropdown.className =
      'px-2 py-1 bg-white text-gray-800 rounded border border-gray-300 w-24';
    radiusDropdown.addEventListener('change', (event: Event) => {
      this.selectedRadius = Number((event.target as HTMLSelectElement).value);
      this.updateLocationRadius();
    });
    radiusDropdown.innerHTML = `
      <option value="1" selected>1 km</option>
      <option value="2">2 km</option>
      <option value="3">3 km</option>
      <option value="4">4 km</option>
    `;

    nearbyLabel.appendChild(nearbyText);
    nearbyLabel.appendChild(radiusDropdown);
    nearbySection.appendChild(nearbyLabel);

    // Checkboxes for nearby options
    const checkboxOptions = [
      { id: 'schools', label: 'Schools' },
      { id: 'hawker', label: 'Hawker Centres' },
      { id: 'busStops', label: 'Bus Stops' },
      { id: 'mrt', label: 'MRT/LRT Stations' },
      { id: 'salesListings', label: 'Sales Listings' },
      { id: 'hdbData', label: 'HDB Data' },
      { id: 'healthcare', label: 'Healthcare' },
    ];

    checkboxOptions.forEach((option) => {
      const checkboxContainer = document.createElement('div');
      checkboxContainer.className = 'flex items-center mb-2';

      const checkbox = document.createElement('input');
      checkbox.type = 'checkbox';
      checkbox.id = option.id;
      checkbox.className = 'mr-2';

      const label = document.createElement('label');
      label.htmlFor = option.id;
      label.textContent = option.label;
      label.className = 'text-md dark:text-black';

      // Add event listeners based on checkbox type
      checkbox.addEventListener('change', (event: Event) => {
        const isChecked = (event.target as HTMLInputElement).checked;

        // Handle different checkbox options using the generic function
        this.handleNearbyOptionChange(option.id, isChecked, position);
      });

      checkboxContainer.appendChild(checkbox);
      checkboxContainer.appendChild(label);
      nearbySection.appendChild(checkboxContainer);
    });

    infoWindowContent.appendChild(nearbySection);

    const infoWindow = new InfoWindow({
      content: infoWindowContent,
      disableAutoPan: false,
    });

    // Add click event to show the info window
    this.searchResultMarker.addListener('click', () => {
      infoWindow.open(this.googleMap, this.searchResultMarker);
    });

    // Open the info window initially
    setTimeout(() => {
      if (this.searchResultMarker) {
        infoWindow.open(this.googleMap, this.searchResultMarker);
      }
    }, 500);
  }

  /**
   * Draw a radius circle around the search result
   * @param position The center position for the circle
   */
  private drawSearchResultRadius(position: google.maps.LatLngLiteral): void {
    const { Circle } = google.maps;

    // Clear any existing circle
    if (this.searchResultCircle) {
      this.searchResultCircle.setMap(null);
    }

    // Create a new circle
    this.searchResultCircle = new Circle({
      map: this.googleMap,
      center: position,
      radius: this.selectedRadius * 1000, // Convert km to meters
      strokeColor: '#4CAF50',
      strokeOpacity: 0.8,
      strokeWeight: 2,
      fillColor: '#4CAF50',
      fillOpacity: 0.1,
    });
  }

  /**
   * Clear the search result marker
   */
  private clearSearchResultMarker(): void {
    this.clearAllMarkersAndCircles();
    this.searchQuery = '';
    this.searchResults = [];
    this.setSearchResultMarker = false;
  }

  clearSearch(): void {
    this.clearAllMarkersAndCircles();
    this.searchQuery = '';
    this.searchResults = [];
  }

  setActiveTab(tab: 'HDB' | 'Condo'): void {
    this.activeTab = tab;
    // Reset pagination when switching tabs
    if (tab === 'HDB') {
      this.currentHDBPage = 0;
    } else {
      this.currentCondoPage = 0;
    }
  }

  /**
   * Load a specific page of HDB data
   * @param page The page number to load (0-based)
   */
  async loadHDBPage(page: number): Promise<void> {
    if (page < 0) return;

    this.isLoading = true;
    try {
      this.currentHDBPage = page;
      const response = await this.#hdbController.getAll({
        page: this.currentHDBPage,
        limit: this.selectedHDBLimit,
      });
      this.hdbData = response.data;
      this.totalHDBCount = response.total;
      this.#snackBarService.success(
        `Loaded ${this.hdbData.length} of ${this.totalHDBCount} HDB properties`,
      );
      this.initializeHDBMap();
    } catch (error) {
      console.error('Error fetching HDB data:', error);
      this.#snackBarService.error('Error fetching HDB data');
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Load a specific page of Condo data
   * @param page The page number to load (0-based)
   */
  async loadCondoPage(page: number): Promise<void> {
    if (page < 0) return;

    this.isLoading = true;
    try {
      this.currentCondoPage = page;
      const response = await this.#condoController.getAll({
        page: this.currentCondoPage,
        limit: this.selectedCondoLimit,
      });
      this.apiCondoData = response.data;
      this.totalCondoCount = response.total;
      this.#snackBarService.success(
        `Loaded ${this.apiCondoData.length} of ${this.totalCondoCount} condo properties`,
      );
      this.initializeCondoMap();
    } catch (error) {
      console.error('Error fetching Condo data:', error);
      this.#snackBarService.error('Error fetching Condo data');
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Display HDB properties filtered by selected towns
   */
  private async displayFilteredHDBProperties(): Promise<void> {
    // Clear existing markers
    this.clearMarkers();

    // If no towns are selected, show all HDB data
    if (this.selectedTowns.size === 0) {
      this.initializeHDBMap();
      return;
    }

    this.isLoading = true;
    try {
      // Fetch data for each selected town and add markers
      for (const town of Array.from(this.selectedTowns)) {
        const response = await this.#hdbController.filterDataByTown({ town });
        console.log(`RESPONSE FOR HDB TOWN DATA (${town}):`, response);

        if (response.data && response.data.length > 0) {
          this.addTownMarkers(response.data, town);
        }
      }

      // Show success message
      this.#snackBarService.success(
        `Showing HDB properties in ${Array.from(this.selectedTowns).join(', ')}`,
      );
    } catch (error) {
      console.error('Error fetching town data:', error);
      this.#snackBarService.error('Error fetching town data');
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Add markers for properties in a specific town
   */
  private addTownMarkers(properties: any[], townName: string): void {
    const { Marker } = google.maps;

    properties.forEach((property) => {
      if (property.location?.coordinates?.length === 2) {
        const [longitude, latitude] = property.location.coordinates;

        const marker = new Marker({
          position: { lat: latitude, lng: longitude },
          map: this.googleMap,
          title: property.title || `Block ${property.blockNumber}`,
          icon: {
            path: google.maps.SymbolPath.CIRCLE,
            fillColor: this.townColors[townName] || '#4285F4',
            fillOpacity: 0.8,
            strokeColor: '#FFFFFF',
            strokeWeight: 1,
            scale: 8,
          },
        });

        // Add click listener to show property details
        marker.addListener('click', async () => {
          // Hide any previously shown property table
          this.showPropertyTable = false;
          this.hasTransactions = false;
          this.transactionData = [];

          try {
            // Get complete property data based on active tab
            let data;

            if (this.activeTab === 'HDB') {
              // Get HDB property data
              const response = await this.#hdbController.getById({ id: property._id });
              data = response.data;

              // Fetch HDB transaction data using coordinates
              console.log('Fetching HDB transaction data for town marker...');
              const hdbResponse = await this.#hdbController.extractHDBTransactionData({
                latitude: Number(latitude),
                longitude: Number(longitude),
              });

              console.log('HDB transaction data response:', hdbResponse);

              if (hdbResponse.success && hdbResponse.data && hdbResponse.data.length > 0) {
                console.log('HDB transaction data found:', hdbResponse.data);
                this.transactionData = hdbResponse.data.slice(0, 10).map((transaction) => ({
                  date: transaction.month,
                  flatType: transaction.flat_type || '4-Room',
                  floorLevel: transaction.storey_range,
                  area: Math.round(transaction.floor_area_sqm * 10.764), // Convert sqm to sqft
                  price: transaction.resale_price,
                  psf: Math.round(transaction.resale_price / (transaction.floor_area_sqm * 10.764)),
                }));
                this.hasTransactions = true;
                console.log('Transaction data:', this.transactionData);
              }
            } else if (this.activeTab === 'Condo') {
              // Get Condo property data
              const response = await this.#condoController.getById({ id: property._id });
              data = response.data;

              // Fetch Condo transaction data using coordinates
              try {
                const condoResponse = await this.#condoController.getLatestTransactionsByLocation({
                  lat: latitude.toString(),
                  long: longitude.toString(),
                });

                console.log('Condo transaction response:', condoResponse);
                if (condoResponse.success && condoResponse.data && condoResponse.data.length > 0) {
                  this.transactionData = condoResponse.data.map((transaction) => ({
                    date: transaction.contract_date,
                    flatType: 'Condo',
                    floorLevel: transaction.floor_range,
                    area: Math.round(transaction.area_sqm),
                    price: transaction.price,
                    psf: Math.round(transaction.price / (transaction.area_sqm * 10.764)),
                  }));
                  this.hasTransactions = true;
                }
              } catch (error) {
                console.error('Error fetching condo transaction data:', error);
              }
            }

            // Show property details modal
            if (data) {
              this.togglePropertyModal(data);
            }
          } catch (error) {
            console.error('Error fetching property details:', error);
            this.#snackBarService.error('Error fetching property details');
          }
        });

        this.markers.push(marker);
      }
    });
  }

  /**
   * Center the map on a property when its sales listing card is clicked
   * @param property The property to center the map on
   */
  centerMapOnProperty(property: any): void {
    if (property && property.latitude && property.longitude) {
      // Center the map on the property
      this.googleMap.setCenter({
        lat: property.latitude,
        lng: property.longitude,
      });

      // Zoom in for better visibility
      this.googleMap.setZoom(16);

      // Find the marker for this property and trigger its click event
      this.markers.forEach((marker) => {
        const position = marker.getPosition();
        if (
          position &&
          position.lat() === property.latitude &&
          position.lng() === property.longitude
        ) {
          // Simulate a click on the marker
          google.maps.event.trigger(marker, 'click');
        }
      });
    }
  }

  removeRecentSearch(searchResult: any, event: MouseEvent): void {
    event.stopPropagation();
    this.recentSearches = this.recentSearches.filter(
      (search) =>
        !(search.LATITUDE === searchResult.LATITUDE && search.LONGITUDE === searchResult.LONGITUDE),
    );
    this.clearAllMarkersAndCircles();
    localStorage.setItem('recentSearches', JSON.stringify(this.recentSearches));
  }
}
