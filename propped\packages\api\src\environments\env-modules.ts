import { escapeId, escape as mysqlEscape } from 'mysql2';
// import S3 from 'aws-sdk/clients/s3';
// require('aws-sdk/lib/maintenance_mode_message').suppress = true;

// biome-ignore lint/complexity/noStaticOnlyClass: <explanation>
export class DBI {
  static JSON = 'json' as any;
  static isMySQL = true;
  static bool = (x: any) => (x ? 1 : 0);
  static escapeId = escapeId;
  static escape = mysqlEscape;
  static g = (col: string, path: string, _raw?: boolean) => `JSON_EXTRACT(${col}, '$.${path}')`;
  static jsonVal = (val: string) => `CAST('${val}' AS JSON)`;
}
// export class DBI {
//   static JSON = 'jsonb' as any;
//   static isMySQL = false;
//   static bool = (x: any) => !!x;
//   static wrap = (s: string) => `"${s}"`;
//   // static q = (s: string) => s.replaceAll('`', '"');
//   static g = (col: string, path: string, raw?: boolean) => {
//     const route = `{"${path.split('.').join('","')}"}`;
//     return `${col} ${raw ? '#>' : '#>>'} '${route}'`;
//   };
//   static jsonVal = (val: string) => `'${val}'::jsonb`;
// }

export const getSES = (_setting: any): any => {
  //
};

// export const getS3V2 = () => S3;
export const getS3V2 = (): any => {
  //
};
