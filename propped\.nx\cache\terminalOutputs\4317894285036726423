[1m[33mComponent HMR has been enabled, see https://angular.dev/hmr for more info.[39m[22m
[33m❯[39m Building...
[32m✔[39m Building...
[37m[1mInitial chunk files[22m[2m | [22m[1mNames[22m                          [2m | [22m [1mRaw size[22m[39m
[37m[32mstyles.css[39m[37m         [2m | [22m[2mstyles[22m                         [2m | [22m[36m424.48 kB[39m[37m[2m | [22m[39m
[37m[32mpolyfills.js[39m[37m       [2m | [22m[2mpolyfills[22m                      [2m | [22m [36m89.80 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-XBDRPN26.js[39m[37m  [2m | [22m[2m-[22m                              [2m | [22m  [36m3.78 kB[39m[37m[2m | [22m[39m
[37m[32mmain.js[39m[37m            [2m | [22m[2mmain[22m                           [2m | [22m[36m396 bytes[39m[37m[2m | [22m[39m
[37m[39m
[37m[1m [22m                  [2m | [22m[1mInitial total[22m                  [2m | [22m[1m518.46 kB[22m[39m
[37m[39m
[37m[1mLazy chunk files[22m   [2m | [22m[1mNames[22m                          [2m | [22m [1mRaw size[22m[39m
[37m[32mchunk-EDSVLUGT.js[39m[37m  [2m | [22m[2mmap-box-overview-component[22m     [2m | [22m[36m352.17 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-HBYKPW5K.js[39m[37m  [2m | [22m[2moverview-routes[22m                [2m | [22m[36m228.15 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-6HWV4D2G.js[39m[37m  [2m | [22m[2m-[22m                              [2m | [22m[36m206.28 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-NOZ5QGO5.js[39m[37m  [2m | [22m[2mproperty-map-component[22m         [2m | [22m[36m185.84 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-OS24SFAY.js[39m[37m  [2m | [22m[2mhdb-transaction-routes[22m         [2m | [22m[36m156.13 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-PAWOSGZF.js[39m[37m  [2m | [22m[2mcondo-transaction-routes[22m       [2m | [22m[36m150.53 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-6P6ZNAAM.js[39m[37m  [2m | [22m[2mbootstrap[22m                      [2m | [22m[36m116.41 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-7RWEPKQL.js[39m[37m  [2m | [22m[2mseller-preview-component[22m       [2m | [22m[36m114.14 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-2SWBOTWF.js[39m[37m  [2m | [22m[2mpreview-listing-component[22m      [2m | [22m [36m64.29 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-ADEY77IT.js[39m[37m  [2m | [22[39m[37mm[2m-[22m                              [2m | [22m [36m62.90 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-TPPQQWQD.js[39m[37m  [2m | [22m[2mhdb-resale-trend-component[22m     [2m | [22m [36m56.32 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-OKAQIVRP.js[39m[37m  [2m | [22m[2mselling-price-component[22m        [2m | [22m [36m48.86 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-N4IGM46E.js[39m[37m  [2m | [22m[2mmake-offer-buyer-component[22m     [2m | [22m [36m42.91 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-D2SQPVAP.js[39m[37m  [2m | [22m[2mproperty-address-component[22m     [2m | [22m [36m39.93 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-RZK7L4Q2.js[39m[37m  [2m | [22m[2moffer-price-estimator-component[22m[2m | [22m [36m36.04 kB[39m[37m[2m | [22m[39m
[37m[2m...and 32 more lazy chunks files. Use "--verbose" to show all the files.[22m[39m
[37m[39m
[37mApplication bundle generation complete. [22.807 seconds][39m
[37m[39m
[37mWatch mode enabled. Watching for file changes...[39m
[37mNOTE: Raw file sizes do not reflect development server per-request transformations.[39m
  [32m➜[39m  [1mLocal[22m:   [36mhttp://localhost:[1m4200[22m/[39m
[2m[32m  ➜[39m[22m[2m  press [22m[1mh + enter[22m[2m to show help[22m
[33m❯[39m Changes detected. Rebuilding...
[32m✔[39m Changes detected. Rebuilding...
[37m[1mInitial chunk files[22m[2m | [22m[1mNames[22m                          [2m | [22m [1mRaw size[22m[39m
[37m[32mstyles.css[39m[37m         [2m | [22m[2mstyles[22m                         [2m | [22m[36m424.48 kB[39m[37m[2m | [22m[39m
[37m[32mpolyfills.js[39m[37m       [2m | [22m[2mpolyfills[22m                      [2m | [22m [36m89.80 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-XBDRPN26.js[39m[37m  [2m | [22m[2m-[22m                              [2m | [22m  [36m3.78 kB[39m[37m[2m | [22m[39m
[37m[32mmain.js[39m[37m            [2m | [22m[2mmain[22m                           [2m | [22m[36m396 bytes[39m[37m[2m | [22m[39m
[37m[39m
[37m[1m [22m                  [2m | [22m[1mInitial total[22m                  [2m | [22m[1m518.46 kB[22m[39m
[37m[39m
[37m[1mLazy chunk files[22m   [2m | [22m[1mNames[22m                          [2m | [22m [1mRaw size[22m[39m
[37m[32mchunk-EDSVLUGT.js[39m[37m  [2m | [22m[2mmap-box-overview-component[22m     [2m | [22m[36m352.17 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-HBYKPW5K.js[39m[37m  [2m | [22m[2moverview-routes[22m                [2m | [22m[36m228.15 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-6HWV4D2G.js[39m[37m  [2m | [22m[2m-[22m                              [2m | [22m[36m206.28 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-NOZ5QGO5.js[39m[37m  [2m | [22m[2mproperty-map-component[22m         [2m | [22m[36m185.84 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-OS24SFAY.js[39m[37m  [2m | [22m[2mhdb-transaction-routes[22m         [2m | [22m[36m156.13 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-PAWOSGZF.js[39m[37m  [2m | [22m[2mcondo-transaction-routes[22m       [2m | [22m[36m150.53 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-6P6ZNAAM.js[39m[37m  [2m | [22m[2mbootstrap[22m                      [2m | [22m[36m116.41 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-7RWEPKQL.js[39m[37m  [2m | [22m[2mseller-preview-component[22m       [2m | [22m[36m114.14 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-2SWBOTWF.js[39m[37m  [2m | [22m[2mpreview-listing-component[22m      [2m | [22m [36m64.29 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-ADEY77IT.js[39m[37m  [2m | [22[39m[37mm[2m-[22m                              [2m | [22m [36m62.90 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-TPPQQWQD.js[39m[37m  [2m | [22m[2mhdb-resale-trend-component[22m     [2m | [22m [36m56.32 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-OKAQIVRP.js[39m[37m  [2m | [22m[2mselling-price-component[22m        [2m | [22m [36m48.86 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-N4IGM46E.js[39m[37m  [2m | [22m[2mmake-offer-buyer-component[22m     [2m | [22m [36m42.91 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-D2SQPVAP.js[39m[37m  [2m | [22m[2mproperty-address-component[22m     [2m | [22m [36m39.93 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-RZK7L4Q2.js[39m[37m  [2m | [22m[2moffer-price-estimator-component[22m[2m | [22m [36m36.04 kB[39m[37m[2m | [22m[39m
[37m[2m...and 32 more lazy chunks files. Use "--verbose" to show all the files.[22m[39m
[37m[39m
[37mApplication bundle generation complete. [9.314 seconds][39m
[37m[39m
[33m❯[39m Changes detected. Rebuilding...
[32m✔[39m Changes detected. Rebuilding...
[37m[1mInitial chunk files[22m[2m | [22m[1mNames[22m                          [2m | [22m [1mRaw size[22m[39m
[37m[32mstyles.css[39m[37m         [2m | [22m[2mstyles[22m                         [2m | [22m[36m424.48 kB[39m[37m[2m | [22m[39m
[37m[32mpolyfills.js[39m[37m       [2m | [22m[2mpolyfills[22m                      [2m | [22m [36m89.80 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-XBDRPN26.js[39m[37m  [2m | [22m[2m-[22m                              [2m | [22m  [36m3.78 kB[39m[37m[2m | [22m[39m
[37m[32mmain.js[39m[37m            [2m | [22m[2mmain[22m                           [2m | [22m[36m396 bytes[39m[37m[2m | [22m[39m
[37m[39m
[37m[1m [22m                  [2m | [22m[1mInitial total[22m                  [2m | [22m[1m518.46 kB[22m[39m
[37m[39m
[37m[1mLazy chunk files[22m   [2m | [22m[1mNames[22m                          [2m | [22m [1mRaw size[22m[39m
[37m[32mchunk-EDSVLUGT.js[39m[37m  [2m | [22m[2mmap-box-overview-component[22m     [2m | [22m[36m352.17 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-HBYKPW5K.js[39m[37m  [2m | [22m[2moverview-routes[22m                [2m | [22m[36m228.15 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-6HWV4D2G.js[39m[37m  [2m | [22m[2m-[22m                              [2m | [22m[36m206.28 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-NOZ5QGO5.js[39m[37m  [2m | [22m[2mproperty-map-component[22m         [2m | [22m[36m185.84 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-OS24SFAY.js[39m[37m  [2m | [22m[2mhdb-transaction-routes[22m         [2m | [22m[36m156.13 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-PAWOSGZF.js[39m[37m  [2m | [22m[2mcondo-transaction-routes[22m       [2m | [22m[36m150.53 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-6P6ZNAAM.js[39m[37m  [2m | [22m[2mbootstrap[22m                      [2m | [22m[36m116.41 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-7RWEPKQL.js[39m[37m  [2m | [22m[2mseller-preview-component[22m       [2m | [22m[36m114.14 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-2SWBOTWF.js[39m[37m  [2m | [22m[2mpreview-listing-component[22m      [2m | [22m [36m64.29 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-ADEY77IT.js[39m[37m  [2m | [22[39m[37mm[2m-[22m                              [2m | [22m [36m62.90 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-TPPQQWQD.js[39m[37m  [2m | [22m[2mhdb-resale-trend-component[22m     [2m | [22m [36m56.32 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-OKAQIVRP.js[39m[37m  [2m | [22m[2mselling-price-component[22m        [2m | [22m [36m48.86 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-N4IGM46E.js[39m[37m  [2m | [22m[2mmake-offer-buyer-component[22m     [2m | [22m [36m42.91 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-D2SQPVAP.js[39m[37m  [2m | [22m[2mproperty-address-component[22m     [2m | [22m [36m39.93 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-RZK7L4Q2.js[39m[37m  [2m | [22m[2moffer-price-estimator-component[22m[2m | [22m [36m36.04 kB[39m[37m[2m | [22m[39m
[37m[2m...and 32 more lazy chunks files. Use "--verbose" to show all the files.[22m[39m
[37m[39m
[37mApplication bundle generation complete. [10.882 seconds][39m
[37m[39m
[33m❯[39m Changes detected. Rebuilding...
[32m✔[39m Changes detected. Rebuilding...
[37m[1mInitial chunk files[22m[2m | [22m[1mNames[22m                          [2m | [22m [1mRaw size[22m[39m
[37m[32mstyles.css[39m[37m         [2m | [22m[2mstyles[22m                         [2m | [22m[36m424.48 kB[39m[37m[2m | [22m[39m
[37m[32mpolyfills.js[39m[37m       [2m | [22m[2mpolyfills[22m                      [2m | [22m [36m89.80 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-XBDRPN26.js[39m[37m  [2m | [22m[2m-[22m                              [2m | [22m  [36m3.78 kB[39m[37m[2m | [22m[39m
[37m[32mmain.js[39m[37m            [2m | [22m[2mmain[22m                           [2m | [22m[36m396 bytes[39m[37m[2m | [22m[39m
[37m[39m
[37m[1m [22m                  [2m | [22m[1mInitial total[22m                  [2m | [22m[1m518.46 kB[22m[39m
[37m[39m
[37m[1mLazy chunk files[22m   [2m | [22m[1mNames[22m                          [2m | [22m [1mRaw size[22m[39m
[37m[32mchunk-EDSVLUGT.js[39m[37m  [2m | [22m[2mmap-box-overview-component[22m     [2m | [22m[36m352.17 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-HBYKPW5K.js[39m[37m  [2m | [22m[2moverview-routes[22m                [2m | [22m[36m228.15 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-6HWV4D2G.js[39m[37m  [2m | [22m[2m-[22m                              [2m | [22m[36m206.28 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-NOZ5QGO5.js[39m[37m  [2m | [22m[2mproperty-map-component[22m         [2m | [22m[36m185.84 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-OS24SFAY.js[39m[37m  [2m | [22m[2mhdb-transaction-routes[22m         [2m | [22m[36m156.13 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-PAWOSGZF.js[39m[37m  [2m | [22m[2mcondo-transaction-routes[22m       [2m | [22m[36m150.53 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-6P6ZNAAM.js[39m[37m  [2m | [22m[2mbootstrap[22m                      [2m | [22m[36m116.41 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-7RWEPKQL.js[39m[37m  [2m | [22m[2mseller-preview-component[22m       [2m | [22m[36m114.14 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-2SWBOTWF.js[39m[37m  [2m | [22m[2mpreview-listing-component[22m      [2m | [22m [36m64.29 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-ADEY77IT.js[39m[37m  [2m | [22[39m[37mm[2m-[22m                              [2m | [22m [36m62.90 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-TPPQQWQD.js[39m[37m  [2m | [22m[2mhdb-resale-trend-component[22m     [2m | [22m [36m56.32 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-OKAQIVRP.js[39m[37m  [2m | [22m[2mselling-price-component[22m        [2m | [22m [36m48.86 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-N4IGM46E.js[39m[37m  [2m | [22m[2mmake-offer-buyer-component[22m     [2m | [22m [36m42.91 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-D2SQPVAP.js[39m[37m  [2m | [22m[2mproperty-address-component[22m     [2m | [22m [36m39.93 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-RZK7L4Q2.js[39m[37m  [2m | [22m[2moffer-price-estimator-component[22m[2m | [22m [36m36.04 kB[39m[37m[2m | [22m[39m
[37m[2m...and 32 more lazy chunks files. Use "--verbose" to show all the files.[22m[39m
[37m[39m
[37mApplication bundle generation complete. [8.498 seconds][39m
[37m[39m
[33m❯[39m Changes detected. Rebuilding...
[32m✔[39m Changes detected. Rebuilding...
[37m[1mInitial chunk files[22m[2m | [22m[1mNames[22m                     [2m | [22m [1mRaw size[22m[39m
[37m[32mmain.js[39m[37m            [2m | [22m[2mmain[22m                      [2m | [22m[36m396 bytes[39m[37m[2m | [22m[39m
[37m[39m
[37m[1mLazy chunk files[22m   [2m | [22m[1mNames[22m                     [2m | [22m [1mRaw size[22m[39m
[37m[32mchunk-X7LHOFIT.js[39m[37m  [2m | [22m[2mbootstrap[22m                 [2m | [22m[36m116.41 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-O4FSSTCT.js[39m[37m  [2m | [22m[2mhdb-resale-trend-component[22m[2m | [22m [36m56.33 kB[39m[37m[2m | [22m[39m
[37m[39m
[37mApplication bundle generation complete. [33.729 seconds][39m
[37m[39m
[37mPage reload sent to client(s).[39m
[33m❯[39m Changes detected. Rebuilding...
[32m✔[39m Changes detected. Rebuilding...
[37m[1mInitial chunk files[22m[2m | [22m[1mNames[22m                          [2m | [22m [1mRaw size[22m[39m
[37m[32mstyles.css[39m[37m         [2m | [22m[2mstyles[22m                         [2m | [22m[36m424.48 kB[39m[37m[2m | [22m[39m
[37m[32mpolyfills.js[39m[37m       [2m | [22m[2mpolyfills[22m                      [2m | [22m [36m89.80 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-XBDRPN26.js[39m[37m  [2m | [22m[2m-[22m                              [2m | [22m  [36m3.78 kB[39m[37m[2m | [22m[39m
[37m[32mmain.js[39m[37m            [2m | [22m[2mmain[22m                           [2m | [22m[36m396 bytes[39m[37m[2m | [22m[39m
[37m[39m
[37m[1m [22m                  [2m | [22m[1mInitial total[22m                  [2m | [22m[1m518.46 kB[22m[39m
[37m[39m
[37m[1mLazy chunk files[22m   [2m | [22m[1mNames[22m                          [2m | [22m [1mRaw size[22m[39m
[37m[32mchunk-EDSVLUGT.js[39m[37m  [2m | [22m[2mmap-box-overview-component[22m     [2m | [22m[36m352.17 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-HBYKPW5K.js[39m[37m  [2m | [22m[2moverview-routes[22m                [2m | [22m[36m228.15 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-6HWV4D2G.js[39m[37m  [2m | [22m[2m-[22m                              [2m | [22m[36m206.28 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-NOZ5QGO5.js[39m[37m  [2m | [22m[2mproperty-map-component[22m         [2m | [22m[36m185.84 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-OS24SFAY.js[39m[37m  [2m | [22m[2mhdb-transaction-routes[22m         [2m | [22m[36m156.13 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-PAWOSGZF.js[39m[37m  [2m | [22m[2mcondo-transaction-routes[22m       [2m | [22m[36m150.53 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-X7LHOFIT.js[39m[37m  [2m | [22m[2mbootstrap[22m                      [2m | [22m[36m116.41 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-7RWEPKQL.js[39m[37m  [2m | [22m[2mseller-preview-component[22m       [2m | [22m[36m114.14 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-2SWBOTWF.js[39m[37m  [2m | [22m[2mpreview-listing-component[22m      [2m | [22m [36m64.29 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-ADEY77IT.js[39m[37m  [2m | [22[39m[37mm[2m-[22m                              [2m | [22m [36m62.90 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-O4FSSTCT.js[39m[37m  [2m | [22m[2mhdb-resale-trend-component[22m     [2m | [22m [36m56.33 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-OKAQIVRP.js[39m[37m  [2m | [22m[2mselling-price-component[22m        [2m | [22m [36m48.86 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-N4IGM46E.js[39m[37m  [2m | [22m[2mmake-offer-buyer-component[22m     [2m | [22m [36m42.91 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-D2SQPVAP.js[39m[37m  [2m | [22m[2mproperty-address-component[22m     [2m | [22m [36m39.93 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-RZK7L4Q2.js[39m[37m  [2m | [22m[2moffer-price-estimator-component[22m[2m | [22m [36m36.04 kB[39m[37m[2m | [22m[39m
[37m[2m...and 32 more lazy chunks files. Use "--verbose" to show all the files.[22m[39m
[37m[39m
[37mApplication bundle generation complete. [10.728 seconds][39m
[37m[39m
[33m❯[39m Changes detected. Rebuilding...
[32m✔[39m Changes detected. Rebuilding...
[37m[1mInitial chunk files[22m[2m | [22m[1mNames[22m                     [2m | [22m [1mRaw size[22m[39m
[37m[32mmain.js[39m[37m            [2m | [22m[2mmain[22m                      [2m | [22m[36m396 bytes[39m[37m[2m | [22m[39m
[37m[39m
[37m[1mLazy chunk files[22m   [2m | [22m[1mNames[22m                     [2m | [22m [1mRaw size[22m[39m
[37m[32mchunk-73Y3IOJD.js[39m[37m  [2m | [22m[2mbootstrap[22m                 [2m | [22m[36m116.41 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-ZXBENVYO.js[39m[37m  [2m | [22m[2mhdb-resale-trend-component[22m[2m | [22m [36m26.30 kB[39m[37m[2m | [22m[39m
[37m[39m
[37mApplication bundle generation complete. [13.072 seconds][39m
[37m[39m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mTS-998113: SVGIconComponent is not used within the template of HdbResaleTrendComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:21:39:[39m[22m
[1m[33m[37m      21 │ ...monModule, FormsModule, [32mSVGIconComponent[37m, ResaleTrendChartCompo...[39m[22m
[1m[33m         ╵                            [32m~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mTS-998113: ResaleTrendChartComponent is not used within the template of HdbResaleTrendComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:21:57:[39m[22m
[1m[33m[37m      21 │ ...Module, FormsModule, SVGIconComponent, [32mResaleTrendChartComponent[37m],[39m[22m
[1m[33m         ╵                                           [32m~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[37mComponent update sent to client(s).[39m
[33m❯[39m Changes detected. Rebuilding...
[32m✔[39m Changes detected. Rebuilding...
[37m[1mInitial chunk files[22m[2m | [22m[1mNames[22m                     [2m | [22m [1mRaw size[22m[39m
[37m[32mmain.js[39m[37m            [2m | [22m[2mmain[22m                      [2m | [22m[36m396 bytes[39m[37m[2m | [22m[39m
[37m[39m
[37m[1mLazy chunk files[22m   [2m | [22m[1mNames[22m                     [2m | [22m [1mRaw size[22m[39m
[37m[32mchunk-6P6ZNAAM.js[39m[37m  [2m | [22m[2mbootstrap[22m                 [2m | [22m[36m116.41 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-TPPQQWQD.js[39m[37m  [2m | [22m[2mhdb-resale-trend-component[22m[2m | [22m [36m56.32 kB[39m[37m[2m | [22m[39m
[37m[39m
[37mApplication bundle generation complete. [18.582 seconds][39m
[37m[39m
[37mPage reload sent to client(s).[39m
[33m❯[39m Changes detected. Rebuilding...
[32m✔[39m Changes detected. Rebuilding...
[37m[1mInitial chunk files[22m[2m | [22m[1mNames[22m                          [2m | [22m [1mRaw size[22m[39m
[37m[32mstyles.css[39m[37m         [2m | [22m[2mstyles[22m                         [2m | [22m[36m424.48 kB[39m[37m[2m | [22m[39m
[37m[32mpolyfills.js[39m[37m       [2m | [22m[2mpolyfills[22m                      [2m | [22m [36m89.80 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-XBDRPN26.js[39m[37m  [2m | [22m[2m-[22m                              [2m | [22m  [36m3.78 kB[39m[37m[2m | [22m[39m
[37m[32mmain.js[39m[37m            [2m | [22m[2mmain[22m                           [2m | [22m[36m396 bytes[39m[37m[2m | [22m[39m
[37m[39m
[37m[1m [22m                  [2m | [22m[1mInitial total[22m                  [2m | [22m[1m518.46 kB[22m[39m
[37m[39m
[37m[1mLazy chunk files[22m   [2m | [22m[1mNames[22m                          [2m | [22m [1mRaw size[22m[39m
[37m[32mchunk-EDSVLUGT.js[39m[37m  [2m | [22m[2mmap-box-overview-component[22m     [2m | [22m[36m352.17 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-HBYKPW5K.js[39m[37m  [2m | [22m[2moverview-routes[22m                [2m | [22m[36m228.15 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-6HWV4D2G.js[39m[37m  [2m | [22m[2m-[22m                              [2m | [22m[36m206.28 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-NOZ5QGO5.js[39m[37m  [2m | [22m[2mproperty-map-component[22m         [2m | [22m[36m185.84 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-OS24SFAY.js[39m[37m  [2m | [22m[2mhdb-transaction-routes[22m         [2m | [22m[36m156.13 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-PAWOSGZF.js[39m[37m  [2m | [22m[2mcondo-transaction-routes[22m       [2m | [22m[36m150.53 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-6P6ZNAAM.js[39m[37m  [2m | [22m[2mbootstrap[22m                      [2m | [22m[36m116.41 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-7RWEPKQL.js[39m[37m  [2m | [22m[2mseller-preview-component[22m       [2m | [22m[36m114.14 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-2SWBOTWF.js[39m[37m  [2m | [22m[2mpreview-listing-component[22m      [2m | [22m [36m64.29 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-ADEY77IT.js[39m[37m  [2m | [22[39m[37mm[2m-[22m                              [2m | [22m [36m62.90 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-TPPQQWQD.js[39m[37m  [2m | [22m[2mhdb-resale-trend-component[22m     [2m | [22m [36m56.32 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-OKAQIVRP.js[39m[37m  [2m | [22m[2mselling-price-component[22m        [2m | [22m [36m48.86 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-N4IGM46E.js[39m[37m  [2m | [22m[2mmake-offer-buyer-component[22m     [2m | [22m [36m42.91 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-D2SQPVAP.js[39m[37m  [2m | [22m[2mproperty-address-component[22m     [2m | [22m [36m39.93 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-RZK7L4Q2.js[39m[37m  [2m | [22m[2moffer-price-estimator-component[22m[2m | [22m [36m36.04 kB[39m[37m[2m | [22m[39m
[37m[2m...and 32 more lazy chunks files. Use "--verbose" to show all the files.[22m[39m
[37m[39m
[37mApplication bundle generation complete. [15.420 seconds][39m
[37m[39m
[33m❯[39m Changes detected. Rebuilding...
[32m✔[39m Changes detected. Rebuilding...
[37m[1mInitial chunk files[22m[2m | [22m[1mNames[22m                          [2m | [22m [1mRaw size[22m[39m
[37m[32mstyles.css[39m[37m         [2m | [22m[2mstyles[22m                         [2m | [22m[36m424.48 kB[39m[37m[2m | [22m[39m
[37m[32mpolyfills.js[39m[37m       [2m | [22m[2mpolyfills[22m                      [2m | [22m [36m89.80 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-XBDRPN26.js[39m[37m  [2m | [22m[2m-[22m                              [2m | [22m  [36m3.78 kB[39m[37m[2m | [22m[39m
[37m[32mmain.js[39m[37m            [2m | [22m[2mmain[22m                           [2m | [22m[36m396 bytes[39m[37m[2m | [22m[39m
[37m[39m
[37m[1m [22m                  [2m | [22m[1mInitial total[22m                  [2m | [22m[1m518.46 kB[22m[39m
[37m[39m
[37m[1mLazy chunk files[22m   [2m | [22m[1mNames[22m                          [2m | [22m [1mRaw size[22m[39m
[37m[32mchunk-EDSVLUGT.js[39m[37m  [2m | [22m[2mmap-box-overview-component[22m     [2m | [22m[36m352.17 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-HBYKPW5K.js[39m[37m  [2m | [22m[2moverview-routes[22m                [2m | [22m[36m228.15 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-6HWV4D2G.js[39m[37m  [2m | [22m[2m-[22m                              [2m | [22m[36m206.28 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-NOZ5QGO5.js[39m[37m  [2m | [22m[2mproperty-map-component[22m         [2m | [22m[36m185.84 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-OS24SFAY.js[39m[37m  [2m | [22m[2mhdb-transaction-routes[22m         [2m | [22m[36m156.13 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-PAWOSGZF.js[39m[37m  [2m | [22m[2mcondo-transaction-routes[22m       [2m | [22m[36m150.53 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-6P6ZNAAM.js[39m[37m  [2m | [22m[2mbootstrap[22m                      [2m | [22m[36m116.41 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-7RWEPKQL.js[39m[37m  [2m | [22m[2mseller-preview-component[22m       [2m | [22m[36m114.14 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-2SWBOTWF.js[39m[37m  [2m | [22m[2mpreview-listing-component[22m      [2m | [22m [36m64.29 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-ADEY77IT.js[39m[37m  [2m | [22[39m[37mm[2m-[22m                              [2m | [22m [36m62.90 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-TPPQQWQD.js[39m[37m  [2m | [22m[2mhdb-resale-trend-component[22m     [2m | [22m [36m56.32 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-OKAQIVRP.js[39m[37m  [2m | [22m[2mselling-price-component[22m        [2m | [22m [36m48.86 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-N4IGM46E.js[39m[37m  [2m | [22m[2mmake-offer-buyer-component[22m     [2m | [22m [36m42.91 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-D2SQPVAP.js[39m[37m  [2m | [22m[2mproperty-address-component[22m     [2m | [22m [36m39.93 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-RZK7L4Q2.js[39m[37m  [2m | [22m[2moffer-price-estimator-component[22m[2m | [22m [36m36.04 kB[39m[37m[2m | [22m[39m
[37m[2m...and 32 more lazy chunks files. Use "--verbose" to show all the files.[22m[39m
[37m[39m
[37mApplication bundle generation complete. [5.961 seconds][39m
[37m[39m
[33m❯[39m Changes detected. Rebuilding...
[32m✔[39m Changes detected. Rebuilding...
[37m[1mInitial chunk files[22m[2m | [22m[1mNames[22m                          [2m | [22m [1mRaw size[22m[39m
[37m[32mstyles.css[39m[37m         [2m | [22m[2mstyles[22m                         [2m | [22m[36m424.48 kB[39m[37m[2m | [22m[39m
[37m[32mpolyfills.js[39m[37m       [2m | [22m[2mpolyfills[22m                      [2m | [22m [36m89.80 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-XBDRPN26.js[39m[37m  [2m | [22m[2m-[22m                              [2m | [22m  [36m3.78 kB[39m[37m[2m | [22m[39m
[37m[32mmain.js[39m[37m            [2m | [22m[2mmain[22m                           [2m | [22m[36m396 bytes[39m[37m[2m | [22m[39m
[37m[39m
[37m[1m [22m                  [2m | [22m[1mInitial total[22m                  [2m | [22m[1m518.46 kB[22m[39m
[37m[39m
[37m[1mLazy chunk files[22m   [2m | [22m[1mNames[22m                          [2m | [22m [1mRaw size[22m[39m
[37m[32mchunk-EDSVLUGT.js[39m[37m  [2m | [22m[2mmap-box-overview-component[22m     [2m | [22m[36m352.17 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-HBYKPW5K.js[39m[37m  [2m | [22m[2moverview-routes[22m                [2m | [22m[36m228.15 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-6HWV4D2G.js[39m[37m  [2m | [22m[2m-[22m                              [2m | [22m[36m206.28 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-NOZ5QGO5.js[39m[37m  [2m | [22m[2mproperty-map-component[22m         [2m | [22m[36m185.84 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-OS24SFAY.js[39m[37m  [2m | [22m[2mhdb-transaction-routes[22m         [2m | [22m[36m156.13 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-PAWOSGZF.js[39m[37m  [2m | [22m[2mcondo-transaction-routes[22m       [2m | [22m[36m150.53 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-6P6ZNAAM.js[39m[37m  [2m | [22m[2mbootstrap[22m                      [2m | [22m[36m116.41 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-7RWEPKQL.js[39m[37m  [2m | [22m[2mseller-preview-component[22m       [2m | [22m[36m114.14 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-2SWBOTWF.js[39m[37m  [2m | [22m[2mpreview-listing-component[22m      [2m | [22m [36m64.29 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-ADEY77IT.js[39m[37m  [2m | [22[39m[37mm[2m-[22m                              [2m | [22m [36m62.90 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-TPPQQWQD.js[39m[37m  [2m | [22m[2mhdb-resale-trend-component[22m     [2m | [22m [36m56.32 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-OKAQIVRP.js[39m[37m  [2m | [22m[2mselling-price-component[22m        [2m | [22m [36m48.86 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-N4IGM46E.js[39m[37m  [2m | [22m[2mmake-offer-buyer-component[22m     [2m | [22m [36m42.91 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-D2SQPVAP.js[39m[37m  [2m | [22m[2mproperty-address-component[22m     [2m | [22m [36m39.93 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-RZK7L4Q2.js[39m[37m  [2m | [22m[2moffer-price-estimator-component[22m[2m | [22m [36m36.04 kB[39m[37m[2m | [22m[39m
[37m[2m...and 32 more lazy chunks files. Use "--verbose" to show all the files.[22m[39m
[37m[39m
[37mApplication bundle generation complete. [5.144 seconds][39m
[37m[39m
[33m❯[39m Changes detected. Rebuilding...
[32m✔[39m Changes detected. Rebuilding...
[37mApplication bundle generation failed. [4.861 seconds][39m
[37m[39m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mTS-998113: SVGIconComponent is not used within the template of HdbResaleTrendComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:21:39:[39m[22m
[1m[33m[37m      21 │ ...monModule, FormsModule, [32mSVGIconComponent[37m, ResaleTrendChartCompo...[39m[22m
[1m[33m         ╵                            [32m~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mTS-998113: ResaleTrendChartComponent is not used within the template of HdbResaleTrendComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:21:57:[39m[22m
[1m[33m[37m      21 │ ...Module, FormsModule, SVGIconComponent, [32mResaleTrendChartComponent[37m],[39m[22m
[1m[33m         ╵                                           [32m~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-icon' is not a known element:[39m[22m
[1m[31m1. If 'mat-icon' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-icon' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:3:4:[39m[22m
[1m[31m[37m      3 │     [32m<mat-icon>[37mhome</mat-icon>[39m[22m
[1m[31m        ╵     [32m~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-form-field' is not a known element:[39m[22m
[1m[31m1. If 'mat-form-field' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-form-field' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:11:4:[39m[22m
[1m[31m[37m      11 │     [32m<mat-form-field appearance="outline" style="min-width: 180px;">[37m[39m[22m
[1m[31m         ╵     [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-label' is not a known element:[39m[22m
[1m[31m1. If[39m[22m[33m❯[39m Changes detected. Rebuilding...
[1m[31m 'mat-label' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-label' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:12:6:[39m[22m
[1m[31m[37m      12 │       [32m<mat-label>[37mTown</mat-label>[39m[22m
[1m[31m         ╵       [32m~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-select' is not a known element:[39m[22m
[1m[31m1. If 'mat-select' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-select' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:13:6:[39m[22m
[1m[31m[37m      13 │       [32m<mat-select value="All Towns">[37m[39m[22m
[1m[31m         ╵       [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-option' is not a known element:[39m[22m
[1m[31m1. If 'mat-option' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-option' is a Web Component [39m[22m[1m[31mthen add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:14:8:[39m[22m
[1m[31m[37m      14 │         [32m<mat-option>[37mAll Towns</mat-option>[39m[22m
[1m[31m         ╵         [32m~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-form-field' is not a known element:[39m[22m
[1m[31m1. If 'mat-form-field' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-form-field' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:17:4:[39m[22m
[1m[31m[37m      17 │     [32m<mat-form-field appearance="outline" style="min-width: 180px;">[37m[39m[22m
[1m[31m         ╵     [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-label' is not a known element:[39m[22m
[1m[31m1. If 'mat-label' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-label' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this c[39m[22m[1m[31momponent to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:18:6:[39m[22m
[1m[31m[37m      18 │       [32m<mat-label>[37mStreet</mat-label>[39m[22m
[1m[31m         ╵       [32m~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-select' is not a known element:[39m[22m
[1m[31m1. If 'mat-select' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-select' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:19:6:[39m[22m
[1m[31m[37m      19 │       [32m<mat-select value="All Streets">[37m[39m[22m
[1m[31m         ╵       [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-option' is not a known element:[39m[22m
[1m[31m1. If 'mat-option' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-option' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dumm[39m[22m[1m[31my.component.html:20:8:[39m[22m
[1m[31m[37m      20 │         [32m<mat-option>[37mAll Streets</mat-option>[39m[22m
[1m[31m         ╵         [32m~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-form-field' is not a known element:[39m[22m
[1m[31m1. If 'mat-form-field' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-form-field' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:23:4:[39m[22m
[1m[31m[37m      23 │     [32m<mat-form-field appearance="outline" style="min-width: 220px;">[37m[39m[22m
[1m[31m         ╵     [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-label' is not a known element:[39m[22m
[1m[31m1. If 'mat-label' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-label' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:24:6:[39m[22m
[1m[31m[37m      24 │       [32m<mat-label>[37mTim[39m[22m[1m[31me Period</mat-label>[39m[22m
[1m[31m         ╵       [32m~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8002: Can't bind to 'matDatepicker' since it isn't a known property of 'input'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:26:24:[39m[22m
[1m[31m[37m      26 │ ...   <input matInput [32m[matDatepicker]="picker1"[37m placeholder="Start...[39m[22m
[1m[31m         ╵                       [32m~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-datepicker-toggle' is not a known element:[39m[22m
[1m[31m1. If 'mat-datepicker-toggle' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-datepicker-toggle' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:27:8:[39m[22m
[1m[31m[37m      27 │         [32m<mat-datepicker-toggle matSuffix [for]="picker1">[37m</mat-dat...[39m[22m
[1m[31m         ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │[39m[22m[1m[31m   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8002: Can't bind to 'htmlFor' since it isn't a known property of 'mat-datepicker-toggle'.[39m[22m
[1m[31m1. If 'mat-datepicker-toggle' is an Angular component and it has 'htmlFor' input, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-datepicker-toggle' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[39m[22m
[1m[31m3. To allow any property add 'NO_ERRORS_SCHEMA' to the '@Component.schemas' of this component.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:27:41:[39m[22m
[1m[31m[37m      27 │ ...tepicker-toggle matSuffix [32m[for]="picker1"[37m></mat-datepicker-toggle>[39m[22m
[1m[31m         ╵                              [32m~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-datepicker' is not a known element:[39m[22m
[1m[31m1. If 'mat-datepicker' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-datepicker' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:28:8:[39m[22m
[1m[31m[37m      28 │         [32m<mat-datepicker #picker1>[37m</mat-datepicker>[39m[22m
[1m[31m         ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component Hd[39m[22m[1m[31mbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8002: Can't bind to 'matDatepicker' since it isn't a known property of 'input'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:30:24:[39m[22m
[1m[31m[37m      30 │ ...   <input matInput [32m[matDatepicker]="picker2"[37m placeholder="End d...[39m[22m
[1m[31m         ╵                       [32m~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-datepicker-toggle' is not a known element:[39m[22m
[1m[31m1. If 'mat-datepicker-toggle' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-datepicker-toggle' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:31:8:[39m[22m
[1m[31m[37m      31 │         [32m<mat-datepicker-toggle matSuffix [for]="picker2">[37m</mat-dat...[39m[22m
[1m[31m         ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~[39m[22m[1m[31m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8002: Can't bind to 'htmlFor' since it isn't a known property of 'mat-datepicker-toggle'.[39m[22m
[1m[31m1. If 'mat-datepicker-toggle' is an Angular component and it has 'htmlFor' input, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-datepicker-toggle' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[39m[22m
[1m[31m3. To allow any property add 'NO_ERRORS_SCHEMA' to the '@Component.schemas' of this component.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:31:41:[39m[22m
[1m[31m[37m      31 │ ...tepicker-toggle matSuffix [32m[for]="picker2"[37m></mat-datepicker-toggle>[39m[22m
[1m[31m         ╵                              [32m~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-datepicker' is not a known element:[39m[22m
[1m[31m1. If 'mat-datepicker' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-datepicker' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:32:8:[39m[22m
[1m[31m[37m      32 │         [32m<mat-datepicker #picker2>[37m</mat-datepicker>[39m[22m
[1m[31m         ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:1[39m[22m[1m[31m5:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-button-toggle-group' is not a known element:[39m[22m
[1m[31m1. If 'mat-button-toggle-group' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-button-toggle-group' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:50:6:[39m[22m
[1m[31m[37m      50 │       [32m<mat-button-toggle-group name="sortBy" value="Month">[37m[39m[22m
[1m[31m         ╵       [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-button-toggle' is not a known element:[39m[22m
[1m[31m1. If 'mat-button-toggle' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-button-toggle' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:51:8:[39m[22m
[1m[31m[37m      51 │         [32m<mat-button-toggle value="Month">[37mMonth</mat-button-toggle>[39m[22m
[1m[31m         ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.componen[39m[22m[1m[31mt.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-button-toggle' is not a known element:[39m[22m
[1m[31m1. If 'mat-button-toggle' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-button-toggle' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:52:8:[39m[22m
[1m[31m[37m      52 │         [32m<mat-button-toggle value="Quarter">[37mQuarter</mat-button-tog...[39m[22m
[1m[31m         ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-button-toggle' is not a known element:[39m[22m
[1m[31m1. If 'mat-button-toggle' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-button-toggle' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:53:8:[39m[22m
[1m[31m[37m      53 │         [32m<mat-button-toggle value="Year" disabled>[37mYear</mat-button-...[39m[22m
[1m[31m         ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts[39m[22m[1m[31m:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-button-toggle-group' is not a known element:[39m[22m
[1m[31m1. If 'mat-button-toggle-group' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-button-toggle-group' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:58:6:[39m[22m
[1m[31m[37m      58 │       [32m<mat-button-toggle-group name="chartType" value="Average PSF">[37m[39m[22m
[1m[31m         ╵       [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-button-toggle' is not a known element:[39m[22m
[1m[31m1. If 'mat-button-toggle' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-button-toggle' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:59:8:[39m[22m
[1m[31m[37m      59 │         [32m<mat-button-toggle value="Average PSF">[37mAverage PSF</mat-bu...[39m[22m
[1m[31m         ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale[39m[22m[1m[31m-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-button-toggle' is not a known element:[39m[22m
[1m[31m1. If 'mat-button-toggle' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-button-toggle' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:60:8:[39m[22m
[1m[31m[37m      60 │         [32m<mat-button-toggle value="Median PSF">[37mMedian PSF</mat-butt...[39m[22m
[1m[31m         ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-button-toggle' is not a known element:[39m[22m
[1m[31m1. If 'mat-button-toggle' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-button-toggle' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:61:8:[39m[22m
[1m[31m[37m      61 │         [32m<mat-button-toggle value="Average Price">[37mAverage Price</ma...[39m[22m
[1m[31m         ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-[39m[22m[1m[31mtrend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-button-toggle' is not a known element:[39m[22m
[1m[31m1. If 'mat-button-toggle' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-button-toggle' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:62:8:[39m[22m
[1m[31m[37m      62 │         [32m<mat-button-toggle value="Median Price">[37mMedian Price</mat-...[39m[22m
[1m[31m         ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-button-toggle' is not a known element:[39m[22m
[1m[31m1. If 'mat-button-toggle' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-button-toggle' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:63:8:[39m[22m
[1m[31m[37m      63 │         [32m<mat-button-toggle value="Capital Gain (PSF)">[37mCapital Gain...[39m[22m
[1m[31m         ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-r[39m[22m[1m[31mesale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-card' is not a known element:[39m[22m
[1m[31m1. If 'mat-card' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-card' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:70:4:[39m[22m
[1m[31m[37m      70 │     [32m<mat-card style="flex: 2; min-width: 350px;">[37m[39m[22m
[1m[31m         ╵     [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-card' is not a known element:[39m[22m
[1m[31m1. If 'mat-card' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-card' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:89:4:[39m[22m
[1m[31m[37m      89 │     [32m<mat-card style="flex: 1; min-width: 320px;">[37m[39m[22m
[1m[31m         ╵     [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-[39m[22m[1m[31mresale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8002: Can't bind to 'dataSource' since it isn't a known property of 'table'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:98:23:[39m[22m
[1m[31m[37m      98 │       <table mat-table [32m[dataSource]="[]"[37m style="width: 100%;">[39m[22m
[1m[31m         ╵                        [32m~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-icon' is not a known element:[39m[22m
[1m[31m1. If 'mat-icon' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-icon' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:148:37:[39m[22m
[1m[31m[37m      148 │ ... mat-icon-button disabled>[32m<mat-icon>[37mchevron_left</mat-icon></b...[39m[22m
[1m[31m          ╵                              [32m~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-icon' is not a known element:[39m[22m
[1m[31m1. If 'mat-icon' is an Angular component, then verify that it is included in the '@Componen[39m[22m[1m[31mt.imports' of this component.[39m[22m
[1m[31m2. If 'mat-icon' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:152:28:[39m[22m
[1m[31m[37m      152 │     <button mat-icon-button>[32m<mat-icon>[37mchevron_right</mat-icon></b...[39m[22m
[1m[31m          ╵                             [32m~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[32m✔[39m Changes detected. Rebuilding...
[37mApplication bundle generation failed. [4.791 seconds][39m
[37m[39m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mTS-998113: SVGIconComponent is not used within the template of HdbResaleTrendComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:21:39:[39m[22m
[1m[33m[37m      21 │ ...monModule, FormsModule, [32mSVGIconComponent[37m, ResaleTrendChartCompo...[39m[22m
[1m[33m         ╵                            [32m~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mTS-998113: ResaleTrendChartComponent is not used within the template of HdbResaleTrendComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:21:57:[39m[22m
[1m[33m[37m      21 │ ...Module, FormsModule, SVGIconComponent, [32mResaleTrendChartComponent[37m],[39m[22m
[1m[33m         ╵                                           [32m~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-icon' is not a known element:[39m[22m
[1m[31m1. If 'mat-icon' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-icon' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:3:4:[39m[22m
[1m[31m[37m      3 │     [32m<mat-icon>[37mhome</mat-icon>[39m[22m
[1m[31m        ╵     [32m~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-form-field' is not a known element:[39m[22m
[1m[31m1. If 'mat-form-field' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-form-field' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:11:4:[39m[22m
[1m[31m[37m      11 │     [32m<mat-form-field appearance="outline" style="min-width: 180px;">[37m[39m[22m
[1m[31m         ╵     [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-label' is not a known element:[39m[22m
[1m[31m1. If[39m[22m[1m[31m 'mat-label' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-label' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:12:6:[39m[22m
[1m[31m[37m      12 │       [32m<mat-label>[37mTown</mat-label>[39m[22m
[1m[31m         ╵       [32m~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-select' is not a known element:[39m[22m
[1m[31m1. If 'mat-select' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-select' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:13:6:[39m[22m
[1m[31m[37m      13 │       [32m<mat-select value="All Towns">[37m[39m[22m
[1m[31m         ╵       [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-option' is not a known element:[39m[22m
[1m[31m1. If 'mat-option' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-option' is a Web Component [39m[22m[1m[31mthen add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:14:8:[39m[22m
[1m[31m[37m      14 │         [32m<mat-option>[37mAll Towns</mat-option>[39m[22m
[1m[31m         ╵         [32m~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-form-field' is not a known element:[39m[22m
[1m[31m1. If 'mat-form-field' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-form-field' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:17:4:[39m[22m
[1m[31m[37m      17 │     [32m<mat-form-field appearance="outline" style="min-width: 180px;">[37m[39m[22m
[1m[31m         ╵     [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-label' is not a known element:[39m[22m
[1m[31m1. If 'mat-label' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-label' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this c[39m[22m[1m[31momponent to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:18:6:[39m[22m
[1m[31m[37m      18 │       [32m<mat-label>[37mStreet</mat-label>[39m[22m
[1m[31m         ╵       [32m~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-select' is not a known element:[39m[22m
[1m[31m1. If 'mat-select' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-select' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:19:6:[39m[22m
[1m[31m[37m      19 │       [32m<mat-select value="All Streets">[37m[39m[22m
[1m[31m         ╵       [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-option' is not a known element:[39m[22m
[1m[31m1. If 'mat-option' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-option' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dumm[39m[22m[1m[31my.component.html:20:8:[39m[22m
[1m[31m[37m      20 │         [32m<mat-option>[37mAll Streets</mat-option>[39m[22m
[1m[31m         ╵         [32m~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-form-field' is not a known element:[39m[22m
[1m[31m1. If 'mat-form-field' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-form-field' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:23:4:[39m[22m
[1m[31m[37m      23 │     [32m<mat-form-field appearance="outline" style="min-width: 220px;">[37m[39m[22m
[1m[31m         ╵     [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-label' is not a known element:[39m[22m
[1m[31m1. If 'mat-label' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-label' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:24:6:[39m[22m
[1m[31m[37m      24 │       [32m<mat-label>[37mTim[39m[22m[1m[31me Period</mat-label>[39m[22m
[1m[31m         ╵       [32m~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8002: Can't bind to 'matDatepicker' since it isn't a known property of 'input'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:26:24:[39m[22m
[1m[31m[37m      26 │ ...   <input matInput [32m[matDatepicker]="picker1"[37m placeholder="Start...[39m[22m
[1m[31m         ╵                       [32m~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-datepicker-toggle' is not a known element:[39m[22m
[1m[31m1. If 'mat-datepicker-toggle' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-datepicker-toggle' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:27:8:[39m[22m
[1m[31m[37m      27 │         [32m<mat-datepicker-toggle matSuffix [for]="picker1">[37m</mat-dat...[39m[22m
[1m[31m         ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │[39m[22m[1m[31m   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8002: Can't bind to 'htmlFor' since it isn't a known property of 'mat-datepicker-toggle'.[39m[22m
[1m[31m1. If 'mat-datepicker-toggle' is an Angular component and it has 'htmlFor' input, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-datepicker-toggle' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[39m[22m
[1m[31m3. To allow any property add 'NO_ERRORS_SCHEMA' to the '@Component.schemas' of this component.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:27:41:[39m[22m
[1m[31m[37m      27 │ ...tepicker-toggle matSuffix [32m[for]="picker1"[37m></mat-datepicker-toggle>[39m[22m
[1m[31m         ╵                              [32m~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-datepicker' is not a known element:[39m[22m
[1m[31m1. If 'mat-datepicker' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-datepicker' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:28:8:[39m[22m
[1m[31m[37m      28 │         [32m<mat-datepicker #picker1>[37m</mat-datepicker>[39m[22m
[1m[31m         ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component Hd[39m[22m[1m[31mbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8002: Can't bind to 'matDatepicker' since it isn't a known property of 'input'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:30:24:[39m[22m
[1m[31m[37m      30 │ ...   <input matInput [32m[matDatepicker]="picker2"[37m placeholder="End d...[39m[22m
[1m[31m         ╵                       [32m~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-datepicker-toggle' is not a known element:[39m[22m
[1m[31m1. If 'mat-datepicker-toggle' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-datepicker-toggle' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:31:8:[39m[22m
[1m[31m[37m      31 │         [32m<mat-datepicker-toggle matSuffix [for]="picker2">[37m</mat-dat...[39m[22m
[1m[31m         ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~[39m[22m[1m[31m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8002: Can't bind to 'htmlFor' since it isn't a known property of 'mat-datepicker-toggle'.[39m[22m
[1m[31m1. If 'mat-datepicker-toggle' is an Angular component and it has 'htmlFor' input, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-datepicker-toggle' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[39m[22m
[1m[31m3. To allow any property add 'NO_ERRORS_SCHEMA' to the '@Component.schemas' of this component.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:31:41:[39m[22m
[1m[31m[37m      31 │ ...tepicker-toggle matSuffix [32m[for]="picker2"[37m></mat-datepicker-toggle>[39m[22m
[1m[31m         ╵                              [32m~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-datepicker' is not a known element:[39m[22m
[1m[31m1. If 'mat-datepicker' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-datepicker' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:32:8:[39m[22m
[1m[31m[37m      32 │         [32m<mat-datepicker #picker2>[37m</mat-datepicker>[39m[22m
[1m[31m         ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:1[39m[22m[1m[31m5:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-button-toggle-group' is not a known element:[39m[22m
[1m[31m1. If 'mat-button-toggle-group' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-button-toggle-group' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:50:6:[39m[22m
[1m[31m[37m      50 │       [32m<mat-button-toggle-group name="sortBy" value="Month">[37m[39m[22m
[1m[31m         ╵       [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-button-toggle' is not a known element:[39m[22m
[1m[31m1. If 'mat-button-toggle' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-button-toggle' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:51:8:[39m[22m
[1m[31m[37m      51 │         [32m<mat-button-toggle value="Month">[37mMonth</mat-button-toggle>[39m[22m
[1m[31m         ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.componen[39m[22m[1m[31mt.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-button-toggle' is not a known element:[39m[22m
[1m[31m1. If 'mat-button-toggle' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-button-toggle' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:52:8:[39m[22m
[1m[31m[37m      52 │         [32m<mat-button-toggle value="Quarter">[37mQuarter</mat-button-tog...[39m[22m
[1m[31m         ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-button-toggle' is not a known element:[39m[22m
[1m[31m1. If 'mat-button-toggle' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-button-toggle' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:53:8:[39m[22m
[1m[31m[37m      53 │         [32m<mat-button-toggle value="Year" disabled>[37mYear</mat-button-...[39m[22m
[1m[31m         ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts[39m[22m[1m[31m:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-button-toggle-group' is not a known element:[39m[22m
[1m[31m1. If 'mat-button-toggle-group' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-button-toggle-group' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:58:6:[39m[22m
[1m[31m[37m      58 │       [32m<mat-button-toggle-group name="chartType" value="Average PSF">[37m[39m[22m
[1m[31m         ╵       [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-button-toggle' is not a known element:[39m[22m
[1m[31m1. If 'mat-button-toggle' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-button-toggle' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:59:8:[39m[22m
[1m[31m[37m      59 │         [32m<mat-button-toggle value="Average PSF">[37mAverage PSF</mat-bu...[39m[22m
[1m[31m         ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale[39m[22m[1m[31m-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-button-toggle' is not a known element:[39m[22m
[1m[31m1. If 'mat-button-toggle' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-button-toggle' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:60:8:[39m[22m
[1m[31m[37m      60 │         [32m<mat-button-toggle value="Median PSF">[37mMedian PSF</mat-butt...[39m[22m
[1m[31m         ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-button-toggle' is not a known element:[39m[22m
[1m[31m1. If 'mat-button-toggle' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-button-toggle' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:61:8:[39m[22m
[1m[31m[37m      61 │         [32m<mat-button-toggle value="Average Price">[37mAverage Price</ma...[39m[22m
[1m[31m         ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-[39m[22m[1m[31mtrend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-button-toggle' is not a known element:[39m[22m
[1m[31m1. If 'mat-button-toggle' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-button-toggle' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:62:8:[39m[22m
[1m[31m[37m      62 │         [32m<mat-button-toggle value="Median Price">[37mMedian Price</mat-...[39m[22m
[1m[31m         ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-button-toggle' is not a known element:[39m[22m
[1m[31m1. If 'mat-button-toggle' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-button-toggle' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:63:8:[39m[22m
[1m[31m[37m      63 │         [32m<mat-button-toggle value="Capital Gain (PSF)">[37mCapital Gain...[39m[22m
[1m[31m         ╵         [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-r[39m[22m[1m[31mesale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-card' is not a known element:[39m[22m
[1m[31m1. If 'mat-card' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-card' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:70:4:[39m[22m
[1m[31m[37m      70 │     [32m<mat-card style="flex: 2; min-width: 350px;">[37m[39m[22m
[1m[31m         ╵     [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-card' is not a known element:[39m[22m
[1m[31m1. If 'mat-card' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-card' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:89:4:[39m[22m
[1m[31m[37m      89 │     [32m<mat-card style="flex: 1; min-width: 320px;">[37m[39m[22m
[1m[31m         ╵     [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-[39m[22m[1m[31mresale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8002: Can't bind to 'dataSource' since it isn't a known property of 'table'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:98:23:[39m[22m
[1m[31m[37m      98 │       <table mat-table [32m[dataSource]="[]"[37m style="width: 100%;">[39m[22m
[1m[31m         ╵                        [32m~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-icon' is not a known element:[39m[22m
[1m[31m1. If 'mat-icon' is an Angular component, then verify that it is included in the '@Component.imports' of this component.[39m[22m
[1m[31m2. If 'mat-icon' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:148:37:[39m[22m
[1m[31m[37m      148 │ ... mat-icon-button disabled>[32m<mat-icon>[37mchevron_left</mat-icon></b...[39m[22m
[1m[31m          ╵                              [32m~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG8001: 'mat-icon' is not a known element:[39m[22m
[1m[31m1. If 'mat-icon' is an Angular component, then verify that it is included in the '@Componen[39m[22m[1m[31mt.imports' of this component.[39m[22m
[1m[31m2. If 'mat-icon' is a Web Component then add 'CUSTOM_ELEMENTS_SCHEMA' to the '@Component.schemas' of this component to suppress this message.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend-dummy.component.html:152:28:[39m[22m
[1m[31m[37m      152 │     <button mat-icon-button>[32m<mat-icon>[37mchevron_right</mat-icon></b...[39m[22m
[1m[31m          ╵                             [32m~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  Error occurs in the template of component HdbResaleTrendComponent.[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-trend-dummy.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[33m❯[39m Changes detected. Rebuilding...
[32m✔[39m Changes detected. Rebuilding...
[37m[1mInitial chunk files[22m[2m | [22m[1mNames[22m                          [2m | [22m [1mRaw size[22m[39m
[37m[32mstyles.css[39m[37m         [2m | [22m[2mstyles[22m                         [2m | [22m[36m424.48 kB[39m[37m[2m | [22m[39m
[37m[32mpolyfills.js[39m[37m       [2m | [22m[2mpolyfills[22m                      [2m | [22m [36m89.80 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-XBDRPN26.js[39m[37m  [2m | [22m[2m-[22m                              [2m | [22m  [36m3.78 kB[39m[37m[2m | [22m[39m
[37m[32mmain.js[39m[37m            [2m | [22m[2mmain[22m                           [2m | [22m[36m396 bytes[39m[37m[2m | [22m[39m
[37m[39m
[37m[1m [22m                  [2m | [22m[1mInitial total[22m                  [2m | [22m[1m518.46 kB[22m[39m
[37m[39m
[37m[1mLazy chunk files[22m   [2m | [22m[1mNames[22m                          [2m | [22m [1mRaw size[22m[39m
[37m[32mchunk-EDSVLUGT.js[39m[37m  [2m | [22m[2mmap-box-overview-component[22m     [2m | [22m[36m352.17 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-HBYKPW5K.js[39m[37m  [2m | [22m[2moverview-routes[22m                [2m | [22m[36m228.15 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-6HWV4D2G.js[39m[37m  [2m | [22m[2m-[22m                              [2m | [22m[36m206.28 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-NOZ5QGO5.js[39m[37m  [2m | [22m[2mproperty-map-component[22m         [2m | [22m[36m185.84 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-OS24SFAY.js[39m[37m  [2m | [22m[2mhdb-transaction-routes[22m         [2m | [22m[36m156.13 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-PAWOSGZF.js[39m[37m  [2m | [22m[2mcondo-transaction-routes[22m       [2m | [22m[36m150.53 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-L4FR72AY.js[39m[37m  [2m | [22m[2mbootstrap[22m                      [2m | [22m[36m116.41 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-7RWEPKQL.js[39m[37m  [2m | [22m[2mseller-preview-component[22m       [2m | [22m[36m114.14 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-2SWBOTWF.js[39m[37m  [2m | [22m[2mpreview-listing-component[22m      [2m | [22m [36m64.29 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-ADEY77IT.js[39m[37m  [2m | [22[39m[37mm[2m-[22m                              [2m | [22m [36m62.90 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-C77SGETE.js[39m[37m  [2m | [22m[2mhdb-resale-trend-component[22m     [2m | [22m [36m58.71 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-OKAQIVRP.js[39m[37m  [2m | [22m[2mselling-price-component[22m        [2m | [22m [36m48.86 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-N4IGM46E.js[39m[37m  [2m | [22m[2mmake-offer-buyer-component[22m     [2m | [22m [36m42.91 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-D2SQPVAP.js[39m[37m  [2m | [22m[2mproperty-address-component[22m     [2m | [22m [36m39.93 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-RZK7L4Q2.js[39m[37m  [2m | [22m[2moffer-price-estimator-component[22m[2m | [22m [36m36.04 kB[39m[37m[2m | [22m[39m
[37m[2m...and 32 more lazy chunks files. Use "--verbose" to show all the files.[22m[39m
[37m[39m
[37mApplication bundle generation complete. [13.354 seconds][39m
[37m[39m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mTS-998113: SVGIconComponent is not used within the template of HdbResaleTrendComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:34:4:[39m[22m
[1m[33m[37m      34 │     [32mSVGIconComponent[37m,[39m[22m
[1m[33m         ╵     [32m~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mTS-998113: ResaleTrendChartComponent is not used within the template of HdbResaleTrendComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:35:4:[39m[22m
[1m[33m[37m      35 │     [32mResaleTrendChartComponent[37m,[39m[22m
[1m[33m         ╵     [32m~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[37mPage reload sent to client(s).[39m
[33m❯[39m Changes detected. Rebuilding...
[32m✔[39m Changes detected. Rebuilding...
[37m[1mInitial chunk files[22m[2m | [22m[1mNames[22m                          [2m | [22m [1mRaw size[22m[39m
[37m[32mstyles.css[39m[37m         [2m | [22m[2mstyles[22m                         [2m | [22m[36m424.48 kB[39m[37m[2m | [22m[39m
[37m[32mpolyfills.js[39m[37m       [2m | [22m[2mpolyfills[22m                      [2m | [22m [36m89.80 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-XBDRPN26.js[39m[37m  [2m | [22m[2m-[22m                              [2m | [22m  [36m3.78 kB[39m[37m[2m | [22m[39m
[37m[32mmain.js[39m[37m            [2m | [22m[2mmain[22m                           [2m | [22m[36m396 bytes[39m[37m[2m | [22m[39m
[37m[39m
[37m[1m [22m                  [2m | [22m[1mInitial total[22m                  [2m | [22m[1m518.46 kB[22m[39m
[37m[39m
[37m[1mLazy chunk files[22m   [2m | [22m[1mNames[22m                          [2m | [22m [1mRaw size[22m[39m
[37m[32mchunk-EDSVLUGT.js[39m[37m  [2m | [22m[2mmap-box-overview-component[22m     [2m | [22m[36m352.17 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-HBYKPW5K.js[39m[37m  [2m | [22m[2moverview-routes[22m                [2m | [22m[36m228.15 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-6HWV4D2G.js[39m[37m  [2m | [22m[2m-[22m                              [2m | [22m[36m206.28 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-NOZ5QGO5.js[39m[37m  [2m | [22m[2mproperty-map-component[22m         [2m | [22m[36m185.84 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-OS24SFAY.js[39m[37m  [2m | [22m[2mhdb-transaction-routes[22m         [2m | [22m[36m156.13 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-PAWOSGZF.js[39m[37m  [2m | [22m[2mcondo-transaction-routes[22m       [2m | [22m[36m150.53 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-L4FR72AY.js[39m[37m  [2m | [22m[2mbootstrap[22m                      [2m | [22m[36m116.41 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-7RWEPKQL.js[39m[37m  [2m | [22m[2mseller-preview-component[22m       [2m | [22m[36m114.14 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-2SWBOTWF.js[39m[37m  [2m | [22m[2mpreview-listing-component[22m      [2m | [22m [36m64.29 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-ADEY77IT.js[39m[37m  [2m | [22[39m[37mm[2m-[22m                              [2m | [22m [36m62.90 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-C77SGETE.js[39m[37m  [2m | [22m[2mhdb-resale-trend-component[22m     [2m | [22m [36m58.71 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-OKAQIVRP.js[39m[37m  [2m | [22m[2mselling-price-component[22m        [2m | [22m [36m48.86 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-N4IGM46E.js[39m[37m  [2m | [22m[2mmake-offer-buyer-component[22m     [2m | [22m [36m42.91 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-D2SQPVAP.js[39m[37m  [2m | [22m[2mproperty-address-component[22m     [2m | [22m [36m39.93 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-RZK7L4Q2.js[39m[37m  [2m | [22m[2moffer-price-estimator-component[22m[2m | [22m [36m36.04 kB[39m[37m[2m | [22m[39m
[37m[2m...and 32 more lazy chunks files. Use "--verbose" to show all the files.[22m[39m
[37m[39m
[37mApplication bundle generation complete. [12.535 seconds][39m
[37m[39m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mTS-998113: SVGIconComponent is not used within the template of HdbResaleTrendComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:34:4:[39m[22m
[1m[33m[37m      34 │     [32mSVGIconComponent[37m,[39m[22m
[1m[33m         ╵     [32m~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mTS-998113: ResaleTrendChartComponent is not used within the template of HdbResaleTrendComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:35:4:[39m[22m
[1m[33m[37m      35 │     [32mResaleTrendChartComponent[37m,[39m[22m
[1m[33m         ╵     [32m~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[2m5:55:39 pm[22m [36m[1m[vite][22m[39m [90m[2m(client)[22m[39m [32m✨ new dependencies optimized: [33m@angular/material/button-toggle, @angular/material/card, @angular/material/table[32m[39m
[2m5:55:39 pm[22m [36m[1m[vite][22m[39m [90m[2m(client)[22m[39m [32m✨ optimized dependencies changed. reloading[39m
[33m❯[39m Changes detected. Rebuilding...
[32m✔[39m Changes detected. Rebuilding...
[37m[1mInitial chunk files[22m[2m | [22m[1mNames[22m                     [2m | [22m [1mRaw size[22m[39m
[37m[32mmain.js[39m[37m            [2m | [22m[2mmain[22m                      [2m | [22m[36m396 bytes[39m[37m[2m | [22m[39m
[37m[39m
[37m[1mLazy chunk files[22m   [2m | [22m[1mNames[22m                     [2m | [22m [1mRaw size[22m[39m
[37m[32mchunk-6P6ZNAAM.js[39m[37m  [2m | [22m[2mbootstrap[22m                 [2m | [22m[36m116.41 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-TPPQQWQD.js[39m[37m  [2m | [22m[2mhdb-resale-trend-component[22m[2m | [22m [36m56.32 kB[39m[37m[2m | [22m[39m
[37m[39m
[37mApplication bundle generation complete. [5.549 seconds][39m
[37m[39m
[37mPage reload sent to client(s).[39m
[33m❯[39m Changes detected. Rebuilding...
[32m✔[39m Changes detected. Rebuilding...
[37m[1mInitial chunk files[22m[2m | [22m[1mNames[22m                     [2m | [22m [1mRaw size[22m[39m
[37m[32mmain.js[39m[37m            [2m | [22m[2mmain[22m                      [2m | [22m[36m396 bytes[39m[37m[2m | [22m[39m
[37m[39m
[37m[1mLazy chunk files[22m   [2m | [22m[1mNames[22m                     [2m | [22m [1mRaw size[22m[39m
[37m[32mchunk-GQPGMZCD.js[39m[37m  [2m | [22m[2mbootstrap[22m                 [2m | [22m[36m116.41 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-MWFMMMBV.js[39m[37m  [2m | [22m[2mhdb-resale-trend-component[22m[2m | [22m [36m26.30 kB[39m[37m[2m | [22m[39m
[37m[39m
[37mApplication bundle generation complete. [5.759 seconds][39m
[37m[39m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mTS-998113: SVGIconComponent is not used within the template of HdbResaleTrendComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:21:39:[39m[22m
[1m[33m[37m      21 │ ...monModule, FormsModule, [32mSVGIconComponent[37m, ResaleTrendChartCompo...[39m[22m
[1m[33m         ╵                            [32m~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mTS-998113: ResaleTrendChartComponent is not used within the template of HdbResaleTrendComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:21:57:[39m[22m
[1m[33m[37m      21 │ ...Module, FormsModule, SVGIconComponent, [32mResaleTrendChartComponent[37m],[39m[22m
[1m[33m         ╵                                           [32m~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[37mPage reload sent to client(s).[39m
[33m❯[39m Changes detected. Rebuilding...
[32m✔[39m Changes detected. Rebuilding...
[37m[1mInitial chunk files[22m[2m | [22m[1mNames[22m                          [2m | [22m [1mRaw size[22m[39m
[37m[32mstyles.css[39m[37m         [2m | [22m[2mstyles[22m                         [2m | [22m[36m424.48 kB[39m[37m[2m | [22m[39m
[37m[32mpolyfills.js[39m[37m       [2m | [22m[2mpolyfills[22m                      [2m | [22m [36m89.80 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-XBDRPN26.js[39m[37m  [2m | [22m[2m-[22m                              [2m | [22m  [36m3.78 kB[39m[37m[2m | [22m[39m
[37m[32mmain.js[39m[37m            [2m | [22m[2mmain[22m                           [2m | [22m[36m396 bytes[39m[37m[2m | [22m[39m
[37m[39m
[37m[1m [22m                  [2m | [22m[1mInitial total[22m                  [2m | [22m[1m518.46 kB[22m[39m
[37m[39m
[37m[1mLazy chunk files[22m   [2m | [22m[1mNames[22m                          [2m | [22m [1mRaw size[22m[39m
[37m[32mchunk-EDSVLUGT.js[39m[37m  [2m | [22m[2mmap-box-overview-component[22m     [2m | [22m[36m352.17 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-HBYKPW5K.js[39m[37m  [2m | [22m[2moverview-routes[22m                [2m | [22m[36m228.15 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-6HWV4D2G.js[39m[37m  [2m | [22m[2m-[22m                              [2m | [22m[36m206.28 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-NOZ5QGO5.js[39m[37m  [2m | [22m[2mproperty-map-component[22m         [2m | [22m[36m185.84 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-OS24SFAY.js[39m[37m  [2m | [22m[2mhdb-transaction-routes[22m         [2m | [22m[36m156.13 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-PAWOSGZF.js[39m[37m  [2m | [22m[2mcondo-transaction-routes[22m       [2m | [22m[36m150.53 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-GQPGMZCD.js[39m[37m  [2m | [22m[2mbootstrap[22m                      [2m | [22m[36m116.41 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-7RWEPKQL.js[39m[37m  [2m | [22m[2mseller-preview-component[22m       [2m | [22m[36m114.14 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-2SWBOTWF.js[39m[37m  [2m | [22m[2mpreview-listing-component[22m      [2m | [22m [36m64.29 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-ADEY77IT.js[39m[37m  [2m | [22[39m[37mm[2m-[22m                              [2m | [22m [36m62.90 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-OKAQIVRP.js[39m[37m  [2m | [22m[2mselling-price-component[22m        [2m | [22m [36m48.86 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-N4IGM46E.js[39m[37m  [2m | [22m[2mmake-offer-buyer-component[22m     [2m | [22m [36m42.91 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-D2SQPVAP.js[39m[37m  [2m | [22m[2mproperty-address-component[22m     [2m | [22m [36m39.93 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-RZK7L4Q2.js[39m[37m  [2m | [22m[2moffer-price-estimator-component[22m[2m | [22m [36m36.04 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-UNQZBP7Y.js[39m[37m  [2m | [22m[2mproperty-details-component[22m     [2m | [22m [36m34.87 kB[39m[37m[2m | [22m[39m
[37m[2m...and 32 more lazy chunks files. Use "--verbose" to show all the files.[22m[39m
[37m[39m
[37mApplication bundle generation complete. [6.467 seconds][39m
[37m[39m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mTS-998113: SVGIconComponent is not used within the template of HdbResaleTrendComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:21:39:[39m[22m
[1m[33m[37m      21 │ ...monModule, FormsModule, [32mSVGIconComponent[37m, ResaleTrendChartCompo...[39m[22m
[1m[33m         ╵                            [32m~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mTS-998113: ResaleTrendChartComponent is not used within the template of HdbResaleTrendComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:21:57:[39m[22m
[1m[33m[37m      21 │ ...Module, FormsModule, SVGIconComponent, [32mResaleTrendChartComponent[37m],[39m[22m
[1m[33m         ╵                                           [32m~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[37mComponent update sent to client(s).[39m
[33m❯[39m Changes detected. Rebuilding...
[32m✔[39m Changes detected. Rebuilding...
[37mApplication bundle generation failed. [4.628 seconds][39m
[37m[39m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS-992008: Could not find template file './hdb-resale-tren.component.html'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    packages/web/src/app/pages/hdb-resale-trend/hdb-resale-trend.component.ts:19:15:[39m[22m
[1m[31m[37m      19 │   templateUrl: [32m'./hdb-resale-tren.component.html'[37m,[39m[22m
[1m[31m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[33m❯[39m Changes detected. Rebuilding...
[32m✔[39m Changes detected. Rebuilding...
[37m[1mInitial chunk files[22m[2m | [22m[1mNames[22m                          [2m | [22m [1mRaw size[22m[39m
[37m[32mstyles.css[39m[37m         [2m | [22m[2mstyles[22m                         [2m | [22m[36m424.48 kB[39m[37m[2m | [22m[39m
[37m[32mpolyfills.js[39m[37m       [2m | [22m[2mpolyfills[22m                      [2m | [22m [36m89.80 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-XBDRPN26.js[39m[37m  [2m | [22m[2m-[22m                              [2m | [22m  [36m3.78 kB[39m[37m[2m | [22m[39m
[37m[32mmain.js[39m[37m            [2m | [22m[2mmain[22m                           [2m | [22m[36m396 bytes[39m[37m[2m | [22m[39m
[37m[39m
[37m[1m [22m                  [2m | [22m[1mInitial total[22m                  [2m | [22m[1m518.46 kB[22m[39m
[37m[39m
[37m[1mLazy chunk files[22m   [2m | [22m[1mNames[22m                          [2m | [22m [1mRaw size[22m[39m
[37m[32mchunk-EDSVLUGT.js[39m[37m  [2m | [22m[2mmap-box-overview-component[22m     [2m | [22m[36m352.17 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-HBYKPW5K.js[39m[37m  [2m | [22m[2moverview-routes[22m                [2m | [22m[36m228.15 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-6HWV4D2G.js[39m[37m  [2m | [22m[2m-[22m                              [2m | [22m[36m206.28 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-NOZ5QGO5.js[39m[37m  [2m | [22m[2mproperty-map-component[22m         [2m | [22m[36m185.84 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-OS24SFAY.js[39m[37m  [2m | [22m[2mhdb-transaction-routes[22m         [2m | [22m[36m156.13 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-PAWOSGZF.js[39m[37m  [2m | [22m[2mcondo-transaction-routes[22m       [2m | [22m[36m150.53 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-NX7PQNSP.js[39m[37m  [2m | [22m[2mbootstrap[22m                      [2m | [22m[36m116.41 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-7RWEPKQL.js[39m[37m  [2m | [22m[2mseller-preview-component[22m       [2m | [22m[36m114.14 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-2SWBOTWF.js[39m[37m  [2m | [22m[2mpreview-listing-component[22m      [2m | [22m [36m64.29 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-ADEY77IT.js[39m[37m  [2m | [22[39m[37mm[2m-[22m                              [2m | [22m [36m62.90 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-OKAQIVRP.js[39m[37m  [2m | [22m[2mselling-price-component[22m        [2m | [22m [36m48.86 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-N4IGM46E.js[39m[37m  [2m | [22m[2mmake-offer-buyer-component[22m     [2m | [22m [36m42.91 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-D2SQPVAP.js[39m[37m  [2m | [22m[2mproperty-address-component[22m     [2m | [22m [36m39.93 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-RZK7L4Q2.js[39m[37m  [2m | [22m[2moffer-price-estimator-component[22m[2m | [22m [36m36.04 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-UNQZBP7Y.js[39m[37m  [2m | [22m[2mproperty-details-component[22m     [2m | [22m [36m34.87 kB[39m[37m[2m | [22m[39m
[37m[2m...and 32 more lazy chunks files. Use "--verbose" to show all the files.[22m[39m
[37m[39m
[37mApplication bundle generation complete. [4.797 seconds][39m
[37m[39m
[37mPage reload sent to client(s).[39m
[33m❯[39m Changes detected. Rebuilding...
[2m5:57:12 pm[22m [36m[1m[vite][22m[39m [90m[2m(client)[22m[39m [32m✨ new dependencies optimized: [33mtslib[32m[39m
[2m5:57:12 pm[22m [36m[1m[vite][22m[39m [90m[2m(client)[22m[39m [32m✨ optimized dependencies changed. reloading[39m
[32m✔[39m Changes detected. Rebuilding...
[37m[1mInitial chunk files[22m[2m | [22m[1mNames[22m                     [2m | [22m [1mRaw size[22m[39m
[37m[32mmain.js[39m[37m            [2m | [22m[2mmain[22m                      [2m | [22m[36m396 bytes[39m[37m[2m | [22m[39m
[37m[39m
[37m[1mLazy chunk files[22m   [2m | [22m[1mNames[22m                     [2m | [22m [1mRaw size[22m[39m
[37m[32mchunk-NLAM4XFO.js[39m[37m  [2m | [22m[2mbootstrap[22m                 [2m | [22m[36m116.41 kB[39m[37m[2m | [22m[39m
[37m[32mchunk-W2DZSD6L.js[39m[37m  [2m | [22m[2mhdb-resale-trend-component[22m[2m | [22m [36m56.32 kB[39m[37m[2m | [22m[39m
[37m[39m
[37mApplication bundle generation complete. [9.595 seconds][39m
[37m[39m
[37mComponent update sent to client(s).[39m
