import { Component, inject } from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { FormModalService } from '@lib/angular/dynamic-forms/form-modal.service';
import * as CustomValidators from '@lib/angular/dynamic-forms/form-validators';
import { SilverFieldTypes } from '@lib/angular/dynamic-forms/silver-field.component';
import { SnackBarService } from '@lib/angular/snack-bar.service';
import { base64UrlDecode } from '@lib/common/fun';
import { AuthService } from '../../services/auth.service';

const parseReturnUrl = (returnUrl: string) => {
  const url = new URL(atob(base64UrlDecode(returnUrl ?? 'Lw')), window.location.origin);
  return { path: url.pathname, query: Object.fromEntries([...url.searchParams]) };
};

@Component({
  imports: [
    FormsModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    ReactiveFormsModule,
    RouterModule,
  ],
  selector: 'app-auth',
  templateUrl: './auth.component.html',
  styles: [
    `
      :host {
        display: flex;
        flex-direction: column;
        height: 100%;
        overflow: auto;
        @apply bg-base-200;
      }
      .win-dragger {
        -webkit-user-select: none;
        user-select: none;
        -webkit-app-region: drag;
        button {
          -webkit-app-region: no-drag;
        }
      }
    `,
  ],
})
export class AuthComponent {
  readonly #activatedRoute = inject(ActivatedRoute);
  readonly #authService = inject(AuthService);
  readonly #formModalService = inject(FormModalService);
  readonly #router = inject(Router);
  readonly #snackBarService = inject(SnackBarService);

  public isSignup = false;
  public signUpForm: FormGroup;
  public loginForm: FormGroup;

  constructor() {
    {
      const name = new FormControl('', [Validators.required]);
      const email = new FormControl('', [Validators.email, Validators.required]);
      const username = new FormControl('', [Validators.required, CustomValidators.username]);
      const password = new FormControl('', [Validators.required, CustomValidators.password]);
      const confirmPwd = new FormControl('', [
        Validators.required,
        CustomValidators.equalTo(password),
      ]);
      this.signUpForm = new FormGroup({ confirmPwd, email, username, password, name });
    }
    const username = new FormControl('', [
      Validators.required,
      CustomValidators.noLeadingTrailingSpace,
    ]);
    const password = new FormControl('', [Validators.required, CustomValidators.password]);
    this.loginForm = new FormGroup({ username, password });
  }

  public toggle() {
    this.isSignup = !this.isSignup;
  }

  public async loginUser() {
    const userData = this.loginForm.value;
    try {
      await this.#authService.login(userData.username, userData.password);
      this.#snackBarService.success('Logged in Successfully!');
      await this.#authService.getActiveUser();
      const returnUrl = this.#activatedRoute.snapshot.queryParamMap.get('return');
      const { path, query } = parseReturnUrl(returnUrl);
      await this.#router.navigate([path], { queryParams: query });
      this.loginForm.reset();
    } catch (error) {
      this.#snackBarService.error(
        error?.error?.message ?? error?.message ?? 'Something went wrong',
      );
    }
  }

  public async signUp() {
    const data = this.signUpForm.value;
    data.email = data.email.toLowerCase();
    const otp = await this.verifyEmailId(data.email);
    if (!otp) return;
    this.signUpForm.reset();
    const isSetup = await this.setupAccount(data, otp);
    if (isSetup) {
      await this.#authService.login(data.username, data.password);
      this.#router.navigate(['/']);
    }
  }

  public async verifyEmailId(email: string) {
    try {
      await this.#authService.verifyEmailId(email);
    } catch (error) {
      this.#snackBarService.error(error.error.message);
      return;
    }

    this.#snackBarService.warn(
      'Please Check your email for the next process and token time only 10 minutes',
    );
    const result = await this.#formModalService.open({
      heading: 'Verify Email',
      form: [
        {
          controlType: SilverFieldTypes.TEXT,
          label: 'Enter OTP',
          placeholder: 'Enter OTP',
          type: 'number',
          key: 'otp',
          value: '',
          valid: { required: true },
        },
      ],
      btn1Name: 'Cancel',
      btn2Name: 'SUBMIT',
    });
    if (!result?.action) return;
    return Number(result.value.otp);
  }

  public async setupAccount(data: any, token: number) {
    data.meta = {};
    data.token = token;
    try {
      await this.#authService.registration(data);
      return true;
    } catch (error: any) {
      this.#snackBarService.error(error.error.message);
    }
    return false;
  }

  async forgotPass() {
    const result = await this.#formModalService.open({
      heading: 'Forgot Password',
      form: [
        {
          controlType: SilverFieldTypes.TEXT,
          label: '',
          placeholder: 'Enter your email',
          type: 'email',
          key: 'email',
          value: '',
          valid: { required: true, email: true },
        },
      ],
      btn1Name: 'Cancel',
      btn2Name: 'Apply',
    });
    if (!result?.action) return;
    const email = result.value.email;
    try {
      await this.#authService.forgotPassword({ email });
    } catch (error) {
      this.#snackBarService.error(error.error.message);
      return;
    }

    const tokenForm = await this.#formModalService.open({
      heading: 'Verify Email',
      form: [
        {
          controlType: SilverFieldTypes.TEXT,
          label: 'Enter Token',
          placeholder: 'Enter Token',
          type: 'text',
          key: 'token',
          value: '',
          valid: { required: true, minLength: 5, maxLength: 5 },
        },
        {
          controlType: SilverFieldTypes.TEXT,
          label: 'Enter Password',
          placeholder: 'Enter Password',
          type: 'password',
          key: 'password',
          value: '',
          cssClass: 'mb-5',
          valid: { required: true, password: true },
        },
        {
          controlType: SilverFieldTypes.TEXT,
          label: 'Confirm Password',
          placeholder: 'Confirm Password',
          type: 'password',
          key: 'confirmPwd',
          value: '',
          cssClass: 'mb-5',
          valid: { required: true, equalTo: 'password' },
        },
      ],
      btn1Name: 'Cancel',
      btn2Name: 'SUBMIT',
    });
    if (!tokenForm?.action) return;
    const data = tokenForm.value;
    this.setPassword(email, data);
  }

  public async setPassword(email: string, data: any) {
    data.email = email;
    if (data.password !== data.confirmPwd) {
      this.#snackBarService.warn('Passwords are not same');
      return false;
    }
    data.confirmPwd = undefined;
    try {
      await this.#authService.setPassword(data);
      this.#snackBarService.success('Password Change Successfully');
      return true;
    } catch {
      this.#snackBarService.error('Token not match or expired');
    }
    return false;
  }
}
