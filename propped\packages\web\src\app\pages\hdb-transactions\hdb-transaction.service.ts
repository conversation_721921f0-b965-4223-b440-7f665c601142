import { Injectable, inject } from '@angular/core';
import { SQLWebService } from '@lib/angular/sql.service';
import { SQL, isNil } from '@lib/common/fun';
import { type JDataConfig } from '@lib/common/j-data.interface';

// # Schema
/*
CREATE TABLE blocks (
    id INTEGER PRIMARY KEY AUTOINCREMENT, -- Unique identifier for each block
    name TEXT UNIQUE -- Name of the block
);

CREATE TABLE flat_models (
    id INTEGER PRIMARY KEY AUTOINCREMENT, -- Unique identifier for each flat model
    name TEXT UNIQUE -- Name of the flat model must be 'New Generation,Improved,DBSS,Simplified,Standard,Apartment,Model A-Maisonette,Maisonette,Model A,Premium Apartment,Adjoined flat,Type S1,Type S2,Model A2,Terrace,Improved-Maisonette,Premium Maisonette,Multi Generation,Premium Apartment Loft,2-room,3Gen'
);

CREATE TABLE flat_types (
    id INTEGER PRIMARY KEY AUTOINCREMENT, -- Unique identifier for each flat type
    name TEXT UNIQUE -- Name of the flat type must be '3 ROOM,2 ROOM,5 ROOM,4 ROOM,EXECUTIVE,1 ROOM,MULTI-GENERATION'
);

CREATE TABLE street_names (
    id INTEGER PRIMARY KEY AUTOINCREMENT, -- Unique identifier for each street name
    name TEXT UNIQUE -- Name of the street
);

CREATE TABLE towns (
    id INTEGER PRIMARY KEY AUTOINCREMENT, -- Unique identifier for each town
    name TEXT UNIQUE -- Name of the town must be in 'ANG MO KIO, BEDOK, BISHAN, BUKIT BATOK, BUKIT MERAH, BUKIT PANJANG, BUKIT TIMAH, CENTRAL AREA, CHOA CHU KANG, CLEMENTI, GEYLANG, HOUGANG, JURONG EAST, JURONG WEST, KALLANG/WHAMPOA, PASIR RIS, PUNGGOL, QUEENSTOWN, SEMBAWANG, SENGKANG, TAMPINES, MARINE PARADE, SERANGOON, TOA PAYOH, WOODLANDS, YISHUN'
);

CREATE TABLE sales (
    id INTEGER PRIMARY KEY AUTOINCREMENT, -- Unique identifier for each sale
    blockId INTEGER, -- Identifier for the block
    flatModelId INTEGER, -- Identifier for the flat model
    flatTypeId INTEGER, -- Identifier for the flat type
    area INTEGER, -- Floor area in square meters
    leaseYear INTEGER, -- Year the lease commenced
    resaleAt INTEGER, -- Resale timestamp epoch
    remain INTEGER, -- Remaining lease in months
    price INTEGER, -- Resale price
    startStorey INTEGER, -- Starting storey of the flat
    endStorey INTEGER, -- Ending storey of the flat
    streetId INTEGER, -- Identifier for the street
    townId INTEGER -- Identifier for the town
);
*/

const escapeSQLiteValue = (value: any) => {
  if (value === null || value === undefined) return 'NULL';
  if (typeof value === 'number') return value.toString();
  if (typeof value === 'boolean') return (value ? 1 : 0).toString();
  if (typeof value === 'string') return `'${value.replace(/'/g, "''")}'`;
  if (value instanceof Date) return `'${value.toISOString()}'`;
  if (Array.isArray(value)) return `'${JSON.stringify(value).replace(/'/g, "''")}'`;
  if (typeof value === 'object') return `'${JSON.stringify(value).replace(/'/g, "''")}'`;
  throw new Error('Unsupported data type');
};

@Injectable({
  providedIn: 'root',
})
export class HDBSalesService {
  salesDb: any;
  readonly #sqlWebService = inject(SQLWebService);

  loadDB() {
    this.salesDb ??= this.#sqlWebService.loadDB('/assets/hdb-sales.sqlite');
    return this.salesDb;
  }

  getAllTowns(db: any) {
    return db.exec(SQL`select * from towns`)[0].values;
  }

  getAllBlocks(db: any) {
    return db.exec(SQL`select * from blocks`)[0].values;
  }

  getAllStreets(db: any) {
    return db.exec(SQL`select * from street_names`)[0].values;
  }

  getAllFlatModels(db: any) {
    return db.exec(SQL`select * from flat_models`)[0].values;
  }

  getAllFlatTypes(db: any) {
    return db.exec(SQL`select * from flat_types`)[0].values;
  }

  public getConfigs(): JDataConfig {
    return { page: 1, limit: 50, sort: 'resaleAt', order: 'DESC' };
  }

  generateSqlQuery(db: any, filters, configs) {
    let query = /* SQL */ `SELECT
      sales.id as id,
      towns.name AS town_name,
      blocks.name AS block_number,
      flat_types.name AS flat_type,
      sales.price AS price,
      ROUND(sales.price/(sales.area*10.73692), 2) AS psf,
      ROUND(sales.area*10.73692, 2) AS area,
      street_names.name AS street_name,
      sales.remain/12 AS remaining_lease,
      datetime(sales.resaleAt, 'unixepoch') AS transaction_date,
      sales.startStorey || '-' || sales.endStorey AS floor_level
    FROM sales
    JOIN blocks ON sales.blockId = blocks.id
    JOIN flat_models ON sales.flatModelId = flat_models.id
    JOIN flat_types ON sales.flatTypeId = flat_types.id
    JOIN street_names ON sales.streetId = street_names.id
    JOIN towns ON sales.townId = towns.id
    WHERE 1 = 1
    `;

    let condition = '';
    if (filters.townId?.length)
      condition += ` AND townId IN (${filters.townId.map(escapeSQLiteValue).join(', ')})`;

    if (filters.blockId?.length)
      condition += ` AND blockId IN (${filters.blockId.map(escapeSQLiteValue).join(', ')})`;

    if (filters.flatModelId?.length)
      condition += ` AND flatModelId IN (${filters.flatModelId.map(escapeSQLiteValue).join(', ')})`;

    if (filters.flatTypeId?.length)
      condition += ` AND flatTypeId IN (${filters.flatTypeId.map(escapeSQLiteValue).join(', ')})`;

    if (filters.timePeriod) {
      if (filters.timePeriod.from)
        condition += ` AND resaleAt >= ${escapeSQLiteValue(
          Math.floor(new Date(filters.timePeriod.from).getTime() / 1000),
        )}`;

      if (filters.timePeriod.to)
        condition += ` AND resaleAt <= ${escapeSQLiteValue(
          Math.floor(new Date(filters.timePeriod.to).getTime() / 1000),
        )}`;
    }

    if (!isNil(filters.minimumPrice))
      condition += ` AND price >= ${escapeSQLiteValue(filters.minimumPrice)}`;

    if (!isNil(filters.maximumPrice))
      condition += ` AND price <= ${escapeSQLiteValue(filters.maximumPrice)}`;

    if (!isNil(filters.minimumPsf))
      condition += ` AND ROUND(price/(area*10.73692), 2) >= ${escapeSQLiteValue(filters.minimumPsf)}`;

    if (!isNil(filters.maximumPsf))
      condition += ` AND ROUND(price/(area*10.73692), 2) <= ${escapeSQLiteValue(filters.maximumPsf)}`;

    if (!isNil(filters.minimumLeaseYear))
      condition += ` AND remain >= ${escapeSQLiteValue(filters.minimumLeaseYear * 12)}`;

    if (!isNil(filters.maximumLeaseYear))
      condition += ` AND remain <= ${escapeSQLiteValue(filters.maximumLeaseYear * 12)}`;

    if (!isNil(filters.minimumStorey))
      condition += ` AND startStorey >= ${escapeSQLiteValue(filters.minimumStorey)}`;

    if (!isNil(filters.maximumStorey))
      condition += ` AND endStorey <= ${escapeSQLiteValue(filters.maximumStorey)}`;

    if (!isNil(filters.area)) condition += ` AND area = ${escapeSQLiteValue(filters.area)}`;

    const total = db.exec(`SELECT COUNT(*) FROM sales WHERE 1=1 ${condition}`)[0].values[0][0];

    query += ` ${condition}`;
    query += ` ORDER BY ${configs.sort} ${configs.order}`;
    query += ` LIMIT ${configs.limit} OFFSET ${(configs.page - 1) * configs.limit}`;
    // query += ` LIMIT ${configs.limit} OFFSET ${(configs.page - 1) * configs.limit}`;
    console.time('query time');
    console.log('DATA QUERY', query);
    const res = db.exec(query);
    console.timeEnd('query time');
    return {
      headers: res[0]?.columns ?? [],
      data: res[0]?.values ?? [],
      meta: {
        total,
        start: (configs.page - 1) * configs.limit,
        end: Math.min(total, configs.page * configs.limit),
      },
    };
  }

  /**
   * Get all data for statistics calculation based on the current filters
   * This returns all matching records without pagination for accurate statistics
   */
  getAllFilteredDataForStats(db: any, filters) {
    let query = /* SQL */ `SELECT
      sales.price AS price,
      ROUND(sales.price/(sales.area*10.73692), 2) AS psf,
      date(sales.resaleAt, 'unixepoch') AS resale_date,
      sales.area AS area
    FROM sales
    JOIN blocks ON sales.blockId = blocks.id
    JOIN flat_models ON sales.flatModelId = flat_models.id
    JOIN flat_types ON sales.flatTypeId = flat_types.id
    JOIN street_names ON sales.streetId = street_names.id
    JOIN towns ON sales.townId = towns.id
    WHERE 1=1
    `;

    // Apply all filters
    if (filters.townId?.length)
      query += ` AND townId IN (${filters.townId.map(escapeSQLiteValue).join(', ')})`;

    if (filters.blockId?.length)
      query += ` AND blockId IN (${filters.blockId.map(escapeSQLiteValue).join(', ')})`;

    if (filters.flatModelId?.length)
      query += ` AND flatModelId IN (${filters.flatModelId.map(escapeSQLiteValue).join(', ')})`;

    if (filters.flatTypeId?.length)
      query += ` AND flatTypeId IN (${filters.flatTypeId.map(escapeSQLiteValue).join(', ')})`;

    if (filters.timePeriod) {
      if (filters.timePeriod.from)
        query += ` AND resaleAt >= ${escapeSQLiteValue(
          Math.floor(new Date(filters.timePeriod.from).getTime() / 1000),
        )}`;

      if (filters.timePeriod.to)
        query += ` AND resaleAt <= ${escapeSQLiteValue(
          Math.floor(new Date(filters.timePeriod.to).getTime() / 1000),
        )}`;
    }

    if (!isNil(filters.minimumPrice))
      query += ` AND price >= ${escapeSQLiteValue(filters.minimumPrice)}`;

    if (!isNil(filters.maximumPrice))
      query += ` AND price <= ${escapeSQLiteValue(filters.maximumPrice)}`;

    if (!isNil(filters.minimumPsf))
      query += ` AND ROUND(price/(area*10.73692), 2) >= ${escapeSQLiteValue(filters.minimumPsf)}`;

    if (!isNil(filters.maximumPsf))
      query += ` AND ROUND(price/(area*10.73692), 2) <= ${escapeSQLiteValue(filters.maximumPsf)}`;

    if (!isNil(filters.minimumLeaseYear))
      query += ` AND remain >= ${escapeSQLiteValue(filters.minimumLeaseYear * 12)}`;

    if (!isNil(filters.maximumLeaseYear))
      query += ` AND remain <= ${escapeSQLiteValue(filters.maximumLeaseYear * 12)}`;

    if (!isNil(filters.minimumStorey))
      query += ` AND startStorey >= ${escapeSQLiteValue(filters.minimumStorey)}`;

    if (!isNil(filters.maximumStorey))
      query += ` AND endStorey <= ${escapeSQLiteValue(filters.maximumStorey)}`;

    if (!isNil(filters.area)) query += ` AND area = ${escapeSQLiteValue(filters.area)}`;

    // Add ORDER BY to ensure we get transactions sorted by date
    query += ` ORDER BY resaleAt DESC`;

    console.time('stats query time');
    const res = db.exec(query);
    console.timeEnd('stats query time');

    return {
      headers: res[0]?.columns ?? [],
      data: res[0]?.values ?? [],
      count: res[0]?.values.length ?? 0,
    };
  }

  private buildWhereClause(filters: any): string {
    const conditions: string[] = [];

    // Handle time period filter
    if (filters.timePeriod) {
      if (filters.timePeriod.from) {
        const fromTimestamp = new Date(filters.timePeriod.from).getTime() / 1000;
        conditions.push(`sales.resaleAt >= ${fromTimestamp}`);
      }

      if (filters.timePeriod.to) {
        const toTimestamp = new Date(filters.timePeriod.to).getTime() / 1000;
        conditions.push(`sales.resaleAt <= ${toTimestamp}`);
      }
    }

    // Handle town filter
    if (filters.town && filters.town.length > 0) {
      const townIds = filters.town.join(',');
      conditions.push(`towns.id IN (${townIds})`);
    }

    // Handle flat type filter
    if (filters.flatType && filters.flatType.length > 0) {
      const flatTypeIds = filters.flatType.join(',');
      conditions.push(`flat_types.id IN (${flatTypeIds})`);
    }

    // Handle flat model filter
    if (filters.flatModel && filters.flatModel.length > 0) {
      const flatModelIds = filters.flatModel.join(',');
      conditions.push(`flat_models.id IN (${flatModelIds})`);
    }

    // Handle price range filter
    if (filters.price) {
      if (filters.price.min !== undefined) {
        conditions.push(`sales.price >= ${filters.price.min}`);
      }

      if (filters.price.max !== undefined) {
        conditions.push(`sales.price <= ${filters.price.max}`);
      }
    }

    // Handle area range filter
    if (filters.area) {
      if (filters.area.min !== undefined) {
        conditions.push(`sales.area >= ${filters.area.min}`);
      }

      if (filters.area.max !== undefined) {
        conditions.push(`sales.area <= ${filters.area.max}`);
      }
    }

    // Handle remaining lease filter
    if (filters.remainingLease) {
      if (filters.remainingLease.min !== undefined) {
        conditions.push(`sales.remainingLease >= ${filters.remainingLease.min}`);
      }

      if (filters.remainingLease.max !== undefined) {
        conditions.push(`sales.remainingLease <= ${filters.remainingLease.max}`);
      }
    }

    return conditions.join(' AND ');
  }

  getStatsQuery(db: any, filters: any) {
    let condition = 'WHERE 1 = 1';

    // Apply dynamic filters
    if (filters.townId?.length) {
      condition += ` AND townId IN (${filters.townId.map(escapeSQLiteValue).join(', ')})`;
    }

    if (filters.blockId?.length) {
      condition += ` AND blockId IN (${filters.blockId.map(escapeSQLiteValue).join(', ')})`;
    }

    if (filters.flatModelId?.length) {
      condition += ` AND flatModelId IN (${filters.flatModelId.map(escapeSQLiteValue).join(', ')})`;
    }

    if (filters.flatTypeId?.length) {
      condition += ` AND flatTypeId IN (${filters.flatTypeId.map(escapeSQLiteValue).join(', ')})`;
    }

    if (filters.timePeriod) {
      if (filters.timePeriod.from) {
        condition += ` AND resaleAt >= ${escapeSQLiteValue(Math.floor(new Date(filters.timePeriod.from).getTime() / 1000))}`;
      }

      if (filters.timePeriod.to) {
        condition += ` AND resaleAt <= ${escapeSQLiteValue(Math.floor(new Date(filters.timePeriod.to).getTime() / 1000))}`;
      }
    }

    if (!isNil(filters.minimumPrice)) {
      condition += ` AND price >= ${escapeSQLiteValue(filters.minimumPrice)}`;
    }

    if (!isNil(filters.maximumPrice)) {
      condition += ` AND price <= ${escapeSQLiteValue(filters.maximumPrice)}`;
    }

    if (!isNil(filters.minimumPsf)) {
      condition += ` AND ROUND(price/(area*10.73692), 2) >= ${escapeSQLiteValue(filters.minimumPsf)}`;
    }

    if (!isNil(filters.maximumPsf)) {
      condition += ` AND ROUND(price/(area*10.73692), 2) <= ${escapeSQLiteValue(filters.maximumPsf)}`;
    }

    if (!isNil(filters.minimumLeaseYear)) {
      condition += ` AND remain >= ${escapeSQLiteValue(filters.minimumLeaseYear * 12)}`;
    }

    if (!isNil(filters.maximumLeaseYear)) {
      condition += ` AND remain <= ${escapeSQLiteValue(filters.maximumLeaseYear * 12)}`;
    }

    if (!isNil(filters.minimumStorey)) {
      condition += ` AND startStorey >= ${escapeSQLiteValue(filters.minimumStorey)}`;
    }

    if (!isNil(filters.maximumStorey)) {
      condition += ` AND endStorey <= ${escapeSQLiteValue(filters.maximumStorey)}`;
    }

    if (!isNil(filters.area)) {
      condition += ` AND area = ${escapeSQLiteValue(filters.area)}`;
    }

    // SQL query to calculate highest, lowest, and median values for price and psf
    const query = /* SQL */ `
      WITH PriceData AS (
        SELECT price, ROUND(price / (area * 10.73692), 2) AS psf
        FROM sales
        ${condition}
      ),
      OrderedPrice AS (
        SELECT price FROM PriceData ORDER BY price
      ),
      OrderedPsf AS (
        SELECT psf FROM PriceData ORDER BY psf
      ),
      PriceCount AS (SELECT COUNT(*) as count FROM PriceData)

      SELECT
        MAX(price) AS max_price,
        MIN(price) AS min_price,
        MAX(psf) AS max_psf,
        MIN(psf) AS min_psf,
        (SELECT price FROM OrderedPrice LIMIT 1 OFFSET (SELECT (count - 1) / 2 FROM PriceCount)) AS median_price,
        (SELECT psf FROM OrderedPsf LIMIT 1 OFFSET (SELECT (count - 1) / 2 FROM PriceCount)) AS median_psf
      FROM PriceData;
    `;

    console.time('query time');
    console.log('DATA QUERY:', query);

    const res = db.exec(query);
    console.timeEnd('query time');

    return {
      data: res[0]?.values[0] ?? [],
      headers: res[0]?.columns ?? [],
    };
  }
}
