const jwt = require('jsonwebtoken');
const pool = require('../config/connection');
const SECRET_KEY = process.env.SECRET_KEY;

const authenticateToken = async (req, res, next) => {
    try {
        // Get token from header
        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

        if (!token) {
            return res.status(401).json({
                success: false,
                message: 'Access denied. No token provided.'
            });
        }

        // Check if token exists in database and is not expired
        const [tokenRows] = await pool.query(
            'SELECT * FROM tokens WHERE token = ? AND expires_at > NOW()',
            [token]
        );

        if (tokenRows.length === 0) {
            return res.status(401).json({
                success: false,
                message: 'Invalid or expired token.'
            });
        }

        // Verify the token
        jwt.verify(token, SECRET_KEY, async (err, decoded) => {
            if (err) {
                return res.status(403).json({
                    success: false,
                    message: 'Invalid token.'
                });
            }

            // Check if user still exists and is not deleted
            const [userRows] = await pool.query(
                'SELECT id, is_deleted FROM users WHERE id = ?',
                [decoded.userId]
            );

            if (userRows.length === 0 || userRows[0].is_deleted === 1) {
                return res.status(403).json({
                    success: false,
                    message: 'User not found or account deleted.'
                });
            }

            // Add user data to request
            req.user = decoded;
            next();
        });
    } catch (error) {
        console.error('Error in authenticateToken:', error);
        return res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};

module.exports = {
    authenticateToken
};


