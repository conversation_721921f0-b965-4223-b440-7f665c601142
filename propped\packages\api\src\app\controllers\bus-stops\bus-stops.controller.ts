import { Auth } from '../../guards/auth.guard';
import { Compress } from '../../receptor/compression.receptor';
import { BusStopModel, type IBusStop } from './bus-stops.model';

@Auth()
export class BusStopsController {
  /**
   * Find bus stops within 1km and 2km radius of a property
   * @param body Request body containing latitude and longitude
   * @returns Bus stops within 1km and 2km radius
   */
  @Compress()
  async findByLocation({ latitude, longitude }: { latitude: number; longitude: number }) {
    // Find bus stops within 1km radius
    const busStopsWithin1km = await BusStopModel.find({
      location: {
        $near: {
          $geometry: { type: 'Point', coordinates: [longitude, latitude] },
          $maxDistance: 1000,
        },
      },
    })
      .limit(50)
      .select('name details location')
      .lean<IBusStop[]>();

    // Find bus stops within 2km radius (excluding those already in 1km)
    const busStopsWithin2km = await BusStopModel.find({
      location: {
        $near: {
          $geometry: { type: 'Point', coordinates: [longitude, latitude] },
          $minDistance: 1000,
          $maxDistance: 2000,
        },
      },
    })
      .limit(50)
      .select('name details location')
      .lean<IBusStop[]>();

    return {
      success: true,
      center: { latitude, longitude },
      busStops: { withinOneKm: busStopsWithin1km, withinTwoKm: busStopsWithin2km },
      counts: { oneKm: busStopsWithin1km.length, twoKm: busStopsWithin2km.length },
    };
  }

  @Compress()
  async findBusStopsWithinRadius({
    latitude,
    longitude,
    radius,
  }: {
    latitude: number;
    longitude: number;
    radius: number;
  }) {
    // Find bus stops within radius
    const busStops = await BusStopModel.find({
      location: {
        $near: {
          $geometry: { type: 'Point', coordinates: [longitude, latitude] },
          $maxDistance: radius,
        },
      },
    })
      .select('name details location')
      .lean<IBusStop[]>();
    return {
      success: true,
      center: { latitude, longitude },
      busStops,
      count: busStops.length,
    };
  }
}
