const express = require('express');
const router = express.Router();
const userController = require('../controller/auth_controller/user_controller');
const { authenticateToken } = require('../middleware/isAuth');

// Protected routes - require authentication
router.get('/profile', authenticateToken, userController.getProfile);
router.post('/update-profile', authenticateToken, userController.updateProfile);
router.post('/change-password', authenticateToken, userController.changePassword);



module.exports = router;




