import { defineConfig, extract, setup, virtual } from '@twind/core';
import presetTailwind from '@twind/preset-tailwind';
import { render } from 'preact-render-to-string';
import { HTML } from './fun';

const hash = (() => {
  let i = 1;
  const hasMap = {} as any;
  return (x: string) => (hasMap[x] ??= `_${(i++).toString(36)}`);
})();

const tw = setup(
  {
    ...defineConfig({
      presets: [
        presetTailwind({ disablePreflight: true }),
        { theme: { fontFamily: { sans: ['Helvetica', 'sans-serif'], serif: ['Times', 'serif'] } } },
      ],
    }),
    hash,
  },
  () => (virtual as any)(false, false),
  null as any,
);

export const renderComplete = (x: any) => {
  const body = render(x);
  const { html, css } = extract(body, tw);
  return HTML`<style data-twind>${css}</style>${html}`;
};
