import { css } from '@lib/common/css-minifier';
import { h } from '@lib/web/v-html';

const React: any = h;
React.createElement = h;

const globalStyles = css`
  body,
  body *:not(html):not(style):not(br):not(tr):not(code) {
    font-family: Avenir, Helvetica, sans-serif;
    box-sizing: border-box;
  }

  body {
    background-color: #f5f8fa;
    color: #74787e;
    height: 100%;
    hyphens: auto;
    line-height: 1.4;
    margin: 0;
    -moz-hyphens: auto;
    -ms-word-break: break-all;
    width: 100% !important;
    -webkit-hyphens: auto;
    -webkit-text-size-adjust: none;
    word-break: break-all;
    word-break: break-word;
  }

  p,
  ul,
  ol,
  blockquote {
    line-height: 1.4;
    text-align: left;
  }

  a {
    color: #3869d4;
  }

  a img {
    border: none;
  }

  h1 {
    color: #2f3133;
    font-size: 19px;
    font-weight: bold;
    margin-top: 0;
    text-align: left;
  }

  h2 {
    color: #2f3133;
    font-size: 16px;
    font-weight: bold;
    margin-top: 0;
    text-align: left;
  }

  h3 {
    color: #2f3133;
    font-size: 14px;
    font-weight: bold;
    margin-top: 0;
    text-align: left;
  }

  p {
    color: #74787e;
    font-size: 16px;
    line-height: 1.5em;
    margin-top: 0;
    text-align: left;
  }

  p.sub {
    font-size: 12px;
  }

  img {
    max-width: 100%;
  }

  .wrapper {
    background-color: #f5f8fa;
    margin: 0;
    padding: 0;
    width: 100%;
    -premailer-cellpadding: 0;
    -premailer-cellspacing: 0;
    -premailer-width: 100%;
  }

  .content {
    margin: 0;
    padding: 0;
    width: 100%;
    -premailer-cellpadding: 0;
    -premailer-cellspacing: 0;
    -premailer-width: 100%;
  }

  .body {
    background-color: #ffffff;
    border-bottom: 1px solid #edeff2;
    border-top: 1px solid #edeff2;
    margin: 0;
    padding: 0;
    width: 100%;
    -premailer-cellpadding: 0;
    -premailer-cellspacing: 0;
    -premailer-width: 100%;
  }

  .inner-body {
    background-color: #ffffff;
    margin: 0 auto;
    padding: 0;
    width: 570px;
    -premailer-cellpadding: 0;
    -premailer-cellspacing: 0;
    -premailer-width: 570px;
  }

  .footer {
    margin: 0 auto;
    padding: 0;
    text-align: center;
    width: 570px;
    -premailer-cellpadding: 0;
    -premailer-cellspacing: 0;
    -premailer-width: 570px;
  }

  .footer p {
    color: #aeaeae;
    font-size: 12px;
    text-align: center;
  }

  /* Tables */

  .table table {
    margin: 30px auto;
    width: 100%;
    -premailer-cellpadding: 0;
    -premailer-cellspacing: 0;
    -premailer-width: 100%;
  }

  .table th {
    border-bottom: 1px solid #edeff2;
    padding-bottom: 8px;
    margin: 0;
  }

  .table td {
    color: #74787e;
    font-size: 15px;
    line-height: 18px;
    padding: 10px 0;
    margin: 0;
  }

  .btn {
    padding: 10px 20px;
    font-size: 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    color: white;
    margin: 5px;
    text-decoration: none;
  }

  .btn.normal {
    background-color: #007bff; /* Blue */
  }

  .btn.success {
    background-color: #28a745; /* Green */
  }

  .btn.error {
    background-color: #dc3545; /* Red */
  }

  .btn.warn {
    background-color: #ffc107; /* Yellow */
    color: black;
  }

  .btn:hover {
    opacity: 0.8;
  }

  @media only screen and (max-width: 600px) {
    .inner - body {
      width: 100% !important;
    }

    .footer {
      width: 100% !important;
    }
  }
`;

export const MailBase = (props: any) => {
  return (
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
        <style>
          {globalStyles}
          {props.css ?? ''}
        </style>
      </head>
      <body
        // biome-ignore lint/security/noDangerouslySetInnerHtml: <explanation>
        dangerouslySetInnerHTML={{ __html: props.html }}
      />
    </html>
  );
};

export const MailLayout = (props: {
  content: string;
  footer: string;
  headerContent: string;
  headerUrl: string;
}) => {
  return (
    <table class="wrapper" width="100%" cellPadding="0" cellSpacing="0">
      <tr>
        <td>
          <table class="content" width="100%" cellPadding="0" cellSpacing="0">
            <tr>
              <td style="padding-top: 24px;padding-bottom: 24px;text-align: center;">
                <a
                  style="color: #bbbfc3ff; font-size: 18px; font-weight: bold; text-decoration: none;"
                  href={props.headerUrl}
                >
                  {props.headerContent}
                </a>
              </td>
            </tr>
            <tr>
              <td class="body" width="100%">
                <table class="inner-body" width="570" cellPadding="0" cellSpacing="0">
                  <tr>
                    <td
                      style="padding: 36px;"
                      // biome-ignore lint/security/noDangerouslySetInnerHtml: <explanation>
                      dangerouslySetInnerHTML={{ __html: props.content }}
                    />
                  </tr>
                </table>
              </td>
            </tr>
            <tr>
              <td>
                <table class="footer" width="570" cellPadding="0" cellSpacing="0">
                  <tr>
                    <td style="padding: 36px;">{props.footer}</td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  );
};
