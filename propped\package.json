{"name": "@web/source", "version": "0.0.0", "license": "MIT", "scripts": {"tim": "node -r @swc-node/register ./tools/tim/main.ts", "web:serve": "nx serve web", "api:serve": "tsx watch --tsconfig ./packages/api/tsconfig.app.json ./packages/api/src/main.ts"}, "private": true, "dependencies": {"@angular/animations": "~19.2.11", "@angular/cdk": "~19.2.16", "@angular/common": "~19.2.11", "@angular/compiler": "~19.2.11", "@angular/core": "~19.2.11", "@angular/forms": "~19.2.11", "@angular/material": "~19.2.16", "@angular/material-date-fns-adapter": "^19.2.16", "@angular/platform-browser": "~19.2.11", "@angular/platform-browser-dynamic": "~19.2.11", "@angular/router": "~19.2.11", "@aws-sdk/client-s3": "3.744.0", "@aws-sdk/client-ses": "3.812.0", "@aws-sdk/s3-request-presigner": "3.744.0", "@aws-sdk/types": "3.804.0", "@hono/node-server": "1.14.2", "@nx/esbuild": "21.0.3", "@preact/signals": "2.0.4", "@twind/core": "1.1.3", "@twind/preset-tailwind": "1.1.4", "@types/mapbox-gl": "^3.4.1", "animate.css": "4.1.1", "change-case": "5.4.4", "chart.js": "4.4.9", "clsx": "2.1.1", "csso": "5.0.5", "daisyui": "^4.12.23", "date-fns": "^4.1.0", "drizzle-orm": "0.43.1", "esbuild": "0.25.4", "fast-glob": "3.3.3", "fetch-cookie": "^3.1.0", "fuzzy-search": "^3.2.1", "hono": "4.7.10", "htm": "3.1.1", "idb-keyval": "6.2.2", "jsonwebtoken": "9.0.2", "jsx-dom": "8.1.6", "mapbox-gl": "^3.12.0", "marked": "15.0.12", "marked-xhtml": "1.0.12", "mongodb": "6.16.0", "mongoose": "^8.15.0", "mysql2": "3.12.0", "ngx-mapbox-gl": "^12.0.0", "ngx-mat-select-search": "^8.0.1", "node-html-parser": "^7.0.1", "nodemailer": "6.10.0", "otpauth": "9.4.0", "pica": "9.0.1", "preact": "10.26.6", "qrcode": "1.5.4", "rxjs": "~7.8.2", "tailwind-merge": "^3.3.0", "yargs": "17.7.2", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "~19.2.12", "@angular-devkit/core": "~19.2.12", "@angular-devkit/schematics": "~19.2.12", "@angular/cli": "~19.2.12", "@angular/compiler-cli": "~19.2.11", "@angular/language-service": "~19.2.11", "@eslint/js": "^9.27.0", "@nx/angular": "21.0.3", "@nx/eslint": "21.0.3", "@nx/eslint-plugin": "21.0.3", "@nx/js": "21.0.3", "@nx/node": "21.0.3", "@nx/web": "21.0.3", "@nx/webpack": "21.0.3", "@schematics/angular": "~19.2.12", "@swc-node/register": "~1.10.10", "@swc/core": "~1.11.24", "@swc/helpers": "~0.5.17", "@types/google.maps": "3.58.1", "@types/node": "~22.15.19", "@typescript-eslint/utils": "^8.32.1", "angular-eslint": "^19.4.0", "autoprefixer": "^10.4.21", "aws-sdk": "2.1692.0", "chalk": "5.4.1", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "nx": "21.0.3", "postcss": "^8.5.3", "prettier": "^3.5.3", "tailwindcss": "^3.0.2", "tslib": "^2.8.1", "tsx": "^4.19.4", "typescript": "~5.8.3", "typescript-eslint": "^8.32.1"}, "pnpm": {"onlyBuiltDependencies": ["@swc/core", "esbuild", "lmdb", "nx"]}}