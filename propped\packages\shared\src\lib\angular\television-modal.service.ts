import type { <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Injectable,
  inject,
} from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import type { MatDialogConfig } from '@angular/material/dialog';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import type { Subject } from 'rxjs';
import { firstValueFrom } from 'rxjs';
import { EnsureSingleInstance } from '../common/test-root-service';

export enum PreviewFileType {
  IMG = 0,
  VID = 1,
  TEXT = 2,
  AUD = 3,
}

export interface TelevisionRef {
  close: () => void;
  setMedia: (mediaType: PreviewFileType, data: File | string) => void;
  dialogRef: MatDialogRef<TelevisionModalComponent>;
  onClose: Promise<any>;
}

@Component({
  selector: 'app-television-modal',
  imports: [MatButtonModule, MatDialogModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `<div class="h-full w-full flex flex-col item-center">
    <button mat-stroked-button mat-dialog-close color="warn" class="ml-auto !absolute right-0">
      Close
    </button>
    @if (url) {
      <img [src]="url" class="h-full w-full object-contain" />
    } @else if (urlVideo) {
      <video class="m-auto" autoplay (ended)="videoEnd()" (loadedmetadata)="videoLoaded($event)">
        <source [src]="urlVideo" type="video/mp4" />
      </video>
    } @else if (audioUrl) {
      <audio autoplay class="m-auto hidden">
        <source [src]="audioUrl" />
      </audio>
      <img class="m-auto" src="assets/images/music.gif" />
    }
    @if (text) {
      <div class="w-full flex justify-center">
        <div
          class="text-sm md:text-3xl text-center p-1 m-8 h-auto w-auto bg-black absolute bottom-48"
        >
          {{ text }}
        </div>
      </div>
    }
  </div> `,
  styles: [
    `
      :host {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
      }
    `,
  ],
})
export class TelevisionModalComponent implements OnInit, OnDestroy {
  readonly #dialogRef: MatDialogRef<TelevisionModalComponent> = inject(MatDialogRef);
  readonly cdRef = inject(ChangeDetectorRef);
  readonly #data: {
    data: Subject<{ value: any; actionType: any }>;
    onOpen: PromiseWithResolvers<TelevisionRef>;
  } = inject(MAT_DIALOG_DATA);
  public url?: string;
  public text?: string;
  public urlVideo?: string;
  public start = 0;
  public audioUrl?: any;

  public ngOnInit() {
    this.#data.onOpen.resolve({
      close: () => this.#dialogRef.close(),
      setMedia: (mediaType: PreviewFileType, data: File | string) => this.setMedia(mediaType, data),
      dialogRef: this.#dialogRef,
      onClose: firstValueFrom(this.#dialogRef.afterClosed()),
    });
  }

  public videoEnd() {
    this.close();
  }

  public videoLoaded({ target: vid }: any) {
    if (this.start) vid.currentTime = this.start / 1000;
  }

  public close() {
    this.#dialogRef.close();
  }

  public setMedia(mediaType: PreviewFileType, data: File | string) {
    if (mediaType === PreviewFileType.IMG) {
      if (data instanceof File) {
        this.revokeUrl(this.url);
        this.url = URL.createObjectURL(data);
      } else {
        this.url = data;
      }
    } else if (mediaType === PreviewFileType.VID) {
      if (data instanceof File) {
        this.revokeUrl(this.urlVideo);
        this.urlVideo = URL.createObjectURL(data);
      } else {
        this.urlVideo = data;
      }
    } else if (mediaType === PreviewFileType.AUD) {
      if (data instanceof File) {
        this.revokeUrl(this.audioUrl);
        this.audioUrl = URL.createObjectURL(data);
      } else {
        this.audioUrl = data;
      }
    } else if (mediaType === PreviewFileType.TEXT) {
      if (typeof data === 'string') this.text = data;
    }

    this.cdRef.detectChanges();
  }

  revokeUrl(url?: string) {
    if (url?.startsWith('blob:')) URL.revokeObjectURL(url);
  }

  ngOnDestroy(): void {
    this.revokeUrl(this.url);
    this.revokeUrl(this.urlVideo);
  }
}

@Injectable({ providedIn: 'root' })
export class TeleVisionModalService {
  readonly #dialog = inject(MatDialog);
  public dialogRef: MatDialogRef<any> | undefined;

  constructor() {
    EnsureSingleInstance(this);
  }

  public openAsTv(data: any = {}, opt: MatDialogConfig<any> = {}) {
    const onOpen = Promise.withResolvers<TelevisionRef>();
    this.dialogRef = this.#dialog.open(TelevisionModalComponent, {
      width: '100vw',
      height: '100vh',
      maxWidth: '100vw',
      maxHeight: '100vh',
      data: { data, onOpen },
      ...opt,
    });
    return onOpen.promise;
  }
}
