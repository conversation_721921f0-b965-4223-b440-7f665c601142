import { ROLES } from '@lib/common/const/roles';
import { smartStringSorter } from '@lib/common/fun';
import { PERMISSIONS } from '../permission.service';

export interface Menu {
  id: string;
  title: string;
  translate?: string;
  type: string;
  icon?: string;
  src?: string;
  roles?: string[];
  perm?: string;
  url: string;
}

export interface MenuGroup {
  id: string;
  title: string;
  translate?: string;
  type: string;
  icon?: string;
  iconSrc?: string;
  children: Menu[];
  roles?: string[];
  perm?: string;
}

export const Menus: MenuGroup[] = [
  {
    id: 'main',
    title: 'Main Menu',
    type: 'header',
    children: [
      {
        id: 'dash',
        title: 'Overview',
        type: 'item',
        icon: 'developer_board',
        url: '/',
        perm: PERMISSIONS.canSeeOverview,
      },
      {
        id: 'hdb-resale-trend',
        title: 'HDB Resale Trend',
        type: 'item',
        iconSrc: 'local:hdb',
        url: '/hdb-resale-trend',
        perm: PERMISSIONS.canSeeOverview,
      },
      // {
      //   id: 'hdb-sales',
      //   title: 'HDB',
      //   type: 'item',
      //   iconSrc: 'local:hdb',
      //   url: '/hdb-sales',
      //   perm: PERMISSIONS.canSeeOverview,
      // },
      // {
      //   id: 'condo-sales',
      //   title: 'Condo',
      //   type: 'item',
      //   iconSrc: 'local:ura-sg',
      //   url: '/condo-sales',
      //   perm: PERMISSIONS.canSeeCondoSales,
      // },
      {
        id: 'hdb-txn',
        title: 'HDB-Txn',
        type: 'item',
        iconSrc: 'local:hdb',
        url: '/hdb-transactions',
        perm: PERMISSIONS.canSeeHDBSales,
      },
      {
        id: 'condo-txn',
        title: 'Condo-Txn',
        type: 'item',
        iconSrc: 'local:ura-sg',
        url: '/condo-transactions',
        perm: PERMISSIONS.canSeeCondoSales,
      },
      {
        id: 'users',
        title: 'Users',
        type: 'item',
        icon: 'supervised_user_circle',
        url: '/users',
        roles: [ROLES.ADMIN.name, ROLES.SUPER_ADMIN.name],
        perm: PERMISSIONS.canSeeUsers,
      },
      {
        id: 'buyer-seller',
        title: 'Buyer/Seller Module',
        type: 'item',
        icon: 'real_estate_agent',
        url: '/buyer-seller',
        perm: PERMISSIONS.canSeeOverview,
      },
      {
        id: 'mapbox-overview',
        title: 'Map Box Overview',
        type: 'item',
        icon: 'map',
        url: '/mapbox-overview',
        perm: PERMISSIONS.canSeeOverview,
      },
      {
        id: 'user-dashboard',
        title: 'User Dashboard',
        type: 'item',
        icon: 'dashboard',
        url: '/user-dashboard',
        perm: PERMISSIONS.canSeeDashboard
      }
    ].sort(smartStringSorter('title')),
  },

  {
    id: 'system',
    title: 'System',
    type: 'header',
    children: [
      {
        id: 'account',
        title: 'Account',
        type: 'item',
        icon: 'manage_accounts',
        url: '/account',
        roles: [ROLES.MASTER.name, ROLES.SUPER_ADMIN.name],
        perm: PERMISSIONS.canSeeAccount,
      },
      {
        id: 'uac',
        title: 'UAC',
        type: 'item',
        icon: 'security',
        url: '/uac',
        perm: PERMISSIONS.canSeeUAC,
      },
    ].sort(smartStringSorter('title')),
  },
];
