import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { AnnotationModalService } from '../../../component/annotation/annotation-modal.service';
import { BuyerInfoComponent } from '../make-offer-buyer/buyer-info/buyer-info.component';
import { SellerInfoComponent } from '../make-offer-buyer/seller-info/seller-info.component';
import {apiRPC, injectController} from '@api/rpc';
import { AuthService } from '../../../services/auth.service';
import { SnackBarService } from '@lib/angular/snack-bar.service';
import { SalesChatController } from '@api/controllers/hdb-sales/sales-chat.controller';
import { MatMenuModule } from '@angular/material/menu';
import { MatExpansionModule } from '@angular/material/expansion';

interface Profile{
  id: number | null;
  name: string;
}

interface ListingData{
  _id: any,
  blockNumber: string,
  town?: string,
  price?: number,
  psf?: number,
  street?: string,
  salesCondition?: string[],
  listingId?: number,
  createdAt?: Date,
  user_id: number,
}

// Accept Offer Dialog Component
@Component({
  selector: 'app-accept-offer-dialog',
  template: `
    <div class="flex flex-col items-center justify-center p-6 relative">
      <button class="top-2 right-2 text-2xl absolute" (click)="onClose()">&times;</button>
      <div class="text-center text-lg font-bold mb-2">
        You are accepting an offer of <br /><span class="text-2xl"
          >S$ {{ data.amount | number }}</span
        >
      </div>
      <hr class="my-2 border-blue-600" />
      <div class="text-center font-bold mb-2">
        Please Review the<br />Terms & Conditions Once More
      </div>
      <div class="mb-2 w-full">
        <div class="text-sm font-semibold mb-1">Terms & Conditions</div>
        <ol class="list-decimal pl-5 text-blue-600 text-xs mb-2">
          <li>
            All offers, once sent, are considered an official offer price and cannot be rescinded
          </li>
          <li>
            All offers will last for 14 days and if seller does not respond, will be considered
            voided.
          </li>
          <li>
            You can only make a new offer once the seller rejects the current offer or if the offer
            has been voided.
          </li>
        </ol>
        <button
          class="w-full bg-blue-600 text-white rounded-lg py-2 font-bold mb-2"
          [class.opacity-60]="agreedTerms"
          [disabled]="agreedTerms"
          (click)="agreedTerms = true"
        >
          I agree to the Terms & Conditions
        </button>
      </div>
      <hr class="my-2 border-blue-600" />
      <div class="text-center font-bold mb-2">Illegal Modifications</div>
      <ol class="list-decimal pl-5 text-blue-600 text-xs mb-2">
        <li>Any illegal modifications shall be made good before handover</li>
        <li>
          Any cost associated with the removal of illegal modifications shall be borne by me (the
          seller(s))
        </li>
      </ol>
      <button
        class="w-full bg-blue-600 text-white rounded-lg py-2 font-bold mb-2"
        [class.opacity-60]="agreedIllegal"
        [disabled]="agreedIllegal"
        (click)="agreedIllegal = true"
      >
        I agree to the Terms & Conditions for Illegal Modifications
      </button>
      <button
        class="w-full bg-blue-600 text-white rounded-lg py-2 font-bold mt-2"
        [disabled]="!(agreedTerms && agreedIllegal)"
        (click)="acceptOffer()"
      >
        Yes, I Accept the Offer!
      </button>
    </div>
  `,
  standalone: true,
  imports: [CommonModule],
})
export class AcceptOfferDialogComponent {
  readonly #dialogRef: MatDialogRef<AcceptOfferDialogComponent> = inject(MatDialogRef);
  readonly data: { amount: number } = inject(MAT_DIALOG_DATA);


  agreedTerms = false;
  agreedIllegal = false;

  acceptOffer() {
    this.#dialogRef.close('accepted');
  }

  onClose() {
    this.#dialogRef.close();
  }
}

@Component({
  selector: 'app-seller-preview',
  templateUrl: './seller-preview.component.html',
  styleUrls: ['./seller-preview.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatIconModule,
    RouterModule,
    BuyerInfoComponent,
    SellerInfoComponent,
    MatMenuModule,
    MatExpansionModule,
  ],
})
export class SellerPreviewComponent {
  // Reuse the same state variables from preview-listing component
  // Inject the annotation modal service
  private annotationModalService = inject(AnnotationModalService);
  private dialog = inject(MatDialog);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  readonly authService = inject(AuthService);
  readonly #hdbSalesOfferController = injectController(apiRPC.HDBSalesOfferController);
  readonly #hdbSalesController = injectController(apiRPC.HDBSalesController);
  readonly #resaleTransactionController = injectController(apiRPC.HDBSalesTransactionController);
  readonly #snackBarService = inject(SnackBarService);
  readonly #salesChatController = injectController(apiRPC.SalesChatController);
  activeView = 'floorplan'; // Possible values: 'default', 'floorplan', 'photos'
  showMapModal = false;
  showPhotosExpanded = false;

  // Image slider state
  selectedPhotoType: string | null = null;
  selectedPhotoIndex = 0;
  showPhotoSlider = false;

  // Seller interaction specific variables
  sellerUsername = 'JohnDoe123';
  buyerUsername = 'CurrentUser456';
  chatMessage = '';
  offerAmount = 0;
  askingPrice = 750000; // This would come from the property data
  priceDifference = 0;
  showAnnotationTool = false;

  // Search within chat variables
  showSearchBar = false;
  searchQuery = '';
  searchResults: any[] = [];
  userData: Profile | null = {
    id: null,
    name: ""
  };
  sellerProfileData: Profile | null = {
    id: null,
    name: ""
  };

  listingData: ListingData | null = null;
  chatId: string | null = null;
  editingMessageId: string | null = null;
  editMessageText= '';
  chatMessages: any[] = [];
  sellerChats: any[] = []; // New property to store seller chats
  isChatsExpanded = false; // For chats accordion
  isChatsLoading = false; // Loading state for chats

  sellerOffers: any[] = [];
  isOffersLoading = false;
  isOffersExpanded = false;
  sellerAcceptedOffer: any | null = null;

  // Suggested questions for the chat
  suggestedQuestions = [
    'When was the last renovation done?',
    'How long have you lived here?',
    'Are there any issues with the property?',
    'What are the neighbors like?',
  ];



  // Dummy user profile
  userProfile = {
    username: 'Ben01234#',
    role: 'buyer',
  };


  toggleRole() {
    if (this.userProfile.role === 'buyer') {
      this.userProfile = {
        username: 'James456#',
        role: 'seller',
      };
    } else {
      this.userProfile = {
        username: 'Ben01234#',
        role: 'buyer',
      };
    }
  }

  // Track last offer and disable state
  lastOfferAmount: number | null = null;
  makeOfferDisabled = false;

  // For seller demo: single chat offer object
  chatOffer: any = null;

  // Modal and offer state for seller
  selectedOffer: any = null;
  showAcceptModal = false;
  acceptAgreedTerms = false;
  acceptAgreedIllegal = false;

  showCounterInput = false;
  counterOfferPrice: number | null = null;

  offerAccepted = false;
  offerAcceptedTimestamp = '';

  async ngOnInit(): Promise<void> {
    const user = await this.authService.getActiveUser();
    console.log("USER DATA", user);
    if(user) {
      this.userData.id = user.id;
      this.userData.name = user.name;
    }

    this.route.queryParams.subscribe(async (params) => {
      const offerId = params['offerId'];
      const listingId = params['listingId'];

      console.log("OFFER ID RECEIVED IN QUERY PARAMS", offerId);
      console.log("LISTING ID RECEIVED IN QUERY PARAMS", listingId);

      if (listingId) {
        try {
          // Fetch the listing details using the controller
          const response = await this.#hdbSalesController.getSalesListingById({ _id: listingId });
          console.log("LISTING RESPONSE", response);

          if (response.data) {
            const listing = response.data;
            this.listingData = listing;



            if(this.listingData.user_id !== this.userData.id){
              await this.getOfferDetails(this.listingData.listingId, this.userData.id);
            }

            // If user is the seller of this listing, fetch the accepted offer
            if(this.listingData.user_id === this.userData.id){
              await this.fetchAcceptedOfferForSeller(this.listingData.listingId, this.userData.id);
              // await this.getOfferDetails(this.listingData.listingId, this.userData.id);
            }

            this.askingPrice = listing.price || this.askingPrice;

            if (this.offerAmount) {
              this.priceDifference = this.offerAmount - this.askingPrice;
            }

            console.log("USER ID FOR LISTING", listing.user_id);
            // const sellerProfile = await this.#authController.getById(2);
            // console.log("SELLER PROFILE", sellerProfile);
            // this.sellerProfileData.id = sellerProfile.data.id;
            // this.sellerProfileData.name = sellerProfile.data.name;
            if(this.userData.id === listing.user_id){
              await this.fetchSellerChats();
            }

            if(this.userData.id !== listing.user_id){
              const chatResponse = await this.#salesChatController.createChat({
              listingId: listing._id.toString(),
              buyerId: this.userData.id,
              sellerId: this.listingData.user_id
            });
            console.log("CHAT RESPONSE", chatResponse);
            this.chatMessages = chatResponse.data.messages;
            this.chatId = chatResponse.data._id.toString();
          }


          }
        } catch (error) {
          console.error('Error fetching listing:', error);
          this.#snackBarService.error('Error fetching listing details');
        }
      }});
    }

  async fetchAcceptedOfferForSeller(listingId: number, sellerId: number): Promise<void> {
      try {
        const response = await this.#hdbSalesOfferController.getAcceptedOfferByListingAndSeller({
          listingId,
          sellerId
        });

        if (response.data) {
          this.sellerAcceptedOffer = response.data;
          console.log('Seller accepted offer:', this.sellerAcceptedOffer);
        }
      }
      catch (error) {
      console.error('Error fetching accepted offer for seller:', error);
    }
  }

  async getOfferDetails(listingId: number, buyerId: number): Promise<void> {
      try {
        // Fetch the offer details using the controller
        const response = await this.#hdbSalesOfferController.getOfferByListingId({ listingId, buyerId });
        console.log("OFFER RESPONSE", response);
        const offer = response.data;

        // Update the chat offer object
        this.chatOffer = {
          id: offer._id,
          amount: offer.offerPrice,
          status: offer.status,
          message: offer.message || '',
          createdAt: offer.createdAt,
          buyerId: offer.buyerId,
          timestamp: new Date().toISOString(),
          counterOfferPrice: offer?.counterOfferPrice,
          counterOfferMessage: offer?.counterOfferMessage
        };

        // Update the offer amount
        this.offerAmount = offer.offerPrice;
        this.priceDifference = this.offerAmount - this.askingPrice;

      } catch (error) {
        console.error('Error fetching offer:', error);
        this.#snackBarService.error('Error fetching offer details');
      }

  }

  // Navigate to dashboard after accepting offer
  navigateToDashboard(): void {
    this.router.navigate(['/user-dashboard']);
  }


  /**
   * Fetch all chats where the user is a seller
   */
  async fetchSellerChats(): Promise<void> {
    try {
      this.isChatsLoading = true;
      const response = await this.#salesChatController.getChatsBySellerId({
        sellerId: this.userData.id
      });

      console.log("LIST OF SELLER CHATS", response);

      if (response.error) {
        this.#snackBarService.error(response.error);
        return;
      }

      this.sellerChats = response.data;
      this.isChatsLoading = false;
    } catch (error) {
      console.error('Error fetching seller chats:', error);
      this.#snackBarService.error('Failed to fetch chats');
      this.isChatsLoading = false;
    }
  }

  // Toggle the chats accordion
  toggleChatsAccordion(): void {
    this.isChatsExpanded = !this.isChatsExpanded;
  }

  async loadChatMessages(chat: any): Promise<void> {

    console.log("CHAT SELECTED", chat);
    try {
      if (!chat || !chat._id) {
        this.#snackBarService.error('Invalid chat selected');
        return;
      }

      const response = await this.#salesChatController.getChatById({
        chatId: chat._id
      });

      console.log("CHAT RESPONSE", response);

      if (response.error) {
        this.#snackBarService.error(response.error);
        return;
      }

      this.chatId = chat._id;
      this.chatMessages = response.data.messages;

      // If there's an associated offer, load it
      if (response.data.offerId) {
        await this.loadOfferDetails(response.data.offerId);
      } else {
        // If no offer ID in chat, check if there's a pending offer for this buyer and listing
        try {
          // Get all offers for this seller
          const offersResponse = await this.#hdbSalesOfferController.getOffersBySellerId({
            sellerId: this.userData.id
          });

          console.log("OFFERS RESPONSE", offersResponse);

          if (!offersResponse.error && offersResponse.data) {
            // Find pending offers from the same buyer for the same listing
            const pendingOffer = offersResponse.data.find(
              (offer: any) =>
                offer.buyerId === chat.buyerId &&
                offer.listingId === chat.listingId?.listingId &&
                offer.status === 'pending'
            );

            if (pendingOffer) {
              this.chatOffer = {
                id: pendingOffer._id,
                amount: pendingOffer.offerPrice,
                status: pendingOffer.status,
                message: pendingOffer.message || '',
                createdAt: pendingOffer.createdAt,
                buyerId: pendingOffer.buyerId,
                timestamp: new Date(pendingOffer.createdAt).toISOString(),
                counterOfferPrice: pendingOffer?.counterOfferPrice,
                counterOfferMessage: pendingOffer?.counterOfferMessage
              };
            } else {
              this.chatOffer = null;
            }
          }
        } catch (error) {
          console.error('Error checking for pending offers:', error);
        }
      }

    } catch (error) {
      console.error('Error loading chat messages:', error);
      this.#snackBarService.error('Failed to load chat messages');
    }
  }

  // Load offer details if available
  async loadOfferDetails(offerId: string): Promise<void> {
    try {
      const response = await this.#hdbSalesOfferController.getOfferById({
        offerId: offerId
      });
      console.log("OFFER RESPONSE", response);
      this.chatOffer = response.data;
    } catch (error) {
      console.error('Error loading offer details:', error);
    }
  }

  /**
   * Format offer status for display
   */
  getStatusClass(status: string): string {
    switch (status) {
      case 'pending':
        return 'text-yellow-600';
      case 'accepted':
        return 'text-green-600';
      case 'rejected':
        return 'text-red-600';
      case 'countered':
        return 'text-blue-600';
      default:
        return 'text-gray-600';
    }
  }

  // Floorplan and Photos methods (copied from preview-listing component)
  toggleFloorplan(): void {
    if (this.activeView === 'floorplan') {
      this.activeView = 'default';
    } else {
      this.activeView = 'floorplan';
      this.showPhotosExpanded = false;
      this.closePhotoSlider();
    }
  }

  togglePhotos(): void {
    this.showPhotosExpanded = !this.showPhotosExpanded;
    if (this.showPhotosExpanded) {
      this.activeView = 'photos';
    } else if (this.activeView === 'photos') {
      this.activeView = 'floorplan';
    }
  }

  closeDetailView(): void {
    this.activeView = 'floorplan';
    this.showPhotosExpanded = false;
    this.closePhotoSlider();
  }

  // Photo slider methods
  openPhotoSlider(photoType: string, index = 0): void {
    this.selectedPhotoType = photoType;
    this.selectedPhotoIndex = index;
    this.showPhotoSlider = true;
  }

  closePhotoSlider(): void {
    this.showPhotoSlider = false;
    this.selectedPhotoType = null;
  }

  nextPhoto(): void {
    const photoObj = this.getDummyPhotos().find((p) => p.type === this.selectedPhotoType);
    if (photoObj && photoObj.images.length > 0) {
      this.selectedPhotoIndex = (this.selectedPhotoIndex + 1) % photoObj.images.length;
    }
  }

  prevPhoto(): void {
    const photoObj = this.getDummyPhotos().find((p) => p.type === this.selectedPhotoType);
    if (photoObj && photoObj.images.length > 0) {
      this.selectedPhotoIndex =
        this.selectedPhotoIndex === 0 ? photoObj.images.length - 1 : this.selectedPhotoIndex - 1;
    }
  }

  getCurrentSlideImage(): string {
    const photoObj = this.getDummyPhotos().find((p) => p.type === this.selectedPhotoType);
    if (photoObj && photoObj.images.length > this.selectedPhotoIndex) {
      return photoObj.images[this.selectedPhotoIndex];
    }
    return 'assets/images/photo-placeholder.jpg';
  }

  getPhotoCount(): number {
    const photoObj = this.getDummyPhotos().find((p) => p.type === this.selectedPhotoType);
    return photoObj ? photoObj.images.length : 0;
  }

  // Get dummy photos data
  getDummyPhotos() {
    return [
      {
        type: 'bedroom',
        placeholderImg: 'assets/properties/bedroom-1.jpg',
        images: ['assets/properties/bedroom-1.jpg', 'assets/properties/bedroom-2.jpg'],
      },
      {
        type: 'kitchen',
        placeholderImg: 'assets/properties/kitchen-1.jpg',
        images: ['assets/properties/kitchen-1.jpg', 'assets/properties/kitchen-2.jpg'],
      },
      {
        type: 'balcony',
        placeholderImg: 'assets/properties/balcony-1.jpg',
        images: ['assets/properties/balcony-1.jpg', 'assets/properties/balcony-2.jpg'],
      },
      {
        type: 'living-room',
        placeholderImg: 'assets/properties/living-room-1.jpg',
        images: ['assets/properties/living-room-1.jpg', 'assets/properties/living-room-2.jpg'],
      },
    ];
  }

  // Seller interaction specific methods
  insertSuggestedQuestion(question: string): void {
    this.chatMessage = this.chatMessage ? this.chatMessage + '\n' + question : question;
  }

  async sendMessage(): Promise<void> {

    if(!this.chatMessage){
      this.#snackBarService.warn("Message cannot be empty");
      return;
    }

    console.log("CHAT ID", this.chatId);
    console.log("MESSAGE", this.chatMessage);

    try {
      const response = await this.#salesChatController.addMessage({
        chatId: this.chatId,
        message: this.chatMessage,
        role: this.listingData.user_id === this.userData.id ? "seller" : "buyer",
        userId: this.userData.id,
        messageType: "text"
      });
      console.log("MESSAGE RESPONSE", response);
      this.chatMessages = response.data.messages;
      this.chatMessage = '';
    }catch(error){
      console.error("Error sending message", error);
      this.#snackBarService.error("Error sending message");
    }
  }

  startEditMessage(message: any): void {
    this.editingMessageId = message._id;
    this.editMessageText = message.message;
  }

  async saveEditedMessage(): Promise<void> {
    if (!this.editingMessageId || !this.editMessageText || !this.chatId) {
      this.#snackBarService.warn("Cannot save empty message");
      return;
    }

    try {
      const response = await this.#salesChatController.editMessage({
        chatId: this.chatId,
        messageId: this.editingMessageId,
        newMessage: this.editMessageText,
        userId: this.userData.id
      });

      if (response.error) {
        this.#snackBarService.error(response.error);
        return;
      }

      this.chatMessages = response.data.messages;
      this.cancelEditMessage();
      this.#snackBarService.success("Message updated successfully");
    } catch (error) {
      console.error("Error updating message", error);
      this.#snackBarService.error("Error updating message");
    }
  }

  cancelEditMessage(): void {
    this.editingMessageId = null;
    this.editMessageText = '';
  }

  async deleteMessage(messageId: string): Promise<void> {
    if (!this.chatId) return;

    try {
      const confirmed = confirm("Are you sure you want to delete this message?");
      if (!confirmed) return;

      const response = await this.#salesChatController.deleteMessage({
        chatId: this.chatId,
        messageId: messageId,
        userId: this.userData.id
      });

      if (response.error) {
        this.#snackBarService.error(response.error);
        return;
      }

      this.chatMessages = response.data.messages;
      this.#snackBarService.success("Message deleted successfully");
    } catch (error) {
      console.error("Error deleting message", error);
      this.#snackBarService.error("Error deleting message");
    }
  }


  exportChat(): void {
    // Implement chat export logic
    console.log('Exporting chat');
  }

  updateOfferAmount(): void {
    this.priceDifference = this.offerAmount - this.askingPrice;
  }

  previewOffer(): void {
    // Implement offer preview logic
    console.log('Previewing offer of $', this.offerAmount);
  }

  openAnnotationTool(): void {
    // Open the annotation modal
    this.annotationModalService.openAnnotationModal().subscribe((result) => {
      if (result && result.annotatedImage) {
        console.log('Annotated image received:', result.annotatedImage);
      }
    });
  }

  // Search within chat methods
  toggleSearchBar(): void {
    this.showSearchBar = !this.showSearchBar;
    if (!this.showSearchBar) {
      this.searchQuery = '';
      this.searchResults = [];
    }
  }

  searchInChat(): void {
    // Implement actual search logic here
    // For now, just log the search query
    console.log('Searching for:', this.searchQuery);
  }

  onMakeOffer() {
    this.router.navigate(['/buyer-seller/make-offer-buyer'], {
      queryParams: {
        listing_id: this.listingData.listingId,
        id: this.listingData._id
      },
    });
  }

    // Handle accepting an offer
    async onAcceptOffer(offer: any): Promise<void> {
      // Open the accept offer dialog
      const dialogRef = this.dialog.open(AcceptOfferDialogComponent, {
        width: '400px',
        data: { amount: offer.amount }
      });

      // Handle the dialog result
      dialogRef.afterClosed().subscribe(async (result) => {
        if (result === 'accepted') {
          try {
            // Call the API to update the offer status
            await this.#hdbSalesOfferController.acceptOffer({
              offerId: offer.id
            });

            console.log(this.listingData);
            console.log("Accepted Offer Details", offer);

            await this.#resaleTransactionController.createTransaction({
              offerId: offer.id,
              listingId: this.listingData._id,
              sellerId: this.userData.id,
              buyerId: offer.buyerId,
              transactionAmount: offer.amount
            })

            console.log("Resale Transaction doc created")
            // Update the local offer status
            this.chatOffer.status = 'accepted';

            // Set offer accepted state
            this.offerAccepted = true;
            this.offerAcceptedTimestamp = new Date().toISOString();

            // Show success message
            this.#snackBarService.success('Offer accepted successfully');

            // Refresh the chat list
            await this.fetchSellerChats();
          } catch (error) {
            console.error('Error accepting offer:', error);
            this.#snackBarService.error('Failed to accept offer');
          }
        }
      });
    }
    // Handle rejecting an offer
    async onRejectOffer(offer: any): Promise<void> {
      try {
        // Call the API to update the offer status
        await this.#hdbSalesOfferController.rejectOffer({
          offerId: offer.id
        });

        // Update the local offer status
        this.chatOffer.status = 'rejected';

        // Show success message
        this.#snackBarService.info('Offer rejected');

        // Refresh the chat list
        await this.fetchSellerChats();
      } catch (error) {
        console.error('Error rejecting offer:', error);
        this.#snackBarService.error('Failed to reject offer');
      }
    }

    // Show counter offer input
    showCounterOfferInput(): void {
      this.showCounterInput = true;
      this.counterOfferPrice = this.chatOffer ? this.chatOffer.amount : null;
    }

    // Cancel counter offer
    cancelCounterOffer(): void {
      this.showCounterInput = false;
      this.counterOfferPrice = null;
    }

    // Submit counter offer
    async submitCounterOffer(): Promise<void> {
      if (!this.counterOfferPrice || !this.chatOffer) {
        this.#snackBarService.error('Please enter a valid counter offer price');
        return;
      }

      try {
        // Call the API to update the offer with counter offer
        await this.#hdbSalesOfferController.counterOffer({
          offerId: this.chatOffer.id,
          counterOfferPrice: this.counterOfferPrice,
          counterOfferMessage: 'Counter offer from seller'
        });

        // Update the local offer
        this.chatOffer.status = 'countered';
        this.chatOffer.counterOfferPrice = this.counterOfferPrice;

        // Reset counter offer UI
        this.showCounterInput = false;
        this.counterOfferPrice = null;


        // Refresh the chat list
        await this.fetchSellerChats();
      } catch (error) {
        console.error('Error submitting counter offer:', error);
        this.#snackBarService.error('Failed to submit counter offer');
      }
    }
}
