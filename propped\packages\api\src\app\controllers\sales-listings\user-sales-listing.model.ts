import { type Model, Schema, type Types, model } from 'mongoose';

export interface IUserSalesListing {
  _id?: Types.ObjectId;
  propertyType: Types.ObjectId;
  town: Types.ObjectId;
  street: string;
  block: number;
  postalCode: number;
  floor: number;
  unit: number;

  location: {
    type: 'Point';
    coordinates: [number, number]; // [longitude, latitude]
  };

  additionalDetails?: {
    renovation?: {
      style?: string;
      lastDone?: number; // Year
      renovationCost?: number;
    };
  };

  propertyMedia?: {
    mainDoor?: { type: string; url: string; file?: string }[];
    kitchen?: { type: string; url: string; file?: string }[];
    balcony?: { type: string; url: string; file?: string }[];
    bed?: { type: string; url: string; file?: string }[];
    study?: { type: string; url: string; file?: string }[];
    walkthroughVideo?: string;
    floorplan?: string;
  };

  sellerMessage?: {
    text?: string;
    style?: string;
  };

  suggestions?: string[];
  targetSellingPrice?: number;
  targetSellingPSF?: number;

  saleConditions?: {
    vacantPossession?: boolean;
    extensionOfStay?: number; // in months
    otherConditions?: string;
  };

  propertyDetails?: {
    area?: number;
    flatType?: Types.ObjectId;
    floor?: number;
    bedrooms?: number;
    bathrooms?: number;
    floorplan?: number;
    developer?: string;
    model?: string;
    yearOfCompletion?: number;
  };
}

const UserSalesListingSchema = new Schema<IUserSalesListing>({
  propertyType: { type: Schema.Types.ObjectId, ref: 'HDBBuildingType', required: true },
  town: { type: Schema.Types.ObjectId, ref: 'towns', required: true },
  street: { type: String },
  block: { type: Number, required: true },
  postalCode: { type: Number, required: true },
  floor: { type: Number, required: true },
  unit: { type: Number, required: true },

  location: {
    type: { type: String, enum: ['Point'], default: 'Point' },
    coordinates: { type: [Number], default: undefined }, // longitude, latitude
  },

  additionalDetails: {
    renovation: {
      style: { type: String, default: null },
      lastDone: { type: Number, default: null },
      renovationCost: { type: Number, default: null },
    },
  },

  propertyMedia: {
    mainDoor: [{ type: { type: String }, url: String, file: String }],
    kitchen: [{ type: { type: String }, url: String, file: String }],
    balcony: [{ type: { type: String }, url: String, file: String }],
    bed: [{ type: { type: String }, url: String, file: String }],
    study: [{ type: { type: String }, url: String, file: String }],
    walkthroughVideo: { type: String, default: null },
    floorplan: { type: String, default: null },
  },

  sellerMessage: {
    text: { type: String, default: null },
    style: { type: String, default: null },
  },

  suggestions: [{ type: String }],
  targetSellingPrice: { type: Number, default: null },
  targetSellingPSF: { type: Number, default: null },

  saleConditions: {
    vacantPossession: { type: Boolean, default: false },
    extensionOfStay: { type: Number, default: null }, // months
    otherConditions: { type: String, default: null },
  },

  propertyDetails: {
    area: { type: Number, default: null },
    flatType: { type: Schema.Types.ObjectId, ref: 'HDBApartmentType', default: null },
    floor: { type: Number, default: null },
    bedrooms: { type: Number, default: null },
    bathrooms: { type: Number, default: null },
    developer: { type: String, default: null },
    model: { type: String, default: null },
    yearOfCompletion: { type: Number, default: null },
  },
});

// Geo Index for location
UserSalesListingSchema.index({ location: '2dsphere' });

export const UserSalesListingModel: Model<IUserSalesListing> = model<IUserSalesListing>(
  'UserSalesListing',
  UserSalesListingSchema,
);
