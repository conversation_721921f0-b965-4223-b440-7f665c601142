import {} from '@angular/common';
import { Component, type OnInit, inject } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { apiRPC, injectController } from '@api/rpc';
import { SilverSearchBarComponent } from '@lib/angular/search-bar.component';
import { SnackBarService } from '@lib/angular/snack-bar.service';
import { ROLES } from '@lib/common/const/roles';
import { FuzzySearch } from '@lib/common/fuzzy-search';
import { capitalCase } from 'change-case';
import { AppCommonModule } from '../../common/common.module';
import { PERMISSIONS } from '../../services/permission.service';

@Component({
  imports: [MatIconModule, SilverSearchBarComponent, AppCommonModule],
  selector: 'app-uac',
  templateUrl: './uac.component.html',
})
export class UACComponent implements OnInit {
  readonly #settingsController = injectController(apiRPC.SettingsController);
  readonly #snackBarService = inject(SnackBarService);
  public ROLES = ROLES;
  public permissions = Object.keys(PERMISSIONS);
  public permissionsTitleCase = Object.fromEntries(
    this.permissions.map((v) => [v, capitalCase(v.replace(/^canSee/, ''))]),
  );
  public permissionMap = {} as any;
  public id = {} as any;
  public roleNames = Object.keys(ROLES);
  public uac: typeof FuzzySearch;
  public searchResult: any;

  public ngOnInit() {
    this.getAll();
  }

  public async getAll() {
    const result = await this.#settingsController.getOne({ key: 'permission' });
    this.permissionMap = JSON.parse(result?.data?.value || '{}');
    this.permissionMap ||= this.defaultPermissionMap();
    const canSee = this.permissions.map((item) => ({ name: item }));
    this.uac = new FuzzySearch(canSee, ['name'], { sort: true });
    this.search('');
    return this.uac;
  }

  public search(query: string) {
    this.searchResult = this.uac.search(query);
  }

  public defaultPermissionMap() {
    return Object.fromEntries(
      this.searchResult.map((v) => [v.name, [ROLES.SUPER_ADMIN.id, ROLES.USER.id]]),
    );
  }

  public onCheckChange(permission: string, role: number, selected: boolean) {
    this.permissionMap[permission] ||= [];
    const p = this.permissionMap[permission] as number[];
    const ix = p.indexOf(role);
    if (selected) {
      if (ix < 0) p.push(role);
    } else {
      if (ix >= 0) p.splice(ix, 1);
    }
  }

  public async update() {
    await this.#settingsController.update(
      { key: 'permission' },
      { value: JSON.stringify(this.permissionMap) },
    );
    this.#snackBarService.success('Updated Permissions');
  }
}
