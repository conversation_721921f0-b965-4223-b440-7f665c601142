import { DateFnsAdapter, MAT_DATE_FNS_FORMATS } from '@angular/material-date-fns-adapter';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { enGB } from 'date-fns/locale';

export const CUSTOM_DATE_FORMAT = [
  { provide: MAT_DATE_LOCALE, useValue: enGB },
  { provide: DateAdapter, useClass: DateFnsAdapter, deps: [MAT_DATE_LOCALE] },
  { provide: MAT_DATE_FORMATS, useValue: MAT_DATE_FNS_FORMATS },
];
