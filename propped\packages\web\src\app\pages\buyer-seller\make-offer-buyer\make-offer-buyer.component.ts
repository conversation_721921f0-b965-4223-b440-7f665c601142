import { CommonModule } from '@angular/common';
import { Component, inject, type OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { Router,ActivatedRoute } from '@angular/router';
import { OfferSentDialogComponent } from './offer-sent-dialog.component'; // Ensure correct path
import { apiRPC, injectController } from '@api/rpc';
import { AuthService } from '@web/services/auth.service';
import { SnackBarService } from '@lib/angular/snack-bar.service';

@Component({
  selector: 'app-make-offer-buyer',
  templateUrl: './make-offer-buyer.component.html',
  styleUrls: [],
  standalone: true,
  imports: [CommonModule, FormsModule, MatIconModule],
})
export class MakeOfferBuyerComponent implements OnInit {
  readonly #hdbSalesController = injectController(apiRPC.HDBSalesController);
  readonly #hdbSalesOfferController = injectController(apiRPC.HDBSalesOfferController);
  readonly #salesChatController = injectController(apiRPC.SalesChatController);
  readonly #snackBarService = inject(SnackBarService);

  askingPrice = 750000; // Example asking price
  offerPrice = 0;
  userName: string | null = null;
  userAvatar = 'https://ui-avatars.com/api/?name=Ben01234';
  isEditable = true; // Set to false if you want readonly

  // Seller info
  sellerInfo: any = null;

  // Preview and agreement state
  showPreview = false;
  agreedTerms = false;
  agreedSale = false;
  offerAccepted = false;
  offerCreated = false;
  userId: number | null = null;
  listingId: number | null = null;  //unique five digit id of listing
  _id: string | null = null;  //actual mongoDB id of document


  salesListingData: any = null;

  // Inject MatDialog and Router
  dialog = inject(MatDialog);
  readonly router = inject(Router)
  readonly route = inject(ActivatedRoute)
  private authService = inject(AuthService)

  async ngOnInit() {

    const user = await this.authService.getActiveUser();
    this.userId = user.id;
    this.userName = user.name;

    this.route.queryParams.subscribe(async(params)=>{
      this.listingId = params['listing_id'];
      if (params['id']) {
        this._id = params['id'];
        // Fetch the listing from the backend
        const res = await this.#hdbSalesController.getSalesListingById({ _id: params['id'] });
        this.askingPrice = res?.data?.price;
        this.salesListingData = res?.data || null;

        if(this.salesListingData.user_id !== this.userId){
          const isOfferExists = await this.#hdbSalesOfferController.getOfferByListingId({ listingId: this.listingId, buyerId: this.userId });
          console.log("IS OFFER EXISTS", isOfferExists);
          if(isOfferExists.data){
            this.offerCreated = true;
            this.offerPrice = isOfferExists.data.offerPrice;
          }
        }
       }

    })
    // Fallback dummy data
    if (!this.salesListingData) {
      this.salesListingData = {
        street: 'Compassvale Bow',
        blockNo: 'Block 266C',
        town: 'Sengkang Town',
        price: 800000,
        psf: 6157,
        listedDate: '17th August 2024',
        listingId: '01234',
        saleConditions: ['Extension of Stay (3 months)', 'Vacant Possession'],
      };
    }

  }

  get offerDifference(): number {

    console.log("OFFER PRICE",this.offerPrice);
    console.log("ASKING PRICE",this.askingPrice);

    console.log("OFFER PRICE",this.askingPrice - this.offerPrice);
    return this.askingPrice - this.offerPrice;
  }

  get offerDifferencePercent(): string {
    const percent = ((this.askingPrice - this.offerPrice)/ this.askingPrice) * 100;
    console.log("PERCENT",percent);
    return percent.toFixed(1);
  }

  onPreviewOffer() {
    this.showPreview = true;
  }

  canProceed(): boolean {
    return this.agreedTerms && this.agreedSale && this.offerPrice > 0;
  }

  async onProceedOffer() {

    if(this.offerPrice <= 0){
      this.#snackBarService.warn("Offer price cannot be less than or equal to 0");
      return;
    }
    if (!this.canProceed()) return;

    try {
      // Call createOffer API
      const res = await this.#hdbSalesOfferController.createOffer({
        listingId: this.listingId,
        buyerId: this.userId,
        sellerId: this.salesListingData.user_id,
        offerPrice: this.offerPrice,
        message: '', // Add message if you have a field for it
      });

      console.log("Offer created with ID:", res.data);
      this.offerCreated = true;
      this.#snackBarService.success("Submitted offer successfully");

      await this.#salesChatController.createChat({
        listingId: this._id,
        buyerId: this.userId,
        sellerId: this.salesListingData.user_id,
      });

      // Open the offer sent dialog and pass the offer ID and price
      const dialogRef = this.dialog.open(OfferSentDialogComponent, {
        width: '400px',
        disableClose: true,
        data: {
          offerId: res.data,
          offerPrice: this.offerPrice,
          listingId: this.salesListingData._id
        }
      });
    } catch (error) {
      console.error("Error creating offer:", error);
    }
  }
}
