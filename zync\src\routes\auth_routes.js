const express = require('express');
const router = express.Router();
const authController = require('../controller/auth_controller/auth_controller');
const { authenticateToken } = require('../middleware/isAuth');

// ========================================
// PUBLIC ENDPOINTS (No Auth Required)
// ========================================

// Registration Flow (3 steps)
router.post('/register', authController.register); // Step 1: Email verification
router.post('/verify-register-otp', authController.verifyRegisterOTP); // Step 2: OTP verification
router.post('/complete-registration', authController.completeRegistration); // Step 3: Account creation

// Login Flow (with 2FA support)
router.post('/login', authController.login); // Step 1: Username/Email + Password
router.post('/verify-tfa-email', authController.verifyTFAEmail); // Step 2a: Email OTP verification
router.post('/verify-totp', authController.verifyTOTP); // Step 2b: TOTP verification

// Password Reset Flow (3 steps)
router.post('/forgot-password', authController.forgotPassword); // Step 1: Request reset
router.post('/verify-forgot-password-otp', authController.verifyForgotPasswordOTP); // Step 2: Verify token
router.post('/reset-password', authController.resetPassword); // Step 3: Set new password

// Email Activation (for unverified accounts)
router.post('/active-mail', authController.activeMail); // Resend activation OTP
router.post('/verify-active-mail-otp', authController.verifyActiveMailOTP); // Verify activation OTP

// Validation Endpoints
router.post('/check-email-exists', authController.checkEmailExists);
router.post('/check-username-exists', authController.checkUsernameExists);

// ========================================
// PROTECTED ENDPOINTS (Auth Required)
// ========================================

// 2FA Management
router.post('/toggle-tfa', authenticateToken, authController.toggleTFA); // Enable/Disable 2FA
router.post('/setup-totp', authenticateToken, authController.setupTOTP); // Generate TOTP QR code
router.post('/confirm-totp-setup', authenticateToken, authController.confirmTOTPSetup); // Confirm TOTP setup

// Session Management
router.post('/logout', authenticateToken, authController.logout);

module.exports = router;