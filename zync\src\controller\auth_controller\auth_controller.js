const pool = require("../../config/connection");
const bcrypt = require("bcrypt");
const jwt = require("jsonwebtoken");
const SECRET_KEY = process.env.SECRET_KEY;
const emailService = require("../../utils/emailService");
const authUtils = require("../../utils/authUtils");

const saltRounds = 10;

// ========================================
// REGISTRATION FLOW (3 STEPS)
// ========================================

// --------- Step 1: Email Verification (Check if email exists and send OTP)
const register = async (req, res) => {
  try {
    const { email } = req.body;

    // Validate email format
    if (!authUtils.isValidEmail(email)) {
      return res.status(400).json({
        success: false,
        message: "Please enter a valid email address.",
      });
    }

    // Check if email already exists
    const [userRows] = await pool.query(
      "SELECT email, is_email_verified, is_deleted FROM users WHERE email = ?",
      [email]
    );

    if (userRows.length > 0) {
      const user = userRows[0];

      // If account was deleted
      if (user.is_deleted === 1) {
        return res.status(400).json({
          success: false,
          message: "Email already registered. Please contact admin.",
        });
      }

      // If email already verified
      if (user.is_email_verified === 1) {
        return res.status(400).json({
          success: false,
          message: "Email already registered. Please login instead.",
        });
      }
    }

    // Generate and store OTP
    const otp = await authUtils.generateUniqueOTP();
    await authUtils.storeOTP(email, otp);

    // Send OTP via email
    try {
      await emailService.sendRegistrationOTP(email, otp);
    } catch (emailError) {
      console.error("Error sending registration email:", emailError);
      // Continue with the response even if email fails
    }

    res.status(200).json({
      success: true,
      message: "Verification code sent to your email. Please check your inbox.",
      // otp, // ⚠️ Remove in production
    });
  } catch (error) {
    console.error("Error in register:", error);
    res.status(500).json({
      success: false,
      message: "Internal Server Error",
      error: error.message,
    });
  }
};

// --------- Login (Step 1: Username/Email + Password)
const login = async (req, res) => {
  try {
    const { identifier, password } = req.body; // identifier can be username or email

    // Validate inputs
    if (!identifier || !password) {
      return res.status(400).json({
        success: false,
        message: "Username/email and password are required.",
      });
    }

    // Find user by username OR email
    const [userRows] = await pool.query(
      `SELECT u.id, u.name, u.username, u.email, u.password, u.is_email_verified,
              u.is_deleted, u.isActive, u.meta, u.secret
       FROM users u
       WHERE (u.username = ? OR u.email = ?) AND u.is_deleted = 0`,
      [identifier, identifier]
    );

    // Check if user exists
    if (userRows.length === 0) {
      return res.status(400).json({
        success: false,
        message: "Invalid credentials. Please check your username/email and password.",
      });
    }

    const user = userRows[0];

    // Check if account is active
    if (!user.isActive) {
      return res.status(403).json({
        success: false,
        message: "Your account has been deactivated. Please contact admin.",
      });
    }

    // Check if email is verified
    if (!user.is_email_verified) {
      return res.status(401).json({
        success: false,
        message: "Email not verified. Please verify your email first.",
        requireEmailVerification: true,
        email: user.email,
      });
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: "Invalid credentials. Please check your username/email and password.",
      });
    }

    // Parse meta data
    const meta = typeof user.meta === 'string' ? JSON.parse(user.meta) : user.meta;
    const secret = typeof user.secret === 'string' ? JSON.parse(user.secret) : user.secret;

    // Check if 2FA is required
    if (meta.TFARequire === true) {
      // Check if user has TOTP setup
      if (secret.totp) {
        // User has TOTP setup - offer both TOTP and Email OTP options
        return res.status(200).json({
          success: true,
          message: "2FA required. Choose your verification method.",
          requireTFA: true,
          tfaOptions: {
            totp: true,
            email: true,
          },
          tempToken: jwt.sign({ userId: user.id, step: 'tfa_pending' }, SECRET_KEY, { expiresIn: '10m' }),
        });
      } else {
        // User has 2FA enabled but no TOTP setup - send email OTP automatically
        const otp = await authUtils.generateUniqueOTP();
        await authUtils.storeOTP(user.email, otp);

        try {
          await emailService.send2FAOTP(user.email, otp);
        } catch (emailError) {
          console.error("Error sending 2FA email:", emailError);
        }

        return res.status(200).json({
          success: true,
          message: "2FA verification code sent to your email.",
          requireTFA: true,
          tfaOptions: {
            totp: false,
            email: true,
          },
          tempToken: jwt.sign({ userId: user.id, step: 'tfa_pending' }, SECRET_KEY, { expiresIn: '10m' }),
        });
      }
    }

    // No 2FA required - proceed with normal login
    const deviceType = req.headers['user-agent']?.includes('Mobile') ? 'android' : 'web';
    const tokenData = authUtils.generateJWTToken(
      { userId: user.id, username: user.username, email: user.email },
      deviceType
    );

    // Store token in database
    await pool.query(
      "INSERT INTO tokens (user_id, token, device_type, expires_at) VALUES (?, ?, ?, ?)",
      [user.id, tokenData.token, deviceType, tokenData.expiryDate]
    );

    // Prepare user data response
    const userData = {
      id: user.id,
      name: user.name,
      username: user.username,
      email: user.email,
    };

    res.status(200).json({
      success: true,
      message: "Login successful",
      data: userData,
      token: tokenData.token,
    });
  } catch (error) {
    console.error("Error in login:", error);
    res.status(500).json({
      success: false,
      message: "Internal Server Error",
      error: error.message,
    });
  }
};

// --------- Step 2: OTP Verification
const verifyRegisterOTP = async (req, res) => {
  try {
    const { email, otp } = req.body;

    // Validate inputs
    if (!email || !otp) {
      return res.status(400).json({
        success: false,
        message: "Email and OTP are required.",
      });
    }

    // Verify OTP
    const isValidOTP = await authUtils.verifyOTP(email, otp, 10); // 10 minutes expiry
    if (!isValidOTP) {
      return res.status(400).json({
        success: false,
        message: "Invalid or expired OTP. Please try again.",
      });
    }

    // Delete OTP after successful verification
    await authUtils.deleteOTP(email);

    res.status(200).json({
      success: true,
      message: "Email verified successfully! Please complete your registration.",
      data: { email, verified: true },
    });
  } catch (error) {
    console.error("Error in verifyRegisterOTP:", error);
    res.status(500).json({
      success: false,
      message: "Internal Server Error",
      error: error.message,
    });
  }
};

// --------- Step 3: Complete Registration (Account Creation)
const completeRegistration = async (req, res) => {
  try {
    const { email, name, username, password, confirmPassword } = req.body;

    // Validate inputs
    if (!email || !name || !username || !password || !confirmPassword) {
      return res.status(400).json({
        success: false,
        message: "All fields are required.",
      });
    }

    // Validate password match
    if (password !== confirmPassword) {
      return res.status(400).json({
        success: false,
        message: "Passwords do not match.",
      });
    }

    // Validate password strength
    const passwordValidation = authUtils.validatePassword(password);
    if (!passwordValidation.isValid) {
      return res.status(400).json({
        success: false,
        message: "Password does not meet requirements.",
        errors: passwordValidation.errors,
      });
    }

    // Validate username format
    if (!authUtils.isValidUsername(username)) {
      return res.status(400).json({
        success: false,
        message: "Username must be 3-30 characters long and contain only letters, numbers, and underscores.",
      });
    }

    // Check if username is already taken
    const [usernameRows] = await pool.query(
      "SELECT id FROM users WHERE username = ?",
      [username]
    );

    if (usernameRows.length > 0) {
      return res.status(400).json({
        success: false,
        message: "Username is already taken. Please choose another.",
      });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Create user account
    const insertQuery = `
      INSERT INTO users (name, username, email, password, is_email_verified, meta, secret, role, isActive)
      VALUES (?, ?, ?, ?, 1, JSON_OBJECT('TFARequire', false), JSON_OBJECT(), 'USER', 1)
    `;

    const [result] = await pool.query(insertQuery, [
      name,
      username,
      email,
      hashedPassword,
    ]);

    const userId = result.insertId;

    // Assign default USER role
    const [roleRows] = await pool.query("SELECT id FROM roles WHERE name = 'USER'");
    if (roleRows.length > 0) {
      await pool.query(
        "INSERT INTO user_roles (user_id, role_id) VALUES (?, ?)",
        [userId, roleRows[0].id]
      );
    }

    // Auto-login: Generate JWT token
    const deviceType = req.headers['user-agent']?.includes('Mobile') ? 'android' : 'web';
    const tokenData = authUtils.generateJWTToken(
      { userId, username, email },
      deviceType
    );

    // Store token in database
    await pool.query(
      "INSERT INTO tokens (user_id, token, device_type, expires_at) VALUES (?, ?, ?, ?)",
      [userId, tokenData.token, deviceType, tokenData.expiryDate]
    );

    // Prepare user data response
    const userData = {
      id: userId,
      name,
      username,
      email,
      role: 'USER',
    };

    res.status(201).json({
      success: true,
      message: "Registration completed successfully! You are now logged in.",
      data: userData,
      token: tokenData.token,
    });
  } catch (error) {
    console.error("Error in completeRegistration:", error);
    res.status(500).json({
      success: false,
      message: "Internal Server Error",
      error: error.message,
    });
  }
};

// --------- Verify 2FA Email OTP
const verifyTFAEmail = async (req, res) => {
  try {
    const { tempToken, otp } = req.body;

    if (!tempToken || !otp) {
      return res.status(400).json({
        success: false,
        message: "Temporary token and OTP are required.",
      });
    }

    // Verify temporary token
    let decoded;
    try {
      decoded = jwt.verify(tempToken, SECRET_KEY);
      if (decoded.step !== 'tfa_pending') {
        throw new Error('Invalid token step');
      }
    } catch (error) {
      return res.status(401).json({
        success: false,
        message: "Invalid or expired temporary token.",
      });
    }

    // Get user data
    const [userRows] = await pool.query(
      "SELECT id, name, username, email FROM users WHERE id = ? AND is_deleted = 0",
      [decoded.userId]
    );

    if (userRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: "User not found.",
      });
    }

    const user = userRows[0];

    // Verify OTP
    const isValidOTP = await authUtils.verifyOTP(user.email, otp, 10);
    if (!isValidOTP) {
      return res.status(400).json({
        success: false,
        message: "Invalid or expired OTP.",
      });
    }

    // Delete OTP after successful verification
    await authUtils.deleteOTP(user.email);

    // Generate final JWT token
    const deviceType = req.headers['user-agent']?.includes('Mobile') ? 'android' : 'web';
    const tokenData = authUtils.generateJWTToken(
      { userId: user.id, username: user.username, email: user.email },
      deviceType
    );

    // Store token in database
    await pool.query(
      "INSERT INTO tokens (user_id, token, device_type, expires_at) VALUES (?, ?, ?, ?)",
      [user.id, tokenData.token, deviceType, tokenData.expiryDate]
    );

    res.status(200).json({
      success: true,
      message: "2FA verification successful. Login completed.",
      data: {
        id: user.id,
        name: user.name,
        username: user.username,
        email: user.email,
      },
      token: tokenData.token,
    });
  } catch (error) {
    console.error("Error in verifyTFAEmail:", error);
    res.status(500).json({
      success: false,
      message: "Internal Server Error",
      error: error.message,
    });
  }
};

// --------- Verify TOTP
const verifyTOTP = async (req, res) => {
  try {
    const { tempToken, totpCode } = req.body;

    if (!tempToken || !totpCode) {
      return res.status(400).json({
        success: false,
        message: "Temporary token and TOTP code are required.",
      });
    }

    // Verify temporary token
    let decoded;
    try {
      decoded = jwt.verify(tempToken, SECRET_KEY);
      if (decoded.step !== 'tfa_pending') {
        throw new Error('Invalid token step');
      }
    } catch (error) {
      return res.status(401).json({
        success: false,
        message: "Invalid or expired temporary token.",
      });
    }

    // Get user data with secret
    const [userRows] = await pool.query(
      "SELECT id, name, username, email, secret FROM users WHERE id = ? AND is_deleted = 0",
      [decoded.userId]
    );

    if (userRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: "User not found.",
      });
    }

    const user = userRows[0];
    const secret = typeof user.secret === 'string' ? JSON.parse(user.secret) : user.secret;

    if (!secret.totp) {
      return res.status(400).json({
        success: false,
        message: "TOTP is not set up for this account.",
      });
    }

    // Decrypt and verify TOTP
    const decryptedSecret = authUtils.decrypt(secret.totp);
    const isValidTOTP = authUtils.verifyTOTPToken(totpCode, decryptedSecret);

    if (!isValidTOTP) {
      return res.status(400).json({
        success: false,
        message: "Invalid TOTP code.",
      });
    }

    // Generate final JWT token
    const deviceType = req.headers['user-agent']?.includes('Mobile') ? 'android' : 'web';
    const tokenData = authUtils.generateJWTToken(
      { userId: user.id, username: user.username, email: user.email },
      deviceType
    );

    // Store token in database
    await pool.query(
      "INSERT INTO tokens (user_id, token, device_type, expires_at) VALUES (?, ?, ?, ?)",
      [user.id, tokenData.token, deviceType, tokenData.expiryDate]
    );

    res.status(200).json({
      success: true,
      message: "TOTP verification successful. Login completed.",
      data: {
        id: user.id,
        name: user.name,
        username: user.username,
        email: user.email,
      },
      token: tokenData.token,
    });
  } catch (error) {
    console.error("Error in verifyTOTP:", error);
    res.status(500).json({
      success: false,
      message: "Internal Server Error",
      error: error.message,
    });
  }
};

// ========================================
// LOGIN FLOW (WITH 2FA SUPPORT)
// ========================================

// ========================================
// FORGOT PASSWORD FLOW
// ========================================

// --------- Step 1: Request Password Reset
const forgotPassword = async (req, res) => {
  try {
    const { email } = req.body;

    // Validate email format
    if (!authUtils.isValidEmail(email)) {
      return res.status(400).json({
        success: false,
        message: "Please enter a valid email address.",
      });
    }

    // Check if user exists with the given email
    const [userRows] = await pool.query(
      "SELECT id, email, is_deleted, is_email_verified FROM users WHERE email = ?",
      [email]
    );

    // For security, always return success message even if email doesn't exist
    // This prevents email enumeration attacks
    if (userRows.length === 0) {
      return res.status(200).json({
        success: true,
        message: "If this email is registered, you will receive a password reset code.",
      });
    }

    const user = userRows[0];

    // Check if account is deleted
    if (user.is_deleted === 1) {
      return res.status(200).json({
        success: true,
        message: "If this email is registered, you will receive a password reset code.",
      });
    }

    // Check if email is verified
    if (!user.is_email_verified) {
      return res.status(400).json({
        success: false,
        message: "Email is not verified. Please verify your email first.",
      });
    }

    // Generate and store reset token
    const resetToken = await authUtils.generateUniqueOTP();
    await authUtils.storeOTP(email, resetToken);

    // Send reset token via email
    try {
      await emailService.sendPasswordResetOTP(email, resetToken);
    } catch (emailError) {
      console.error("Error sending password reset email:", emailError);
      // Continue with the response even if email fails
    }

    res.status(200).json({
      success: true,
      message: "Password reset code sent to your email. Please check your inbox.",
      // resetToken, // ⚠️ Remove in production
    });
  } catch (error) {
    console.error("Error in forgotPassword:", error);
    res.status(500).json({
      success: false,
      message: "Internal Server Error",
      error: error.message,
    });
  }
};

// --------- Step 2: Verify Password Reset Token
const verifyForgotPasswordOTP = async (req, res) => {
  try {
    const { email, token } = req.body;

    // Validate inputs
    if (!email || !token) {
      return res.status(400).json({
        success: false,
        message: "Email and reset token are required.",
      });
    }

    // Validate email format
    if (!authUtils.isValidEmail(email)) {
      return res.status(400).json({
        success: false,
        message: "Please enter a valid email address.",
      });
    }

    // Check if user exists
    const [userRows] = await pool.query(
      "SELECT id, email, is_deleted, is_email_verified FROM users WHERE email = ?",
      [email]
    );

    if (userRows.length === 0) {
      return res.status(400).json({
        success: false,
        message: "Invalid reset token.",
      });
    }

    const user = userRows[0];

    // Check if account is deleted or email not verified
    if (user.is_deleted === 1 || !user.is_email_verified) {
      return res.status(400).json({
        success: false,
        message: "Invalid reset token.",
      });
    }

    // Verify reset token
    const isValidToken = await authUtils.verifyOTP(email, token, 10); // 10 minutes expiry
    if (!isValidToken) {
      return res.status(400).json({
        success: false,
        message: "Invalid or expired reset token.",
      });
    }

    // Generate a temporary token for password reset
    const tempResetToken = jwt.sign(
      { userId: user.id, email: user.email, step: 'password_reset' },
      SECRET_KEY,
      { expiresIn: '15m' } // 15 minutes to complete password reset
    );

    res.status(200).json({
      success: true,
      message: "Reset token verified successfully. You can now set a new password.",
      tempResetToken,
    });
  } catch (error) {
    console.error("Error in verifyForgotPasswordOTP:", error);
    res.status(500).json({
      success: false,
      message: "Internal Server Error",
      error: error.message,
    });
  }
};

// --------- Step 3: Reset Password
const resetPassword = async (req, res) => {
  try {
    const { tempResetToken, newPassword, confirmPassword } = req.body;

    // Validate inputs
    if (!tempResetToken || !newPassword || !confirmPassword) {
      return res.status(400).json({
        success: false,
        message: "All fields are required.",
      });
    }

    // Validate password match
    if (newPassword !== confirmPassword) {
      return res.status(400).json({
        success: false,
        message: "Passwords do not match.",
      });
    }

    // Validate password strength
    const passwordValidation = authUtils.validatePassword(newPassword);
    if (!passwordValidation.isValid) {
      return res.status(400).json({
        success: false,
        message: "Password does not meet requirements.",
        errors: passwordValidation.errors,
      });
    }

    // Verify temporary reset token
    let decoded;
    try {
      decoded = jwt.verify(tempResetToken, SECRET_KEY);
      if (decoded.step !== 'password_reset') {
        throw new Error('Invalid token step');
      }
    } catch (error) {
      return res.status(401).json({
        success: false,
        message: "Invalid or expired reset token.",
      });
    }

    // Get user data
    const [userRows] = await pool.query(
      "SELECT id, email FROM users WHERE id = ? AND is_deleted = 0 AND is_email_verified = 1",
      [decoded.userId]
    );

    if (userRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: "User not found or account not verified.",
      });
    }

    const user = userRows[0];

    // Hash the new password
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

    // Update password in database
    await pool.query("UPDATE users SET password = ? WHERE id = ?", [
      hashedPassword,
      user.id,
    ]);

    // Delete any remaining reset tokens for this email
    await authUtils.deleteOTP(user.email);

    // Invalidate all existing sessions for security
    await pool.query("DELETE FROM tokens WHERE user_id = ?", [user.id]);

    res.status(200).json({
      success: true,
      message: "Password reset successfully! Please login with your new password.",
    });
  } catch (error) {
    console.error("Error in resetPassword:", error);
    res.status(500).json({
      success: false,
      message: "Internal Server Error",
      error: error.message,
    });
  }
};

// ========================================
// EMAIL ACTIVATION (For unverified accounts)
// ========================================

// --------- Resend Email Activation OTP
const activeMail = async (req, res) => {
  try {
    const { email } = req.body;

    // Validate email format
    if (!authUtils.isValidEmail(email)) {
      return res.status(400).json({
        success: false,
        message: "Please enter a valid email address.",
      });
    }

    // Check if user exists
    const [userRows] = await pool.query(
      "SELECT id, email, is_deleted, is_email_verified FROM users WHERE email = ?",
      [email]
    );

    if (userRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: "User with this email does not exist",
      });
    }

    const user = userRows[0];

    // Check if account is deleted
    if (user.is_deleted === 1) {
      return res.status(403).json({
        success: false,
        message: "This account has been deleted",
      });
    }

    // Check if already verified
    if (user.is_email_verified === 1) {
      return res.status(400).json({
        success: false,
        message: "Email is already verified",
      });
    }

    // Generate and store OTP
    const otp = await authUtils.generateUniqueOTP();
    await authUtils.storeOTP(email, otp);

    // Send OTP via email
    try {
      await emailService.sendEmailActivationOTP(email, otp);
    } catch (emailError) {
      console.error("Error sending email activation OTP:", emailError);
      // Continue with the response even if email fails
    }

    res.status(200).json({
      success: true,
      message: "Verification OTP sent to your email",
      // otp, // ⚠️ Remove in production
    });
  } catch (error) {
    console.error("Error in activeMail:", error);
    res.status(500).json({
      success: false,
      message: "Internal Server Error",
      error: error.message,
    });
  }
};

// --------- Verify Email Activation OTP
const verifyActiveMailOTP = async (req, res) => {
  try {
    const { email, otp } = req.body;

    // Validate inputs
    if (!email || !otp) {
      return res.status(400).json({
        success: false,
        message: "Email and OTP are required.",
      });
    }

    // Check if user exists
    const [userRows] = await pool.query(
      "SELECT id, email, is_deleted, is_email_verified FROM users WHERE email = ?",
      [email]
    );

    if (userRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: "User with this email does not exist",
      });
    }

    const user = userRows[0];

    // Check if account is deleted
    if (user.is_deleted) {
      return res.status(403).json({
        success: false,
        message: "This account has been deleted",
      });
    }

    // Check if already verified
    if (user.is_email_verified) {
      return res.status(400).json({
        success: false,
        message: "Email is already verified",
      });
    }

    // Verify OTP
    const isValidOTP = await authUtils.verifyOTP(email, otp, 10);
    if (!isValidOTP) {
      return res.status(400).json({
        success: false,
        message: "Invalid or expired OTP. Please try again.",
      });
    }

    // Update verification status and clear OTP
    await pool.query(
      "UPDATE users SET is_email_verified = 1 WHERE id = ?",
      [user.id]
    );

    // Delete OTP after successful verification
    await authUtils.deleteOTP(email);

    res.status(200).json({
      success: true,
      message: "Email verified successfully!",
    });
  } catch (error) {
    console.error("Error in verifyActiveMailOTP:", error);
    res.status(500).json({
      success: false,
      message: "Internal Server Error",
      error: error.message,
    });
  }
};

// ========================================
// 2FA SETUP AND MANAGEMENT
// ========================================

// --------- Enable/Disable 2FA
const toggleTFA = async (req, res) => {
  try {
    const { enable } = req.body; // true to enable, false to disable
    const userId = req.user.userId; // From auth middleware

    // Get current user data
    const [userRows] = await pool.query(
      "SELECT id, email, username, meta FROM users WHERE id = ? AND is_deleted = 0",
      [userId]
    );

    if (userRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: "User not found.",
      });
    }

    const user = userRows[0];
    const meta = typeof user.meta === 'string' ? JSON.parse(user.meta) : user.meta;

    // Update TFA requirement
    meta.TFARequire = enable === true;

    await pool.query(
      "UPDATE users SET meta = ? WHERE id = ?",
      [JSON.stringify(meta), userId]
    );

    res.status(200).json({
      success: true,
      message: enable ? "2FA enabled successfully" : "2FA disabled successfully",
      data: {
        tfaEnabled: meta.TFARequire,
      },
    });
  } catch (error) {
    console.error("Error in toggleTFA:", error);
    res.status(500).json({
      success: false,
      message: "Internal Server Error",
      error: error.message,
    });
  }
};

// --------- Setup TOTP (Generate QR Code)
const setupTOTP = async (req, res) => {
  try {
    const userId = req.user.userId;

    // Get user data
    const [userRows] = await pool.query(
      "SELECT id, email, username FROM users WHERE id = ? AND is_deleted = 0",
      [userId]
    );

    if (userRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: "User not found.",
      });
    }

    const user = userRows[0];

    // Generate TOTP secret and QR code
    const totpData = await authUtils.generateTOTPSecret(user.username, user.email);

    // Store the secret temporarily (not yet confirmed)
    const tempSecret = {
      secret: totpData.secret,
      confirmed: false,
    };

    res.status(200).json({
      success: true,
      message: "TOTP setup initiated. Please scan the QR code and verify.",
      data: {
        qrCode: totpData.qrCode,
        manualEntryKey: totpData.manualEntryKey,
        tempSecret: authUtils.encrypt(totpData.secret), // Send encrypted for verification
      },
    });
  } catch (error) {
    console.error("Error in setupTOTP:", error);
    res.status(500).json({
      success: false,
      message: "Internal Server Error",
      error: error.message,
    });
  }
};

// --------- Verify and Confirm TOTP Setup
const confirmTOTPSetup = async (req, res) => {
  try {
    const { tempSecret, totpCode } = req.body;
    const userId = req.user.userId;

    if (!tempSecret || !totpCode) {
      return res.status(400).json({
        success: false,
        message: "Temporary secret and TOTP code are required.",
      });
    }

    // Decrypt the temporary secret
    const decryptedSecret = authUtils.decrypt(tempSecret);

    // Verify the TOTP code
    const isValidTOTP = authUtils.verifyTOTPToken(totpCode, decryptedSecret);
    if (!isValidTOTP) {
      return res.status(400).json({
        success: false,
        message: "Invalid TOTP code. Please try again.",
      });
    }

    // Get user data
    const [userRows] = await pool.query(
      "SELECT id, email, secret FROM users WHERE id = ? AND is_deleted = 0",
      [userId]
    );

    if (userRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: "User not found.",
      });
    }

    const user = userRows[0];
    const secret = typeof user.secret === 'string' ? JSON.parse(user.secret) : user.secret;

    // Store the confirmed TOTP secret
    secret.totp = authUtils.encrypt(decryptedSecret);

    await pool.query(
      "UPDATE users SET secret = ? WHERE id = ?",
      [JSON.stringify(secret), userId]
    );

    // Send confirmation email
    try {
      await emailService.sendTOTPSetupConfirmation(user.email);
    } catch (emailError) {
      console.error("Error sending TOTP confirmation email:", emailError);
    }

    res.status(200).json({
      success: true,
      message: "TOTP setup completed successfully!",
    });
  } catch (error) {
    console.error("Error in confirmTOTPSetup:", error);
    res.status(500).json({
      success: false,
      message: "Internal Server Error",
      error: error.message,
    });
  }
};

// --------- Logout
const logout = async (req, res) => {
  try {
    const token = req.headers["authorization"]?.split(" ")[1];

    if (!token) {
      return res.status(400).json({
        success: false,
        message: "Authorization token is required",
      });
    }

    // 1. Remove token from DB
    const deleteQuery = "DELETE FROM tokens WHERE token = ?";
    await pool.query(deleteQuery, [token]);

    // 2. Respond with logout confirmation
    res.status(200).json({
      success: true,
      message: "Logged out successfully",
    });
  } catch (error) {
    // 3. Handle errors
    console.error("Error in logout:", error);
    res.status(500).json({
      success: false,
      message: "Internal Server Error",
      error: {
        code: error.code,
        sqlMessage: error.sqlMessage,
        sql: error.sql,
      },
    });
  }
};


// ========================================
// VALIDATION ENDPOINTS
// ========================================

// --------- Check if Email Exists
const checkEmailExists = async (req, res) => {
  try {
    const { email } = req.body;

    // Validate email format
    if (!authUtils.isValidEmail(email)) {
      return res.status(400).json({
        success: false,
        message: "Please enter a valid email address.",
      });
    }

    // Check if email exists
    const [userRows] = await pool.query(
      "SELECT id FROM users WHERE email = ? AND is_deleted = 0",
      [email]
    );

    res.status(200).json({
      success: true,
      exists: userRows.length > 0,
    });
  } catch (error) {
    console.error("Error in checkEmailExists:", error);
    res.status(500).json({
      success: false,
      message: "Internal Server Error",
      error: error.message,
    });
  }
};

// --------- Check if Username Exists
const checkUsernameExists = async (req, res) => {
  try {
    const { username } = req.body;

    // Validate username format
    if (!authUtils.isValidUsername(username)) {
      return res.status(400).json({
        success: false,
        message: "Username must be 3-30 characters long and contain only letters, numbers, and underscores.",
      });
    }

    // Check if username exists
    const [userRows] = await pool.query(
      "SELECT id FROM users WHERE username = ? AND is_deleted = 0",
      [username]
    );

    res.status(200).json({
      success: true,
      exists: userRows.length > 0,
    });
  } catch (error) {
    console.error("Error in checkUsernameExists:", error);
    res.status(500).json({
      success: false,
      message: "Internal Server Error",
      error: error.message,
    });
  }
};

module.exports = {
  // Registration Flow
  register,
  verifyRegisterOTP,
  completeRegistration,

  // Login Flow
  login,
  verifyTFAEmail,
  verifyTOTP,

  // Password Reset Flow
  forgotPassword,
  verifyForgotPasswordOTP,
  resetPassword,

  // Email Activation
  activeMail,
  verifyActiveMailOTP,

  // 2FA Management
  toggleTFA,
  setupTOTP,
  confirmTOTPSetup,

  // Validation
  checkEmailExists,
  checkUsernameExists,

  // Session Management
  logout,
};
