import { Observable } from 'rxjs';

export interface SSEMessage {
  type: string;
  payload: any;
}

export const observable = new Observable<MessageEvent<SSEMessage>>((o$) => {
  const eventSource = new EventSource('/interval-sse-observable');
  eventSource.addEventListener('message', (event: MessageEvent) => o$.next(event.data));
  eventSource.addEventListener('error', (event: MessageEvent) => o$.error(event));
  return () => eventSource.close();
});
