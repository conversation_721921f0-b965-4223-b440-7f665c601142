import { Injectable, inject } from '@angular/core';
import { SQLWebService } from '@lib/angular/sql.service';
import { SQL, isNil } from '@lib/common/fun';
import { type JDataConfig } from '@lib/common/j-data.interface';

// # Schema
/*
CREATE TABLE blocks (
    id INTEGER PRIMARY KEY AUTOINCREMENT, -- Unique identifier for each block
    name TEXT UNIQUE -- Name of the block
);

CREATE TABLE flat_models (
    id INTEGER PRIMARY KEY AUTOINCREMENT, -- Unique identifier for each flat model
    name TEXT UNIQUE -- Name of the flat model must be 'New Generation,Improved,DBSS,Simplified,Standard,Apartment,Model A-Maisonette,Maisonette,Model A,Premium Apartment,Adjoined flat,Type S1,Type S2,Model A2,Terrace,Improved-Maisonette,Premium Maisonette,Multi Generation,Premium Apartment Loft,2-room,3Gen'
);

CREATE TABLE flat_types (
    id INTEGER PRIMARY KEY AUTOINCREMENT, -- Unique identifier for each flat type
    name TEXT UNIQUE -- Name of the flat type must be '3 ROOM,2 ROOM,5 ROOM,4 ROOM,EXECUTIVE,1 ROOM,MULTI-GENERATION'
);

CREATE TABLE street_names (
    id INTEGER PRIMARY KEY AUTOINCREMENT, -- Unique identifier for each street name
    name TEXT UNIQUE -- Name of the street
);

CREATE TABLE towns (
    id INTEGER PRIMARY KEY AUTOINCREMENT, -- Unique identifier for each town
    name TEXT UNIQUE -- Name of the town must be in 'ANG MO KIO, BEDOK, BISHAN, BUKIT BATOK, BUKIT MERAH, BUKIT PANJANG, BUKIT TIMAH, CENTRAL AREA, CHOA CHU KANG, CLEMENTI, GEYLANG, HOUGANG, JURONG EAST, JURONG WEST, KALLANG/WHAMPOA, PASIR RIS, PUNGGOL, QUEENSTOWN, SEMBAWANG, SENGKANG, TAMPINES, MARINE PARADE, SERANGOON, TOA PAYOH, WOODLANDS, YISHUN'
);

CREATE TABLE sales (
    id INTEGER PRIMARY KEY AUTOINCREMENT, -- Unique identifier for each sale
    blockId INTEGER, -- Identifier for the block
    flatModelId INTEGER, -- Identifier for the flat model
    flatTypeId INTEGER, -- Identifier for the flat type
    area INTEGER, -- Floor area in square meters
    leaseYear INTEGER, -- Year the lease commenced
    resaleAt INTEGER, -- Resale timestamp epoch
    remain INTEGER, -- Remaining lease in months
    price INTEGER, -- Resale price
    startStorey INTEGER, -- Starting storey of the flat
    endStorey INTEGER, -- Ending storey of the flat
    streetId INTEGER, -- Identifier for the street
    townId INTEGER -- Identifier for the town
);
*/

const escapeSQLiteValue = (value: any) => {
  if (value === null || value === undefined) return 'NULL';
  if (typeof value === 'number') return value.toString();
  if (typeof value === 'boolean') return (value ? 1 : 0).toString();
  if (typeof value === 'string') return `'${value.replace(/'/g, "''")}'`;
  if (value instanceof Date) return `'${value.toISOString()}'`;
  if (Array.isArray(value)) return `'${JSON.stringify(value).replace(/'/g, "''")}'`;
  if (typeof value === 'object') return `'${JSON.stringify(value).replace(/'/g, "''")}'`;
  throw new Error('Unsupported data type');
};
@Injectable({ providedIn: 'root' })
export class SalesService {
  salesDb: any;
  readonly #sqlWebService = inject(SQLWebService);

  initialize(db: any) {
    const res = db.exec("PRAGMA index_list('sales')");
    if (res[0]?.values?.length) return;
    return console.log(
      db.exec(SQL`
CREATE INDEX IF NOT EXISTS idx_price ON sales(price);
ALTER TABLE sales ADD COLUMN pua REAL;
UPDATE sales SET pua = price / area WHERE area > 0;
CREATE INDEX IF NOT EXISTS idx_pua ON sales(pua);
      `),
    );
  }

  async loadDB() {
    const path = '/assets/hdb-sales.sqlite';
    this.salesDb ??= await this.#sqlWebService.loadDB(path);
    this.initialize(this.salesDb);
    this.#sqlWebService.saveDb(path, this.salesDb);
    return this.salesDb;
  }

  getAllTowns(db: any) {
    return db.exec('select * from towns')[0].values;
  }

  getAllBlocks(db: any) {
    return db.exec('select * from blocks')[0].values;
  }

  getAllStreets(db: any) {
    return db.exec('select * from street_names')[0].values;
  }
  getAllFlatModels(db: any) {
    return db.exec('select * from flat_models')[0].values;
  }
  getAllFlatTypes(db: any) {
    return db.exec('select * from flat_types')[0].values;
  }

  public getConfigs(): JDataConfig {
    return { page: 1, limit: 50, sort: 'resaleAt', order: 'DESC' };
  }

  generateSqlQuery(db: any, filters, configs) {
    let query = /* SQL */ `SELECT
      sales.id as id,
      towns.name AS town_name,
      -- sales.townId as town_id,
      street_names.name AS street_name,
      -- sales.streetId as street_id,
      blocks.name AS block_name,
      -- sales.blockId as block_id,
      flat_types.name AS flat_type_name,
      -- sales.flatTypeId as flat_type_id,
      flat_models.name AS flat_model_name,
      -- sales.flatModelId as flat_model_id,
      sales.area,
      sales.leaseYear as lease_year,
      strftime('%m-%Y', sales.resaleAt, 'unixepoch') AS resale_at,
      sales.remain/12 AS year_remain,
      sales.price AS price,
      sales.startStorey AS start,
      sales.endStorey AS end
    FROM sales
    JOIN blocks ON sales.blockId = blocks.id
    JOIN flat_models ON sales.flatModelId = flat_models.id
    JOIN flat_types ON sales.flatTypeId = flat_types.id
    JOIN street_names ON sales.streetId = street_names.id
    JOIN towns ON sales.townId = towns.id
    WHERE ${configs.lastId ? `sales.id < ${escapeSQLiteValue(configs.lastId)}` : '1 = 1'}
    `;

    let condition = '';
    if (filters.townId?.length)
      condition += ` AND townId IN (${filters.townId.map(escapeSQLiteValue).join(', ')})`;

    if (filters.flatModelId?.length)
      condition += ` AND flatModelId IN (${filters.flatModelId.map(escapeSQLiteValue).join(', ')})`;

    if (filters.flatTypeId?.length)
      condition += ` AND flatTypeId IN (${filters.flatTypeId.map(escapeSQLiteValue).join(', ')})`;

    if (filters.timePeriod) {
      if (filters.timePeriod.from)
        condition += ` AND resaleAt >= ${escapeSQLiteValue(
          new Date(filters.timePeriod.from).getTime(),
        )}`;

      if (filters.timePeriod.to)
        condition += ` AND resaleAt <= ${escapeSQLiteValue(
          new Date(filters.timePeriod.to).getTime(),
        )}`;
    }

    if (!isNil(filters.minimumPrice))
      condition += ` AND price >= ${escapeSQLiteValue(filters.minimumPrice)}`;

    if (!isNil(filters.maximumPrice))
      condition += ` AND price <= ${escapeSQLiteValue(filters.maximumPrice)}`;

    if (!isNil(filters.minimumLeaseYear))
      condition += ` AND remain >= ${escapeSQLiteValue(filters.minimumLeaseYear * 12)}`;

    if (!isNil(filters.maximumLeaseYear))
      condition += ` AND remain <= ${escapeSQLiteValue(filters.maximumLeaseYear * 12)}`;

    if (!isNil(filters.minimumStorey))
      condition += ` AND startStorey >= ${escapeSQLiteValue(filters.minimumStorey)}`;

    if (!isNil(filters.maximumStorey))
      condition += ` AND endStorey <= ${escapeSQLiteValue(filters.maximumStorey)}`;

    if (!isNil(filters.area)) condition += ` AND area = ${escapeSQLiteValue(filters.area)}`;

    // const total = 281023;
    const total = db.exec(`SELECT COUNT(*) FROM sales WHERE 1=1 ${condition}`)[0].values[0][0];

    query += ` ${condition}`;
    query += ` ORDER BY ${configs.sort} ${configs.order}`;
    query += ` LIMIT ${configs.limit}`;
    // query += ` LIMIT ${configs.limit} OFFSET ${(configs.page - 1) * configs.limit}`;
    console.time('query time');
    const res = db.exec(query);
    console.timeEnd('query time');
    return {
      headers: res[0]?.columns ?? [],
      data: res[0]?.values ?? [],
      meta: {
        total,
        start: (configs.page - 1) * configs.limit,
        end: Math.min(total, configs.page * configs.limit),
      },
    };
  }
}
