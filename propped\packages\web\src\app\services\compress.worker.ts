/// <reference lib="webworker" />

class BrotliCompressWorker {
  #brotliModule;
  #ref: Promise<any>;

  constructor(_thisWorker: typeof globalThis) {
    const importProxy = new Function('return import(arguments[0])');
    this.#brotliModule = importProxy(
      // 'https://esm.sh/brotli-wasm@3.0.1/es2022/brotli-wasm.mjs',
      'https://unpkg.com/brotli-wasm@3.0.1/index.web.js?module',
    );
    this.setup();
  }

  #getBrotli(): Promise<any> {
    if (this.#ref) return this.#ref;
    const brotli = this.#brotliModule.then((m: { default: any }) => m.default);
    this.#ref = brotli;
    return this.#ref;
  }

  public async compress(data: Uint8Array, opt: { quality: number } = {} as any) {
    opt.quality = opt.quality ?? 5;
    const brotli = await this.#getBrotli();
    return brotli.compress(data, opt);
  }

  public async decompress(data: Uint8Array) {
    const brotli = await this.#getBrotli();
    return brotli.decompress(data);
  }

  private setup() {
    const routes = {
      compress: (e: any) => this.compress(e.data.buffer, e.data.opt),
      decompress: (e: any) => this.decompress(e.data.buffer),
    };
    self.addEventListener('message', async (e: any) => {
      if (routes[e.data.command]) {
        const key = e.data.command as keyof typeof routes;
        const data = await routes[key](e);
        self.postMessage({ id: e.data.id, buffer: data }, [data.buffer]);
      }
    });
  }
}
new BrotliCompressWorker(this);
