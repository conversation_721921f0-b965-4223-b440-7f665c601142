import { Injectable, NgZone, inject } from '@angular/core';
import { wait } from '../common/fun';
import { EnsureSingleInstance } from '../common/test-root-service';

export class FilePickerKlass {
  #ref!: HTMLInputElement;
  lastPromise = Promise.withResolvers<boolean>();

  #getFileInput(): HTMLInputElement {
    if (this.#ref) return this.#ref;
    const input = document.createElement('input');
    input.type = 'file';
    this.#ref = input;
    return this.#ref;
  }

  public async pick(opt: { multiple?: boolean; accept?: string } = {}): Promise<File[]> {
    this.lastPromise.resolve(true);
    this.lastPromise = Promise.withResolvers();
    const input = this.#getFileInput();
    input.multiple = !!opt.multiple;
    input.accept = opt.accept ?? '*/*';
    const onChange = new Promise((res) => input.addEventListener('change', res, { once: true }));
    const onWindowFocusP = new Promise((res) =>
      window.addEventListener('focus', res, { once: true }),
    );
    input.click();
    await Promise.race([this.lastPromise.promise, onChange, onWindowFocusP]);
    await wait(400);
    const files = Array.from(input.files ?? []);
    input.value = '';
    return files;
  }
}

export const FilePicker = new FilePickerKlass();

@Injectable({ providedIn: 'root' })
export class FilePickerService {
  readonly #ngZone = inject(NgZone);

  constructor() {
    EnsureSingleInstance(FilePickerService);
  }

  pick(opt: { multiple?: boolean; accept?: string } = {}): Promise<File[]> {
    return this.#ngZone.runOutsideAngular(() => FilePicker.pick(opt));
  }
}
