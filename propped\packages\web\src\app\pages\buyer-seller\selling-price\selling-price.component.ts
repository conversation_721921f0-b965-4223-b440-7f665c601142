import { CommonModule } from '@angular/common';
import { Component, type OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { RouterModule } from '@angular/router';

interface ReferenceData {
  highestPSF: number;
  recentPSF: number;
  highestPSFNearby: number;
  recentPSFNearby: number;
  hdbIndexPurchase: number;
  hdbIndexSelling: number;
}

@Component({
  selector: 'app-selling-price',
  standalone: true,
  imports: [CommonModule, FormsModule, MatIconModule, RouterModule],
  templateUrl: './selling-price.component.html',
})
export class SellingPriceComponent implements OnInit {
  // Manual selling price
  manualSellingPrice = '742,500';

  // PSF Method
  unitSize = '990';
  targetPSF = '750';
  targetSellingPrice = '742,500';
  showPSFReference = false;

  // Capital Gain Method
  purchasePrice = '455,000';
  purchaseDate = 'Jan 2017';
  capitalGainSellingPrice = '643,500';
  sellingDate = 'Oct 2024';
  profit = '188,500';
  holdingPeriod = '7 yrs 9 mths';
  capitalGainPercentage = '4.7';
  showCapitalGainReference = false;

  // Chart Modal
  showChartModal = false;

  // Reference data
  referenceData: ReferenceData = {
    highestPSF: 883,
    recentPSF: 812,
    highestPSFNearby: 880,
    recentPSFNearby: 750,
    hdbIndexPurchase: 133.9,
    hdbIndexSelling: 200.9,
  };

  ngOnInit(): void {
    // Initialize calculations
    this.calculateFromPSF();
    this.calculateCapitalGain();
  }

  // Format currency inputs
  formatCurrency(field: string): void {
    // Remove non-numeric characters
    if (field === 'manual') {
      this.manualSellingPrice = this.manualSellingPrice.replace(/[^0-9]/g, '');
      // Format with commas
      this.manualSellingPrice = this.formatNumberWithCommas(this.manualSellingPrice);
    }
  }

  // Calculate selling price from PSF
  calculateFromPSF(): void {
    const psf = Number(this.targetPSF.replace(/[^0-9.]/g, ''));
    const size = Number(this.unitSize.replace(/[^0-9.]/g, ''));

    if (!isNaN(psf) && !isNaN(size)) {
      const price = Math.round(psf * size);
      this.targetSellingPrice = this.formatNumberWithCommas(price.toString());
    }
  }

  // Calculate PSF from selling price
  calculateFromSellingPrice(): void {
    const price = Number(this.targetSellingPrice.replace(/[^0-9.]/g, ''));
    const size = Number(this.unitSize.replace(/[^0-9.]/g, ''));

    if (!isNaN(price) && !isNaN(size) && size > 0) {
      const psf = Math.round(price / size);
      this.targetPSF = psf.toString();
    }
  }

  // Calculate when unit size changes
  calculateFromUnitSize(): void {
    this.calculateFromPSF();
  }

  // Calculate capital gain
  calculateCapitalGain(): void {
    const purchase = Number(this.purchasePrice.replace(/[^0-9.]/g, ''));
    const selling = Number(this.capitalGainSellingPrice.replace(/[^0-9.]/g, ''));

    if (!isNaN(purchase) && !isNaN(selling)) {
      const profitValue = selling - purchase;
      this.profit = this.formatNumberWithCommas(profitValue.toString());

      // Calculate annualized gain (simplified)
      const years = 7.75; // 7 years and 9 months
      const annualizedGain = ((selling / purchase) ** (1 / years) - 1) * 100;
      this.capitalGainPercentage = annualizedGain.toFixed(1);
    }
  }

  // Toggle reference data sections
  togglePSFReference(): void {
    this.showPSFReference = !this.showPSFReference;
  }

  toggleCapitalGainReference(): void {
    this.showCapitalGainReference = !this.showCapitalGainReference;
  }

  // Show HDB Index Chart Modal
  showHdbIndexChart(): void {
    this.showChartModal = true;
  }

  // Close HDB Index Chart Modal
  closeHdbIndexChart(): void {
    this.showChartModal = false;
  }

  // Helper function to format numbers with commas
  private formatNumberWithCommas(value: string): string {
    return value.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }
}
